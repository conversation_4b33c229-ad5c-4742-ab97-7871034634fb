import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  Linking,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { TextInput, FlatList } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import Switch from 'react-native-switch-pro';
import {
  isTablet, listenToCurrOutletIdReservationChanges
} from '../util/common';
import { UserStore } from '../store/userStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { parseValidPriceText } from '../util/common';
import CheckBox from 'react-native-check-box';
import { color } from 'react-native-reanimated';
import Close from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import moment, { isDate } from 'moment';
import Barcode from 'react-native-barcode-builder';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { EXPAND_TAB_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal from '../util/apiLocalReplacers';
import { qrUrl } from '../constant/env';
import Entypo from 'react-native-vector-icons/Entypo';
import firestore from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";

const EasyParcelScreen = (props) => {
  const { navigation } = props;

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  // const [merchantId, setMerchantId] = useState([]);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const [isMounted, setIsMounted] = useState(true);
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  //////////

  //Added 2024-04-17

  const [integrationId, setIntegrationId] = useState('');
  const [epStateFrom, setEpStateFrom] = useState('sgr');
  const [epNameFrom, setEpNameFrom] = useState('');
  const [epAddr1From, setEpAddr1From] = useState('');
  const [epCityFrom, setEpCityFrom] = useState('');
  const [epCodeFrom, setEpCodeFrom] = useState('');

  const dummylist = [
    {
      name: 'sample1',
      status: true,
    },
    {
      name: 'sample2',
      status: true,
    },
    {
      name: 'sample3',
      status: true,
    },
  ]

  ////////////////////
  ///////////////////
  ///////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('EasyParcelScreen');

            CommonStore.update((s) => {
              s.currPage = 'EasyParcelScreen';
              s.currPageStack = [...currPageStack, 'EasyParcelScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
            });
          }
        }}
        style={{
          width: windowWidth * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            marginRight: Platform.OS === 'ios' ? "27%" : 0,
            bottom: switchMerchant ? '2%' : 0,
            width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
          },
          windowWidth >= 768 && switchMerchant
            ? { right: windowWidth * 0.1 }
            : {},
          windowWidth <= 768
            ? { right: 20 }
            : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Easy Parcel
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      setEpStateFrom(currOutlet.epStateFrom ? currOutlet.epStateFrom : 'sgr');
      setEpNameFrom(currOutlet.epNameFrom ? currOutlet.epNameFrom : '');
      setEpAddr1From(currOutlet.epAddr1From ? currOutlet.epAddr1From : '');
      setEpCityFrom(currOutlet.epCityFrom ? currOutlet.epCityFrom : '');
      setEpCodeFrom(currOutlet.epCodeFrom ? currOutlet.epCodeFrom : '');
    }
  }, [currOutlet]);

  const updateEpDetails = async () => {
    console.log(epStateFrom);
    console.log(epNameFrom);
    console.log(epAddr1From);
    console.log(epCityFrom);
    console.log(epCodeFrom);

    if (epStateFrom && epNameFrom && epAddr1From && epCityFrom && epCodeFrom) {
      // valid
    }
    else {
      Alert.alert('Info', 'Please fill in all the fields to proceed.');

      return;
    }

    var body = {
      outletId: currOutlet.uniqueId,

      epStateFrom,
      epNameFrom,
      epAddr1From,
      epCityFrom,
      epCodeFrom,
    };

    // console.log('body', body);

    APILocal.updateEpDetails({ body }).then((result) => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success',
          'All details has been saved.',
          [
            {
              text: 'OK',
              onPress: () => {

              },
            },
          ],
          { cancelable: false },
        );
      }
    });
  };

  const renderRates = ({ item, index }) => {

    return (
      <View
        style={{
          paddingVertical: 5,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,
        }}>
        <View
          style={[
            { elevation: 1, borderRadius: 0, backgroundColor: 'white' },
          ]}>
          <View
            style={[
              {
                width: '100%',
                flexDirection: 'row',
                height: windowHeight * 0.1,
                alignItems: 'center',
                borderBottomColor: Colors.fieldtT,
              },
            ]}>
            <View
              style={[
                {
                  width: '8%',
                  marginLeft: 10,
                  alignItems: 'flex-start',
                },
              ]}>
              {
                item.image
                  ?
                  <AsyncImage
                    style={[
                      {
                        width: 50,
                        height: 50,
                        marginLeft: 0,
                      },
                      switchMerchant
                        ? {
                          width: windowWidth * 0.03,
                          height: windowHeight * 0.05,
                        }
                        : {},
                    ]}
                    resizeMode="contain"
                    // source={require('../assets/image/dineinGrey.png')}
                    source={{
                      uri: item.image
                    }}
                    item={item}
                  />
                  :
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 50,
                      height: 50,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      borderRadius: 5,
                    }}>
                    <Ionicons
                      name="fast-food-outline"
                      size={switchMerchant ? 15 : 25}
                    />
                  </View>
              }

            </View>

            <View
              style={[
                {
                  width: '70%',
                  flexDirection: 'row',
                  alignItems: 'flex-start',
                },
              ]}>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'left',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {item.name}
                </Text>
              </View>
            </View>

            <View
              style={[
                {
                  width: '25%',
                  textAlign: 'left',
                },
              ]}>
              <Switch
                onSyncPress={(value) => { }}
                width={42}
                circleColorActive={Colors.primaryColor}
                circleColorInactive={Colors.fieldtTxtColor}
                backgroundActive="#dddddd"
                value={item.status}
              />
            </View>
          </View>
        </View>
      </View>
    );
  };

  // function end

  return (

    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={10}
            expandSettings
          />
        </View> */}

        <ScrollView
          showsVerticalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{ backgroundColor: Colors.highlightColor, }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <View style={{
            padding: 20,
            backgroundColor: 'white',
            width: windowWidth * 0.85,
            borderRadius: 5,
            alignSelf: 'center',
            marginTop: 20,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            // elevation: 1,
            elevation: 3,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Image
                style={{
                  width: windowHeight * 0.15,
                  height: windowHeight * 0.15,
                  marginRight: 10,
                }}
                source={require('../assets/image/easyParcelLogo.png')}
              />

              <View>
                <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                  Easy Parcel Malaysia
                </Text>
                <Text style={{ fontSize: 16, marginTop: 5 }}>
                  Providing reliable and quick deliveries for our customers
                </Text>
              </View>

              <View style={{ width: windowWidth * 0.41 }}>
                <TouchableOpacity
                  style={{
                    alignSelf: 'flex-end',
                    justifyContent: 'center',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    width: 120,
                    paddingHorizontal: 10,
                    height: 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                  }}
                  onPress={() => {
                    updateEpDetails();
                  }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    SAVE
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* <View style={{marginTop: 15}}>
                    <Text style={{fontWeight: 'bold', fontSize: 18}}>
                        Setup
                    </Text>
                    <Text style={{fontSize: 16,marginTop: 5}}>
                        Integration ID
                    </Text>
                    <TextInput
                        placeholder={"e.g e4ghjkad132867AAKKKL"}
                        onChangeText={(text) => {
                            setIntegrationId(text);
                        }}
                        value={integrationId}
                        style={styles.textInput}
                    />
                </View> */}

            <View style={{ marginTop: 25 }}>
              <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                Company Information
              </Text>

              <Text style={{ fontSize: 16, marginTop: 5 }}>
                Location Name
              </Text>
              <TextInput
                onChangeText={(text) => {
                  setEpNameFrom(text);
                }}
                value={epNameFrom}
                style={styles.textInput}
              />

              <Text style={{ fontSize: 16, marginTop: 5 }}>
                Address
              </Text>
              <TextInput
                onChangeText={(text) => {
                  setEpAddr1From(text);
                }}
                value={epAddr1From}
                style={styles.textInput}
              />

              {/* <Text style={{fontSize: 16,marginTop: 5}}>
                        Address 2
                    </Text>
                    <TextInput
                        style={styles.textInput}
                    /> */}

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 5 }}>
                <View style={{ width: '47%' }}>
                  <Text style={{ fontSize: 16, marginTop: 5, }}>
                    City
                  </Text>
                  <TextInput
                    onChangeText={(text) => {
                      setEpCityFrom(text);
                    }}
                    value={epCityFrom}
                    style={styles.textInput}
                  />
                </View>

                <View style={{ width: '47%' }}>
                  <Text style={{ fontSize: 16, marginTop: 5 }}>
                    Zip Code
                  </Text>
                  <TextInput
                    onChangeText={(text) => {
                      setEpCodeFrom(text);
                    }}
                    value={epCodeFrom}
                    style={styles.textInput}
                  />
                </View>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 5 }}>
                <View style={{ width: '47%' }}>
                  <Text style={{ fontSize: 16, marginTop: 5, }}>
                    Country
                  </Text>
                  <TextInput
                    editable={false}
                    /* onChangeText={(text) => {

                    }}
                    value={}*/
                    defaultValue="Malaysia"
                    style={styles.textInput}
                  />
                </View>

                <View style={{ width: '47%' }}>
                  <Text style={{ fontSize: 16, marginTop: 5 }}>
                    Province
                  </Text>
                  <RNPickerSelect
                    placeholder={{
                      label: 'Select a province',
                      value: 'empty',
                    }}
                    items={[
                      {
                        label: 'Johor',
                        value: 'jhr',
                      },
                      {
                        label: 'Kedah',
                        value: 'kdh',
                      },
                      {
                        label: 'Kelantan',
                        value: 'ktn',
                      },
                      {
                        label: 'Melaka',
                        value: 'mlk',
                      },
                      {
                        label: 'Negeri Sembilan',
                        value: 'nsn',
                      },
                      {
                        label: 'Pahang',
                        value: 'phg',
                      },
                      {
                        label: 'Perak',
                        value: 'prk',
                      },
                      {
                        label: 'Perlis',
                        value: 'pls',
                      },
                      {
                        label: 'Pulau Pinang',
                        value: 'png',
                      },
                      {
                        label: 'Selangor',
                        value: 'sgr',
                      },
                      {
                        label: 'Terengganu',
                        value: 'trg',
                      },
                      {
                        label: 'Kuala Lumpur',
                        value: 'kul',
                      },
                      {
                        label: 'Putra Jaya',
                        value: 'pjy',
                      },
                      {
                        label: 'Sarawak',
                        value: 'srw',
                      },
                      {
                        label: 'Sabah',
                        value: 'sbh',
                      },
                      {
                        label: 'Labuan',
                        value: 'lbn',
                      },
                    ]}
                    style={{
                      inputIOS: styles.textInput,
                      inputAndroid: styles.textInput,
                      pickerContainer: {
                        height: 120,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                      },
                      viewContainer: {
                        zIndex: 1000,
                      },
                    }}
                    onValueChange={(item) =>
                      setEpStateFrom(item.value)
                    }
                    value={epStateFrom}
                  />
                </View>
              </View>
            </View>
          </View>
          {dummylist ?
            < View style={{
              padding: 20,
              backgroundColor: 'white',
              width: windowWidth * 0.85,
              borderRadius: 5,
              alignSelf: 'center',
              marginTop: 20,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              // elevation: 1,
              elevation: 3,
            }}
            >
              <View style={{ flexDirection: 'row' }}>
                <View style={{ width: '8%', marginLeft: 10, }}></View>
                <Text style={{ width: '70%', fontFamily: 'NunitoSans-Regular' }}>
                  Courier Name
                </Text>
                <Text style={{ width: '70%', fontFamily: 'NunitoSans-Regular' }}>
                  Status
                </Text>
              </View>

              <FlatList
                showsVerticalScrollIndicator={false}
                data={dummylist}
                renderItem={renderRates}
                keyExtractor={(item, index) => String(index)}
                contentContainerStyle={{
                  paddingBottom: 40,
                }}
              />
            </View>
            : null}
        </ScrollView>
      </View >
    </UserIdleWrapper >
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInputTakeawayCharges: {
    backgroundColor: Colors.fieldtBgColor,
    width: 200,
    height: 40,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
    //marginLeft: 190,
  },

  textInput: {
    //fontFamily: 'NunitoSans-Regular',
    //width: 300,
    marginTop: 10,
    color: 'black',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
    paddingLeft: 10,
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 60,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: '#E5E5E5',
    borderWidth: 1,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: Platform.OS == 'ios' ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10,
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center',
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  viewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0,
    width: '100%',
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
    marginBottom: 15,
    width: '100%',
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 7,
    width: '83%',
    alignSelf: 'flex-end',
  },
  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
});
export default EasyParcelScreen;
