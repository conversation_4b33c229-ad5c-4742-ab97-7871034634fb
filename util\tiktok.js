import ApiClient from './ApiClient';

const API_ROUTE = {
  GET_SHOP_HEALTH: 'tiktok/checkListingPrerequisites',
  GET_CATEGORIES: 'tiktok/getCategories',
  GET_WAREHOUSES: 'tiktok/getWarehouses',

  SYNC_PRODUCT: 'tiktok/syncOutletItem',
  DELETE_PRODUCT: 'tiktok/deleteOutletItem',
  TOGGLE_PRODUCT_ACTIVATION: 'tiktok/toggleProductActivation',
}

export const getTiktokShopHealth = async (outletId) => { 
  const response = await ApiClient.POST(API_ROUTE.GET_SHOP_HEALTH, {
    outletId,
  });

  return response;
}

export const getTiktokCategories = async (outletId) => {
  const response = await ApiClient.POST(API_ROUTE.GET_CATEGORIES, {
    outletId,
  });

  return response;
}

export const getTiktokShopWarehouses = async (outletId) => {
  const response = await ApiClient.POST(API_ROUTE.GET_WAREHOUSES, {
    outletId,
  });

  return response;
}

//////////////////////////////////////////////////

export const syncTiktokProduct = async (item_id) => {
  const response = await ApiClient.POST(API_ROUTE.SYNC_PRODUCT, {
    item_id,
  });

  return response;
}

export const deleteTiktokProduct = async (item_id) => {
  const response = await ApiClient.POST(API_ROUTE.DELETE_PRODUCT, {
    item_id,
  });

  return response;
}

export const toggleTiktokProductActivation = async (item_id, is_active) => {
  const response = await ApiClient.POST(API_ROUTE.TOGGLE_PRODUCT_ACTIVATION, {
    item_id,
    is_active,
  });

  return response;
}