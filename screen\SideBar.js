import { Text } from "react-native-fast-text";
import React, { Component, useState, useEffect, useRef } from 'react';
import {
  TouchableOpacity, TouchableHighlight, // View,
  StyleSheet, Image, Dimensions, Alert, Modal, useWindowDimensions
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView } from "react-native-gesture-handler";
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import Dashboard from '../assets/svg/Dashboard';
import DashboardG from '../assets/svg/Dashboard';
import CRM from '../assets/svg/CRM';
import Employees from '../assets/svg/Employees';
import Inventory from '../assets/svg/Inventory';
import Operation from '../assets/svg/Operation';
import Product from '../assets/svg/Product';
import Promotions from '../assets/svg/Promotions';
import Redemption from '../assets/svg/Redemption';
import RedemptionG from '../assets/svg/RedemptionG';
import Report from '../assets/svg/Report';
import Settings from '../assets/svg/Settings';
import Transactions from '../assets/svg/Transactions';
import CRMG from '../assets/svg/CRMG';
import GCoin from '../assets/svg/GCoin';
import Coins from '../assets/svg/Coins';
import EmployeesG from '../assets/svg/EmployeesG';
import InventoryG from '../assets/svg/InventoryG';
import OperationG from '../assets/svg/OperationG';
import ProductG from '../assets/svg/ProductG';
import PromotionsG from '../assets/svg/PromotionsG';
import ReportG from '../assets/svg/ReportG';
import SettingsG from '../assets/svg/SettingsG';
import TransactionsG from '../assets/svg/TransactionsG';
import Arrow from '../assets/svg/Arrow';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  isTablet, logToFile, performResize
} from '../util/common';
import { CommonStore } from '../store/commonStore';
import { color, onChange } from 'react-native-reanimated';
import { EXPAND_TAB_TYPE, MODE_ADD_CART, PRIVILEGES_NAME, ORDER_TYPE, ROLE_TYPE, OUTLET_SHIFT_STATUS, USER_ORDER_STATUS, RESERVATION_SCREEN_SECTION } from '../constant/common';
import { suppressDeprecationWarnings } from 'moment';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { NotificationStore } from '../store/notificationStore';
import { UserStore } from '../store/userStore';
import { PageStore } from '../store/pageStore';
// import IdleTimer from 'react-idle-timer'
import * as Token from '../util/Token';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import auth from '@react-native-firebase/auth';
import { logEventAnalytics } from '../util/common';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import { TableStore } from '../store/tableStore';
// import { storageMMKV } from '../util/storageMMKV';
import SideBarBadge from '../components/sideBarBadge';
import { useNavigation } from '@react-navigation/native';

const View = require(
  'react-native/Libraries/Components/View/ViewNativeComponent'
).default;

const SideBar = React.memo((props) => {
  // const { navigation } = props;

  let navigation = useNavigation();

  if (props.navigation) {
    navigation = props.navigation;
  }

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [expandOperation, setExpandOperation] = useState(
    props.expandOperation === undefined ? false : props.expandOperation,
  );
  const [expandProduct, setExpandProduct] = useState(
    props.expandProduct === undefined ? false : props.expandProduct,
  );
  const [expandInventory, setExpandInventory] = useState(
    props.expandInventory === undefined ? false : props.expandInventory,
  );
  const [expandSales, setExpandSales] = useState(
    props.expandSales === undefined ? false : props.expandSales,
  );
  const [expandPromotions, setExpandPromotions] = useState(
    props.expandPromotions === undefined ? false : props.expandPromotions,
  );
  const [expandCRM, setExpandCRM] = useState(
    props.expandCRM === undefined ? false : props.expandCRM,
  );
  const [expandLoyaltyPoints, setExpandLoyaltyPoints] = useState(
    props.expandLoyaltyPoints === undefined ? false : props.expandLoyaltyPoints,
  );
  const [expandVoucher, setExpandVoucher] = useState(
    props.expandVoucher === undefined ? false : props.expandVoucher,
  );
  const [expandTransactions, setExpandTransactions] = useState(
    props.expandTransactions === undefined ? false : props.expandTransactions,
  );
  const [expandReport, setExpandReport] = useState(
    props.expandReport === undefined ? false : props.expandReport,
  );
  const [expandEmployees, setExpandEmployees] = useState(
    props.expandEmployees === undefined ? false : props.expandEmployees,
  );
  const [expandSettings, setExpandSettings] = useState(
    props.expandSettings === undefined ? false : props.expandSettings,
  );
  const [expandRedemption, setExpandRedemption] = useState(
    props.expandRedemption === undefined ? false : props.expandRedemption,
  );
  const [selectedTab, setSelectedTab] = useState(
    props.selectedTab === undefined ? 0 : props.selectedTab,
  );
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  const myScroll = useRef(null);

  // const [currPage, setCurrPage] = useState('');

  // const clearDashboardDataFunc = CommonStore.useState((s) => s.clearDashboardDataFunc);

  const currPage = CommonStore.useState((s) => s.currPage);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const expandTab = CommonStore.useState((s) => s.expandTab);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);

  const isBetaUser = UserStore.useState((s) => s.isBetaUser);

  const merchantId = UserStore.useState((s) => s.merchantId);
  const role = UserStore.useState((s) => s.role);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const privileges_state = UserStore.useState((s) => s.privileges);
  const screensToBlock = UserStore.useState((s) => s.screensToBlock);
  const pinNo = UserStore.useState(s => s.pinNo);
  // const userOrders = OutletStore.useState((s) => s.userOrders);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutletShiftStatus = OutletStore.useState((s) => s.currOutletShiftStatus);

  // const payoutTransactions = OutletStore.useState((s) => s.payoutTransactions);

  const merchantName = MerchantStore.useState((s) => s.name);

  const sideBarScrollX = CommonStore.useState((s) => s.sideBarScrollX);
  const sideBarScrollY = CommonStore.useState((s) => s.sideBarScrollY);

  const [isMounted, setIsMounted] = useState(false);

  // const viewTableOrderModal = TableStore.useState(s => s.viewTableOrderModal);
  const isLeaveTablePaymentSummary = TableStore.useState(s => s.isLeaveTablePaymentSummary);

  // const initialCommonStore = CommonStore.useLocalCopyInitial();
  // const initialMerchantStore = MerchantStore.useLocalCopyInitial();
  // const initialOutletStore = OutletStore.useLocalCopyInitial();
  // const initialNotificationStore = NotificationStore.useLocalCopyInitial();
  // const initialUserStore = UserStore.useLocalCopyInitial();
  // const initialPageStore = PageStore.useLocalCopyInitial();

  // useEffect(() => {
  //     expandAction(selectedTab);
  // }, [selectedTab])

  // componentDidMount() {
  //     expandAction(selectedTab)

  //     // retrieveAsyncStorage();
  // }

  // async retrieveAsyncStorage() {
  //     const switchMerchant = await AsyncStorage.getItem('switchMerchant');

  //     if (switchMerchant === '1') {
  //         setState({
  //             switchMerchant: true,
  //         });
  //     }
  // }

  const [privileges, setPrivileges] = useState([]);

  const logOutButton = async () => {
    return Alert.alert(`Logout`, `Are you sure you want to logout?`, [
      {
        text: 'YES',
        // onPress={async () => {
        //     await AsyncStorage.clear();
        //     User.setlogin(false);
        //     User.getRefreshMainScreen();
        // }}

        onPress: async () => {
          // to support offline-mode
          // AsyncStorage.clear();

          UserStore.update((s) => {
            s.avatar = '';
            s.dob = null;
            s.email = '';
            s.gender = '';
            s.name = '';
            s.number = '';
            s.outletId = '';
            s.race = '';
            s.state = '';
            s.uniqueName = '';
            s.updatedAt = null;
            s.merchantId = '';
            s.role = '';
            s.refreshToken = '';
            s.firebaseUid = '';
            s.privileges = [];
            s.screensToBlock = [];
          });

          MerchantStore.update((s) => {
            s.allOutlets = [];
            s.allOutletsDict = {};
            s.currOutletId = '',
              s.currOutlet = {
                uniqueId: '',
                privileges: [],
              };
          });

          const merchantId = await AsyncStorage.getItem('merchantId');
          const currOutletId = await AsyncStorage.getItem('currOutletId');

          // clock out employee
          const bodyClockOut = {
            employeeId: firebaseUid,
            logoutTime: Date.now(),

            merchantId,
            outletId: currOutletId,
          };

          if (role === ROLE_TYPE.ADMIN || role === ROLE_TYPE.STORE_MANAGER) {
            // 2022-06-16 - No need first
            // ApiClient.POST(API.updateUserClockInOut, bodyClockOut).then(
            //   (result) => {
            //     console.log('updateUserClockOut', result);
            //   },
            // );
          }
          else {
            // if waiter, clock out manually outside
          }

          const tokenFcm = await AsyncStorage.getItem('tokenFcm');

          await AsyncStorage.multiRemove([
            'accessToken',
            'userData',
            'refreshToken',

            'merchantLogoUrl',

            // 'lastActivity',

            'email',
            'password',

            'printerList',

            'supportCodeData',

            // 'isPrintingKDAndOS',
          ]);

          global.signInAlready = false;

          const body = {
            role,
            merchantId,
            outletId: currOutlet.uniqueId,
            tokenFcm,
          };

          // Token.clear();
          // User.setlogin(false);
          // User.getRefreshMainScreen();

          logToFile('App.js | logOutButton');

          ApiClient.POST(API.logoutUser, body).then((result) => {
            User.setlogin(false);
            User.setMerchantId(null);
            User.setUserData(null);
            User.setUserId(null);
            User.setRefreshToken(null);
            User.setOutletId(null);
            User.getRefreshMainScreen();

            global.privileges = [];
            global.privileges_state = [];

            global.isOnLoginPage = true;
            global.isOnPinLoginPage = false;

            if (global.simulateTabletMode) {
              performResize(
                {
                  windowPhysicalPixels: {
                    height: 2160,
                    width: 1620,
                    scale: 2,
                  },
                },
                'iPad 9th Generation',
                false,
                false,
                true,
                global.windowWidthOriginal,
                global.windowHeightOriginal,
                global.fontScaleOriginal,
              );
            }

            //////////////////////////////////

            // 2023-01-29 - clear offline data after logout

            try {
              global.watermelonDBDatabase
                .write(async () => {
                  await global.watermelonDBDatabase.unsafeResetDatabase();
                });
            }
            catch (ex) {
              console.error(ex);
            }

            global.funcSwitchShowApp(false);

            //////////////////////////////////

            // CommonStore.replace(initialCommonStore);
            // MerchantStore.replace(initialMerchantStore);
            // OutletStore.replace(initialOutletStore);
            // NotificationStore.replace(initialNotificationStore);
            // UserStore.replace(initialUserStore);
            // PageStore.replace(initialPageStore);
          });

          // if (clearDashboardDataFunc) {
          //   clearDashboardDataFunc();
          // }
        },
      },

      {
        text: 'NO',
        onPress: () => { },
      },
    ]);
  };

  const signOutButton = async () => {
    return Alert.alert(`Sign Out`, `Are you sure you want to sign out?`, [
      {
        text: 'YES',
        // onPress={async () => {
        //     await AsyncStorage.clear();
        //     User.setlogin(false);
        //     User.getRefreshMainScreen();
        // }}

        onPress: async () => {
          // to support offline-mode
          // AsyncStorage.clear();

          UserStore.update((s) => {
            s.avatar = '';
            s.dob = null;
            s.email = '';
            s.gender = '';
            s.name = '';
            s.number = '';
            s.outletId = '';
            s.race = '';
            s.state = '';
            s.uniqueName = '';
            s.updatedAt = null;
            s.merchantId = '';
            s.role = '';
            s.refreshToken = '';
            s.firebaseUid = '';
            s.privileges = [];
            s.screensToBlock = [];
          });

          // const merchantId = await AsyncStorage.getItem('merchantId');
          // const currOutletId = await AsyncStorage.getItem('currOutletId');

          // clock out employee
          const bodyClockOut = {
            employeeId: firebaseUid,
            logoutTime: Date.now(),

            merchantId,
            outletId: currOutletId,
          };

          if (role === ROLE_TYPE.ADMIN || role === ROLE_TYPE.STORE_MANAGER) {
            // 2022-06-16 - No need first
            // ApiClient.POST(API.updateUserClockInOut, bodyClockOut).then(
            //   (result) => {
            //     console.log('updateUserClockOut', result);
            //   },
            // );
          }
          else {
            // if waiter, clock out manually outside
          }

          const tokenFcm = await AsyncStorage.getItem('tokenFcm');

          // 2023-05-17 - No need remove for sign out
          AsyncStorage.multiRemove([
            // 'accessToken',
            // 'userData',
            // 'refreshToken',

            'merchantLogoUrl',

            // 'lastActivity',

            // 'isPrintingKDAndOS',
          ]);

          global.signInAlready = false;

          const body = {
            role,
            merchantId,
            outletId: currOutlet.uniqueId,
            tokenFcm,
          };

          // Token.clear();
          // User.setlogin(false);
          // User.getRefreshMainScreen();

          logToFile('App.js | signOutButton');

          ApiClient.POST(API.logoutUser, body).then((result) => {

            // CommonStore.replace(initialCommonStore);
            // MerchantStore.replace(initialMerchantStore);
            // OutletStore.replace(initialOutletStore);
            // NotificationStore.replace(initialNotificationStore);
            // UserStore.replace(initialUserStore);
            // PageStore.replace(initialPageStore);
          });

          setTimeout(() => {
            User.setlogin(false);
            User.setMerchantId(null);
            User.setUserData(null);
            User.setUserId(null);
            User.setRefreshToken(null);
            User.setOutletId(null);
            User.getRefreshMainScreen();

            global.privileges = [];
            global.privileges_state = [];

            global.isOnLoginPage = false;
            global.isOnPinLoginPage = true;

            if (global.simulateTabletMode) {
              performResize(
                {
                  windowPhysicalPixels: {
                    height: 2160,
                    width: 1620,
                    scale: 2,
                  },
                },
                'iPad 9th Generation',
                false,
                false,
                true,
                global.windowWidthOriginal,
                global.windowHeightOriginal,
                global.fontScaleOriginal,
              );
            }

            global.funcSwitchShowApp(false);
          }, 100);

          // if (clearDashboardDataFunc) {
          //   clearDashboardDataFunc();
          // }
        },
      },

      {
        text: 'NO',
        onPress: () => { },
      },
    ]);
  };

  // const logOutButton = async () => {
  //     const [signoutTime, setSignoutTime] = useState(0);
  //         let logoutTimeout;

  //     const setTimeout = () => {
  //         logoutTimeout = setTimeout(logout, signoutTime);
  //     };

  //     const clearTimeout = () => {
  //         if (logoutTimeout) clearTimeout(logoutTimeout);
  //     };

  //     // useEffect(() => {
  //     //     setSignoutTime(20000);
  //     //     const events = [
  //     //         'load',
  //     //         'mousemove',
  //     //         'mousedown',
  //     //         'click',
  //     //         'scroll',
  //     //         'keypress'
  //     //     ];

  //     const setSignoutTime = 2000; //2 second

  //     const resetTimeout = () => {
  //         clearTimeout();
  //         setTimeout();
  //     };

  //     for (var i in events) {
  //         window.addEventListener(events[i], resetTimeout);
  //     }

  //     setTimeout();
  //     };

  //     if (signoutTime) {
  //         return (
  //             Alert.alert(
  //                 "Session Timeout",
  //                 "Timeout! You want to stay?",
  //                 [
  //                     {
  //                         text: 'Yes',
  //                         // onPress={async () => {
  //                         //     await AsyncStorage.clear();
  //                         //     User.setlogin(false);
  //                         //     User.getRefreshMainScreen();
  //                         // }}
  //                         onPress: () => {
  //                             User.setlogin(false);
  //                             User.getRefreshMainScreen();

  //                         },
  //                     },

  //                     {
  //                         text: 'No',
  //                         onPress: () => { },
  //                     }
  //                 ]
  //             )
  //         )
  //     }

  //             Alert.alert(
  //                 'Logout',
  //                 "Do you want to logout?",
  //                 [
  //                     {
  //                         text: 'Yes',
  //                         // onPress={async () => {
  //                         //     await AsyncStorage.clear();
  //                         //     User.setlogin(false);
  //                         //     User.getRefreshMainScreen();
  //                         // }}
  //                         onPress: () => {
  //                             User.setlogin(false);
  //                             User.getRefreshMainScreen();

  //                         },
  //                     },

  //                     {
  //                         text: 'No',
  //                         onPress: () => { },
  //                     }
  //                 ]
  //             )
  // }

  // Modal.setAppElement('#root')

  // const IdleTimerContainer = () => {
  //     const [isLoggedIn, setIsLoggedIn] = useState(true)
  //     const [modalIsOpen, setModalIsOpen] = useState(false)
  //     const  idleTimerRef = useRef(null)
  //     const sessionTimeoutRef = usseRef(null)

  //     const onIdle = () => {
  //         setModalIsOpen(true)
  //     }

  //     const stayActive = () => {
  //         setModalIsOpen(false)
  //         clearTimeout(sessionTimeoutRef.current)

  //     }

  //     const stayActive = () => {
  //         setModalIsOpen(false)
  //         clearTimeout(sessionTimeoutRef.current)

  //     }

  //     const logOut = () => {
  //         setModalIsOpen(false)
  //         setIsLoggedIn(false)
  //         clearTimeout(sessionTimeoutRef.current)

  //     }

  //     return (
  //     <div>
  //         {isLoggedIn ? <h2> Hello Vishawa </h2>:  <h2> Hello Guest</h2>}
  //         <ModalView isOpen = {modalIsOpen}>
  //             <h2> You've been idle for a while! </h2>
  //             <p> You will be logged out soon </p>
  //             <div>
  //                 <button onClick = {logOut}> Log me out </button>
  //                 <button onClick = {stayActive}> Keep me signed in </button>
  //             </div>
  //         </ModalView>
  //         <IdlerTimer ref = {idleTimerRef}
  //                     timeout = {5*1000}
  //                     onIdle = {onIdle}/>

  //     </div>
  //     )
  // }

  // useEffect(() => {
  //   // reset states
  //   CommonStore.update((s) => {
  //     // s.selectedProductEdit = null;
  //     // s.selectedOutletCategoryEdit = null;
  //     // s.selectedSupplierEdit = null;
  //     // s.selectedPurchaseOrderEdit = null;
  //     // s.selectedStockTransferEdit = null;
  //     // s.selectedStockTakeEdit = null;
  //     // s.selectedVoucherEdit = null;
  //     // s.selectedOutletEmployeeEdit = null;

  //     // s.selectedPromotionEdit = null;

  //     // s.selectedCustomerEdit = null;

  //     // experimental
  //     // s.modeAddCart = MODE_ADD_CART.NORMAL;
  //   });
  // }, [currPage]);

  // useEffect(() => {
  //   if (!isMounted) {
  //     setIsMounted(true);
  //     return;
  //   }

  //   const scrollPositions = {
  //     [EXPAND_TAB_TYPE.DASHBOARD]: 0,
  //     [EXPAND_TAB_TYPE.OPERATION]: windowHeight * 0.1,
  //     [EXPAND_TAB_TYPE.PRODUCT]: windowHeight * 0.15,
  //     [EXPAND_TAB_TYPE.INVENTORY]: windowHeight * 0.23,
  //     [EXPAND_TAB_TYPE.INVENTORY_COMPOSITE]: windowHeight * 0.32,
  //     [EXPAND_TAB_TYPE.DOCKET]: windowHeight * 0.42,
  //     [EXPAND_TAB_TYPE.VOUCHER]: windowHeight * 0.50,
  //     [EXPAND_TAB_TYPE.PROMOTION]: windowHeight * 0.58,
  //     [EXPAND_TAB_TYPE.CRM]: windowHeight * 0.66,
  //     [EXPAND_TAB_TYPE.LOYALTY]: windowHeight * 0.74,
  //     [EXPAND_TAB_TYPE.TRANSACTIONS]: 'end',
  //     [EXPAND_TAB_TYPE.REPORT]: windowHeight * 0.9,
  //     [EXPAND_TAB_TYPE.EMPLOYEES]: 'end',
  //     [EXPAND_TAB_TYPE.SETTINGS]: 'end',
  //   };

  //   const scrollToPosition = scrollPositions[expandTab];
  //   if (scrollToPosition === 'end') {
  //     myScroll?.current?.scrollToEnd();
  //   } else {
  //     myScroll?.current?.scrollTo({ y: scrollToPosition });
  //   }
  // }, [expandTab, isMounted]);

  useEffect(() => {
    const fetchPrivileges = async () => {
      const enteredPinNo = await global.watermelonDBDatabase.localStorage.get('enteredPinNo');
      if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
        setPrivileges([
          "EMPLOYEES", "OPERATION", "PRODUCT", "INVENTORY", "INVENTORY_COMPOSITE",
          "DOCKET", "VOUCHER", "PROMOTION", "CRM", "LOYALTY", "TRANSACTIONS",
          "REPORT", "RESERVATIONS", 'REFUND_ORDER', 'SETTINGS', 'QUEUE',
          'OPEN_CASH_DRAWER', 'KDS', 'UPSELLING', 'REJECT_ITEM', 'CANCEL_ORDER', 'MANUAL_DISCOUNT'
        ]);
      } else {
        setPrivileges(privileges_state || []);
      }
    };

    fetchPrivileges();
  }, [role, privileges_state, pinNo]);

  useEffect(() => {
    const screenY = global.sideBarScreenYDict[currPage] || 0;

    const skipScrollPages = [
      'MenuOrderingScreen', 'Kitchen', 'Table', 'Order',
      'Takeaway', 'OtherDelivery', 'Queue', 'History'
    ];

    if (!skipScrollPages.includes(currPage)) {
      setTimeout(() => {
        myScroll?.current?.scrollTo({
          x: sideBarScrollX,
          y: sideBarScrollY + screenY,
        });
      }, 50);
    }
  }, [currPage]);

  console.log('privileges');
  console.log(privileges);

  const layoutHandler = (event, screenName) => {
    const { x, y, width, height } = event.nativeEvent.layout;

    global.sideBarScreenYDict[screenName] = y;
  };

  // const scrollHandler = (screenName) => {
  //   if (global.sideBarScreenYDict[screenName]) {
  //     myScroll && myScroll.current && myScroll.current.scrollTo({
  //       x: 0,
  //       y: sideBarScrollY + global.sideBarScreenYDict[screenName],
  //     });
  //   }
  // };

  const expandAction = (selectedTabParam) => {
    console.log('selectedTabParam');
    console.log(selectedTabParam);

    // reset states
    CommonStore.update((s) => {
      s.selectedProductEdit = null;
      s.selectedOutletCategoryEdit = null;
      s.selectedSupplierEdit = null;
      s.selectedPurchaseOrderEdit = null;
      s.selectedStockTransferEdit = null;
      s.selectedStockTakeEdit = null;
      s.selectedVoucherEdit = null;
      s.selectedOutletEmployeeEdit = null;

      s.selectedPromotionEdit = null;

      s.selectedCustomerEdit = null;

      // experimental
      s.modeAddCart = MODE_ADD_CART.NORMAL;
    });

    TableStore.update(s => {
      s.customerName = '';
      s.customerPhone = '+60';
      s.customerUniqueId = '';
    });

    if (selectedTabParam == 0) {
      myScroll && myScroll.current && myScroll.current.scrollTo({ y: 0 });
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 1) {
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({ y: windowHeight * 0.1 });
      // setState({
      //     expandOperation: true,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(true);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 2) {
      // myScroll && myScroll.current && myScroll.current.scrollTo({ y: 173 })
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({ y: windowHeight * 0.15 });

      // setState({
      //     expandOperation: false,
      //     expandProduct: true,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(true);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 3) {
      // myScroll && myScroll.current && myScroll.current.scrollTo({ y: 255.5 })
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({ y: windowHeight * 0.2 });

      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: true,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(true);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 4) {
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({ y: windowHeight * 0.3 });
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: true,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(true);
      setExpandVoucher(false);
    } else if (selectedTabParam == 5) {
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({ y: windowHeight * 0.35 });
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: true,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(true);
    } else if (selectedTabParam == 6) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: true,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(true);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 7) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: true,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(true);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 8) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: true,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(true);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 9) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: true,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(true);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 10) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: true,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(true);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 11) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: true,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(true);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 12) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: true,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(true);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    }
  };

  var sidebarFontSizeTablet = 14;

  if (
    // windowWidth <= 1024
    true
  ) {
    sidebarFontSizeTablet = 12;
    styles.sidebarArrow = { paddingRight: 20 };

    if (isTablet()) {
      styles.expandedItems = {
        fontSize: 12,
        alignSelf: 'center',
        textAlign: 'center',
      };
    } else {
      styles.expandedItems = {
        fontSize: 12,
        alignSelf: 'center',
        textAlign: 'center',
      };
    }
    //remember chg back to fontsize 12
  }

  console.log(`sidebarFontSizeTablet: ${sidebarFontSizeTablet}`);

  // styles.expandedItems = {
  //   marginLeft: windowWidth * 0.01,
  //   width: windowWidth * 0.075,
  //   marginLeft: -windowWidth * 0.001,
  // };

  // styles.subBar = {
  //   height: windowHeight * 0.1,
  // };

  const sidebarTextStyleScale = [
    styles.sidebarTextStyle,
    !isTablet()
      ? {
        fontSize:
          windowWidth <= 720
            ? 9
            : switchMerchant
              ? 10
              : 12,
      }
      : {
        fontSize: sidebarFontSizeTablet,
      },
  ];

  const sideBarItemsContainerStyleScale = [
    // styles.sidebarItems,
    !isTablet()
      ? {
        // height: windowHeight * 0.1,
      }
      : undefined,
  ];

  const sideBarItemsStyleScale = [
    styles.sidebarItems,
    {
      width: windowWidth * Styles.sideBarWidth,
    },
    !isTablet()
      ? {
        // width: windowWidth * 0.08,
        height: windowHeight * 0.15,
        // backgroundColor: 'red'
      }
      : undefined,
  ];

  const sidebarIconStyleScale = [
    sidebarIconStyleScale,
    !isTablet()
      ? {
        // marginRight: 10,
      }
      : {
        // marginRight: 12,
      },
  ];

  const arrowSize = !isTablet() ? 17 : 12;

  const iconSize = !isTablet() ? 0.04 : 0.02;

  const highlightStyle = {
    // backgroundColor: '#405d27',
    // opacity: 0,
  };

  // Temporary quantity value (JJ's comment)
  // const takeawayQuantity = userOrders.filter((order) =>
  //   order.orderType === ORDER_TYPE.PICKUP &&
  //   (order.orderStatus !== USER_ORDER_STATUS.ORDER_AUTHORIZED &&
  //     order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARING &&
  //     order.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED &&
  //     order.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED));

  // const dineinQuantity = userOrders.filter((order) =>
  //   currOutlet.dineInRequiredAuthorization &&
  //   order.orderType === ORDER_TYPE.DINEIN &&
  //   order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED);

  return (
    <ScrollView
      style={[
        styles.scrollView,
        {
          // backgroundColor: 'red',
          width: windowWidth * Styles.sideBarWidth,
        },
        switchMerchant
          ? {
            // width: '10%'
          }
          : {},
      ]}
      // ref={(ref) => {
      //     myScroll = ref
      // }}
      ref={myScroll}
      onScroll={(event) => {
        //console.log(event.nativeEvent.contentOffset.y)
      }}
      contentContainerStyle={{
        paddingBottom: windowHeight * 0.1,
      }}
      onMomentumScrollEnd={(e) => {
        // scroll animation ended
        console.log(e.nativeEvent.contentOffset.x);
        console.log(e.nativeEvent.contentOffset.y);

        if (sideBarScrollX !== e.nativeEvent.contentOffset.x ||
          sideBarScrollY !== e.nativeEvent.contentOffset.y) {
          CommonStore.update(s => {
            s.sideBarScrollX = e.nativeEvent.contentOffset.x;
            s.sideBarScrollY = e.nativeEvent.contentOffset.y;
          });
        }
      }}
    >

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
        &&
        (privileges.includes(PRIVILEGES_NAME.OPERATION) || privileges.includes(PRIVILEGES_NAME.KDS))

      ) && <View style={sideBarItemsContainerStyleScale}>
          {/* <TouchableOpacity */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              requestAnimationFrame(() => {
                // setState({ expandOperation: !expandOperation, selectedTab: 1 });

                // setExpandOperation(!expandOperation);
                // setSelectedTab(1);
                // expandAction(1);

                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                  });
                } else {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.NONE;
                  });
                }

                logEventAnalytics({
                  eventName: ANALYTICS.MODULE_OPERATION,
                  eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION,
                });
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.OPERATION ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <OperationG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Operation
                    </Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                                        <Arrow width={arrowSize} height={arrowSize} />
                                    </View>
                                </View> */}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Operation
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={[sidebarTextStyleScale, {}]}>Operation</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <Arrow width={arrowSize} height={arrowSize} />
                                </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>
          {/* </TouchableOpacity> */}
          {expandTab == EXPAND_TAB_TYPE.OPERATION ? (
            <View
              style={[
                {
                  paddingVertical: 16,
                  paddingTop: 5,
                  backgroundColor:
                    expandTab == EXPAND_TAB_TYPE.OPERATION
                      ? Colors.lightPrimary
                      : null,
                  alignItems: 'center',
                },
              ]}>
              {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                  && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Ordering'))
                  ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'MenuOrderingScreen');
                    }}
                    onPress={() => {
                      requestAnimationFrame(() => {
                        navigation.navigate('MenuOrderingScreen');
                        // setSelectedTab(1), expandAction(1),
                        CommonStore.update((s) => {
                          s.currPage = 'MenuOrderingScreen';
                          s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];

                          if (s.cartItemsMo.length < 1) {
                            s.selectedOutletTableMo = {};
                          }

                          // 2025-06-07 - to address issue when place takeaway orders, the last selected table still listen to snapshot, and causing side effects: table auto unseated
                          s.selectedOutletTable = {};
                          s.selectedCustomerEdit = null;
                        });

                        TableStore.update(s => {
                          s.customerName = '';
                          s.customerPhone = '+60';
                          s.customerUniqueId = '';

                          s.orderDisplayIndividual = false;
                          s.orderDisplayProduct = false;
                          s.orderDisplaySummary = true;

                          s.viewTableOrderModal = false;
                          s.renderPaymentSummary = false;
                          s.renderReceipt = false;

                          s.displayQrModal = false;
                          s.displayQModal = false;
                          s.deleteTableModal = false;
                          s.updateTableModal = false;
                          s.joinTableModal = false;
                          s.moveOrderModal = false;
                          s.addSectionAreaModel = false;
                          s.addTableModal = false;
                          s.preventDeleteTableModal = false;
                          s.seatingModal = false;
                          s.showLoyaltyModal = false;
                          s.showAddLoyaltyModal = false;
                          s.cashbackModal = false;
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_OPERATION_ORDERING,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_ORDERING,
                        });
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'MenuOrderingScreen' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'MenuOrderingScreen'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 10 } : {},
                      ]}>
                      Ordering
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              }

              {/* {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                  && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Order_Dashboard'))
                  ?
                  <TouchableOpacity
                    onPress={() => {
                      requestAnimationFrame(() => {
                        navigation.navigate('OrderDashboard');
                        // setSelectedTab(1), expandAction(1),
                        CommonStore.update((s) => {
                          s.currPage = 'OrderDashboard';
                          s.currPageStack = [...currPageStack, 'OrderDashboard'];
                          s.selectedOrder = {};
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_OPERATION_ORDER_DASHBOARD,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_ORDER_DASHBOARD,
                        });
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'OrderDashboard' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'OrderDashboard'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 10 } : {},
                      ]}>
                      {"Order\nDashboard"}
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              } */}

              {/* {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                  &&
                  (privileges.includes(PRIVILEGES_NAME.OPERATION) || privileges.includes(PRIVILEGES_NAME.KDS))
                  && !screensToBlock.includes('Kitchen')
                )
                  ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'Kitchen');
                    }}
                    onPress={() => {
                      requestAnimationFrame(() => {
                        navigation.navigate('Kitchen');
                        // setSelectedTab(1);
                        // expandAction(1);
                        // setCurrPage('Kitchen');
                        CommonStore.update((s) => {
                          s.currPage = 'Kitchen';
                          s.currPageStack = [...currPageStack, 'Kitchen'];
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_OPERATION_KITCHEN,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_KITCHEN,
                        });
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'Kitchen' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage === 'Kitchen'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 10 } : {},
                      ]}>
                      Kitchen
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              } */}

              {/* {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                  && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Table'))
                  ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'Table');
                    }}
                    onPress={() => {
                      requestAnimationFrame(() => {
                        navigation.navigate('Table');
                        // setSelectedTab(1);
                        // expandAction(1);
                        // setCurrPage('Table');
                        CommonStore.update((s) => {
                          s.currPage = 'Table';
                          s.currPageStack = [...currPageStack, 'Table'];
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }

                        if (isLeaveTablePaymentSummary) {
                          CommonStore.update((s) => {
                            s.modeAddCart = MODE_ADD_CART.NORMAL;
                          });

                          global.selectedTaggableVoucher = {};

                          CommonStore.update(s => {
                            s.selectedTaggableVoucher = {};

                            s.selectedPromoCodePromotion = {};
                            // s.selectedOrderToPayUserId = global.selectedOrderToPayUserIdPrev;
                            // s.selectedOrderToPayUserIdVoucherIdRedemptionList = [];
                            // s.selectedOrderToPayUserIdVoucherIdValidList = [];
                            s.checkingOutTakeawayOrderList = [];
                            s.selectedCustomerEdit = null;
                          });

                          // global.viewTableOrderModalPrev = viewTableOrderModal;

                          TableStore.update(s => {
                            s.customerName = '';
                            s.customerPhone = '+60';
                            s.customerUniqueId = '';

                            s.renderPaymentSummary = false;
                            s.renderReceipt = false;

                            s.discountCartItemDict = {};
                            s.discountOrder = {
                              value: '0',
                              text: '0',
                              // isDiscountPercentage: isDiscountPercentage,
                            };
                            s.discountAccum = 0;
                            s.discountAccumOrder = 0;
                            s.useCashback = false;
                            s.cashbackAmount = '0.00';
                            s.discountPromotionsTotalLCC = 0;
                            s.currPendingOrder = {};
                            s.selectedOrderToPayList = [];
                            s.selectedOrdersCartItems = [];
                            s.useLoyaltyVoucher = false;
                            s.selectedTaggableVoucherId = '';
                            s.selectedPromoCodePromotionId = '';

                            // 2022-11-17 - changes
                            // s.viewTableOrderModal = true;

                            s.amount = '0';
                            s.isLeaveTablePaymentSummary = false;
                          });

                          setTimeout(() => {
                            typeof global.checkoutTakeawayFunc === 'function' && global.checkoutTakeawayFunc();
                          }, 100);
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_OPERATION_TABLE,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_TABLE,
                        });
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'Table' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage === 'Table'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 10 } : {},
                      ]}>
                      Table
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              } */}

              {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('CounterOrdering', {
                  params: {
                    outletData: currOutlet,
                    orderType: 0,
                    test: {},
                    navFrom: ORDER_TYPE.DINEIN,
                  },
                });
                // setSelectedTab(1);
                // expandAction(1);
                // setCurrPage('Table');
                CommonStore.update((s) => {
                  s.currPage = 'CounterOrdering';
                  s.currPageStack = [...currPageStack, 'CounterOrdering'];

                  s.orderType = ORDER_TYPE.DINEIN;

                  s.isCounterOrdering = true;
                });

                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'CounterOrdering' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage === 'CounterOrdering'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                {'Counter\nOrdering'}
              </Text>
            </TouchableOpacity> */}

              {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                  && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Takeaway'))
                  ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'Takeaway');
                    }}
                    onPress={() => {
                      requestAnimationFrame(() => {
                        navigation.navigate('Takeaway');
                        // setSelectedTab(1);
                        // expandAction(1);
                        CommonStore.update((s) => {
                          s.currPage = 'Takeaway';
                          s.currPageStack = [...currPageStack, 'Takeaway'];
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_OPERATION_DINEIN,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_DINEIN,
                        });
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'Takeaway' && highlightStyle),
                      },
                    ]}>
                    {/* Dine in quantity badge (JJ's comment) */}
                    <View style={styles.quantityContainer}>
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage === 'Takeaway'
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        Order
                      </Text>

                      {/* {(dineinQuantity.length > 0) ?
                        <View style={styles.badgeContainer}>
                          <Text style={styles.badgeText}>{dineinQuantity.length > 99 ? "99+" : dineinQuantity.length}</Text>
                        </View>
                        : <></>
                      } */}

                      <SideBarBadge orderType={ORDER_TYPE.PICKUP} />

                    </View>
                  </TouchableOpacity>
                  :
                  <></>
              }

              {/* {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                  && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Takeaway'))
                  ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'Takeaway');
                    }}
                    onPress={() => {
                      requestAnimationFrame(() => {
                        navigation.navigate('Takeaway');
                        // setSelectedTab(1), expandAction(1),
                        CommonStore.update((s) => {
                          s.currPage = 'Takeaway';
                          s.currPageStack = [...currPageStack, 'Takeaway'];
                          s.checkingOutTakeawayOrderList = [];
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_OPERATION_TAKEAWAY,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_TAKEAWAY,
                        });
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'Takeaway' && highlightStyle),
                      },
                    ]}>
                    <View style={styles.quantityContainer}>
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage === 'Takeaway'
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}
                      >
                        Takeaway
                      </Text>
                      <SideBarBadge orderType={ORDER_TYPE.PICKUP} />

                    </View>
                  </TouchableOpacity>
                  :
                  <></>
              }

              {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                  && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Other_D'))
                  ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'OtherDelivery');
                    }}
                    onPress={() => {
                      requestAnimationFrame(() => {
                        navigation.navigate('OtherDelivery');
                        // setSelectedTab(1), expandAction(1),
                        CommonStore.update((s) => {
                          s.currPage = 'OtherDelivery';
                          s.currPageStack = [...currPageStack, 'OtherDelivery'];
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_OPERATION_OTHER_DELIVERY,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_OTHER_DELIVERY,
                        });
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'OtherDelivery' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'OtherDelivery'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 10 } : {},
                      ]}>
                      Other D.
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              } */}


              {/* {isAlphaUser ? <TouchableOpacity
              onPress={() => {
                navigation.navigate('Reservation');
                // setSelectedTab(1), expandAction(1),
                CommonStore.update((s) => {
                  s.currPage = 'Reservation';
                  s.currPageStack = [...currPageStack, 'Reservation'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'Reservation' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'Reservation'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Reservation
              </Text>
            </TouchableOpacity> : <></> } */}

              {/* {((currOutlet && currOutlet.privileges &&
                currOutlet.privileges.includes(PRIVILEGES_NAME.QUEUE))
                && privileges.includes(PRIVILEGES_NAME.QUEUE) && !screensToBlock.includes('Queue')) ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Queue');
                  }}
                  onPress={() => {
                    requestAnimationFrame(() => {
                      navigation.navigate('Queue');
                      // setSelectedTab(1), expandAction(1),
                      CommonStore.update((s) => {
                        s.currPage = 'Queue';
                        s.currPageStack = [...currPageStack, 'Queue'];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_OPERATION_QUEUE,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_QUEUE,
                      });
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Queue' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Queue'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    Queue
                  </Text>
                </TouchableOpacity> : <></>} */}

              {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                  && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('History'))
                  ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'History');
                    }}
                    onPress={() => {
                      requestAnimationFrame(() => {
                        navigation.navigate('History');
                        // setSelectedTab(1), expandAction(1),
                        CommonStore.update((s) => {
                          s.currPage = 'History';
                          s.currPageStack = [...currPageStack, 'History'];
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_OPERATION_HISTORY,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_OPERATION_HISTORY,
                        });
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'History' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'History'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 10 } : {},
                      ]}>
                      History
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              }

            </View>
          ) : null}
        </View>}

      {/* //////////////////Reservations here/////////////////////// */}

      {
        (currOutlet && currOutlet.privileges &&
          currOutlet.privileges.includes(PRIVILEGES_NAME.RESERVATIONS))
          && privileges.includes(PRIVILEGES_NAME.RESERVATIONS) ?
          <View style={sideBarItemsContainerStyleScale}>
            <TouchableHighlight
              underlayColor={Colors.lightPrimary}
              onPress={() => {
                // setState({ expandEmployees: !expandEmployees, selectedTab: 9 }),
                // setExpandEmployees(!expandEmployees);
                // setSelectedTab(9);
                // expandAction(9)

                if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                  });
                } else {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.NONE;
                  });
                }

                logEventAnalytics({
                  eventName: ANALYTICS.MODULE_RESERVATION,
                  eventNameParsed: ANALYTICS_PARSED.MODULE_RESERVATION,
                });
              }}
              style={sideBarItemsStyleScale}>
              {expandTab == EXPAND_TAB_TYPE.RESERVATIONS ? (
                <View>
                  <View
                    style={[
                      styles.sidebarView,
                      {
                        backgroundColor: 'Colors.lightPrimary',
                        height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                          isTablet()
                            ? windowHeight * 0.08
                            : windowHeight * 0.16,
                        width: isTablet()
                          ? windowWidth * Styles.sideBarWidth
                          : '100%',
                      },
                      switchMerchant
                        ? {
                          // borderWidth: 1,
                          // width: '48%'
                        }
                        : {},
                    ]}>
                    <View style={sidebarIconStyleScale}>
                      <Inventory
                        width={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                        height={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                      />
                    </View>
                    <View style={styles.sidebarText}>
                      <Text
                        style={[
                          sidebarTextStyleScale,
                          { color: Colors.primaryColor },
                        ]}>
                        Appointments
                      </Text>
                    </View>
                  </View>
                </View>
              ) : (
                <View>
                  <View
                    style={[
                      styles.sidebarView,
                      {
                        backgroundColor: 'Colors.lightPrimary',
                        height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                          isTablet()
                            ? windowHeight * 0.08
                            : windowHeight * 0.16,
                        width: isTablet()
                          ? windowWidth * Styles.sideBarWidth
                          : '100%',
                      },
                      switchMerchant
                        ? {
                          // borderWidth: 1,
                          // width: '48%'
                        }
                        : {},
                    ]}>
                    <View style={sidebarIconStyleScale}>
                      <InventoryG
                        width={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                        height={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                      />
                    </View>
                    <View style={styles.sidebarText}>
                      <Text style={sidebarTextStyleScale}>Appointments</Text>
                    </View>
                  </View>
                </View>
              )}
            </TouchableHighlight>
            {expandTab === EXPAND_TAB_TYPE.RESERVATIONS ? (
              <View
                style={{
                  paddingVertical: 16,
                  paddingTop: 5,
                  backgroundColor:
                    expandTab == EXPAND_TAB_TYPE.RESERVATIONS
                      ? Colors.lightPrimary
                      : null,
                  alignItems: 'center',
                }}>
                {!screensToBlock.includes('Manage_Reservation') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'DetailsScreen');
                    }}
                    onPress={() => {
                      navigation.navigate('DetailsScreen');
                      // setSelectedTab(9), expandAction(9),
                      CommonStore.update((s) => {
                        s.currPage = 'DetailsScreen';
                        s.currPageStack = [...currPageStack, 'DetailsScreen'];

                        s.reservationScreenSection = RESERVATION_SCREEN_SECTION.MAIN;
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_RESERVATION_MANAGE,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_RESERVATION_MANAGE,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'DetailsScreen' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'DetailsScreen'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{`Manage`}</Text>
                  </TouchableOpacity>
                  : <></>
                }
                {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('FloorScreen');
                // setSelectedTab(9), expandAction(9),
                CommonStore.update((s) => {
                  s.currPage = 'FloorScreen';
                  s.currPageStack = [...currPageStack, 'FloorScreen'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'FloorScreen' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'FloorScreen'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>{`Floor`}</Text>
            </TouchableOpacity> */}
                {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('Guests');
                // setSelectedTab(9), expandAction(9),
                CommonStore.update((s) => {
                  s.currPage = 'Guests';
                  s.currPageStack = [...currPageStack, 'Guests'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'Guests' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'Guests'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>{`Guests`}</Text>
            </TouchableOpacity> */}
                {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('FeedbackScreen');
                // setSelectedTab(9), expandAction(9),
                CommonStore.update((s) => {
                  s.currPage = 'FeedbackScreen';
                  s.currPageStack = [...currPageStack, 'FeedbackScreen'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'Employee' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'Employee'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>{`Feedback`}</Text>
            </TouchableOpacity> */}
                {!screensToBlock.includes('Overview_Reservation') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'CalendarScreen');
                    }}
                    onPress={() => {
                      navigation.navigate('CalendarScreen');
                      // setSelectedTab(9), expandAction(9),
                      CommonStore.update((s) => {
                        s.currPage = 'CalendarScreen';
                        s.currPageStack = [...currPageStack, 'CalendarScreen'];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_RESERVATION_OVERVIEW,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_RESERVATION_OVERVIEW,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'CalendarScreen' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'CalendarScreen'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{`Overview`}</Text>
                  </TouchableOpacity>
                  : <></>}

                {/* {!screensToBlock.includes('Deposit_Setting') ?
                  <TouchableOpacity
                    onPress={() => {
                      navigation.navigate('SettingDeposit');
                      // setSelectedTab(9), expandAction(9),
                      CommonStore.update((s) => {
                        s.currPage = 'SettingDeposit';
                        s.currPageStack = [...currPageStack, 'SettingDeposit'];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_RESERVATION_DEPOSIT_SETTINGS,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_RESERVATION_DEPOSIT_SETTINGS,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'SettingDeposit' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'SettingDeposit'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{`Deposit Settings`}</Text>
                  </TouchableOpacity>
                  : <></>} */}

                {/* {!screensToBlock.includes('Interval_Setting') ?
                  <TouchableOpacity
                    onPress={() => {
                      navigation.navigate('SettingInterval');
                      // setSelectedTab(9), expandAction(9),
                      CommonStore.update((s) => {
                        s.currPage = 'SettingInterval';
                        s.currPageStack = [...currPageStack, 'SettingInterval'];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_RESERVATION_INTERVAL_SETTINGS,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_RESERVATION_INTERVAL_SETTINGS,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'SettingInterval' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'SettingInterval'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{`Interval Settings`}</Text>
                  </TouchableOpacity>
                  : <></>} */}
                {!screensToBlock.includes('Analytics') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'AnalyticReservationScreen');
                    }}
                    onPress={() => {
                      navigation.navigate('AnalyticReservationScreen');
                      // setSelectedTab(9), expandAction(9),
                      CommonStore.update((s) => {
                        s.currPage = 'AnalyticReservationScreen';
                        s.currPageStack = [
                          ...currPageStack,
                          'AnalyticReservationScreen',
                        ];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_RESERVATION_ANALYTICS,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_RESERVATION_ANALYTICS,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'AnalyticReservationScreen' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'AnalyticReservationScreen'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{`Analytics`}</Text>
                  </TouchableOpacity>
                  : <></>}
                {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('NewSettingsScreen');
                // setSelectedTab(9), expandAction(9),
                CommonStore.update((s) => {
                  s.currPage = 'NewSettingsScreen';
                  s.currPageStack = [...currPageStack, 'NewSettingsScreen'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'NewSettingsScreen' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'NewSettingsScreen'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>{`Settings`}</Text>
            </TouchableOpacity> */}
                {!screensToBlock.includes('Reservation_Setting') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'SettingReservation');
                    }}
                    onPress={() => {
                      navigation.navigate('SettingReservation');
                      CommonStore.update((s) => {
                        s.currPage = 'SettingReservation';
                        s.currPageStack = [...currPageStack, 'SettingReservation'];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }
                    }}
                    style={[
                      expandTab === EXPAND_TAB_TYPE.RESERVATIONS
                        ? Colors.lightPrimary
                        : null,
                      styles.subBar,
                      {
                        ...(currPage === 'SettingReservation' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'SettingReservation'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}>
                      Settings
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>}
              </View>
            ) : null}
          </View> : <></>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.PRODUCT))
        && privileges.includes(PRIVILEGES_NAME.PRODUCT)) && <View style={[sideBarItemsContainerStyleScale]}>
          {/* <TouchableOpacity */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({
              //     expandProduct: !expandProduct,
              //     selectedTab: 2
              // });
              // setExpandProduct(!expandProduct);
              // setSelectedTab(2);
              // expandAction(2)

              if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_PRODUCT,
                eventNameParsed: ANALYTICS_PARSED.MODULE_PRODUCT,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.PRODUCT ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <ProductG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Product Catalog
                    </Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                                        <Arrow width={arrowSize} height={arrowSize} />
                                    </View>
                                </View> */}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Product
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Product Catalog</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <Arrow width={arrowSize} height={arrowSize} />
                                </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>
          {/* </TouchableOpacity> */}
          {expandTab === EXPAND_TAB_TYPE.PRODUCT ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.PRODUCT
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('Product_Category') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ProductCategory');
                  }}
                  onPress={() => {
                    navigation.navigate('ProductCategory');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = 'ProductCategory';
                      s.currPageStack = [...currPageStack, 'ProductCategory'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_PRODUCT_P_CATEGORY,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_PRODUCT_P_CATEGORY,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ProductCategory' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ProductCategory'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 10, height: '150%' } : {},
                    ]}>{`Product\nCategory`}</Text>
                </TouchableOpacity>
                : <></>}
              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("Product"), setSelectedTab(2), expandAction(2), CommonStore.update(s => {
                                    s.currPage = 'Product';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'Product') && highlightStyle,
                            }]}>

                            <Text style={[styles.expandedItems, { color: currPage == "Product" ? Colors.primaryColor : Colors.descriptionColor }]}>Manage Product</Text>
                        </TouchableOpacity> */}
              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("ProductAdd"), setSelectedTab(2), expandAction(2), CommonStore.update(s => {
                                    s.currPage = 'ProductAdd';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'ProductAdd') && highlightStyle,
                            }]}>

                            <Text style={[styles.expandedItems, { color: currPage == "ProductAdd" ? Colors.primaryColor : Colors.descriptionColor }]}>{'Add\nProduct'}</Text>
                        </TouchableOpacity> */}

              {!screensToBlock.includes('Variant_AddOn') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'VariantAddOn');
                  }}
                  onPress={() => {
                    navigation.navigate('VariantAddOn');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = 'VariantAddOn';
                      s.currPageStack = [...currPageStack, 'VariantAddOn'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_PRODUCT_P_CATEGORY,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_PRODUCT_P_CATEGORY,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'VariantAddOn' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'VariantAddOn'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 10, height: '150%' } : {},
                    ]}>{`Variant\nAdd-On`}</Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Menu_Display') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ProductMenu');
                  }}
                  onPress={() => {
                    navigation.navigate('ProductMenu');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = 'ProductMenu';
                      s.currPageStack = [...currPageStack, 'ProductMenu'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_PRODUCT_MENU_DISPLAY,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_PRODUCT_MENU_DISPLAY,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ProductMenu' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ProductMenu'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Menu\nDisplay'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Manage_Preorder') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'PreorderPackage');
                  }}
                  onPress={() => {
                    navigation.navigate('PreorderPackage');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = 'PreorderPackage';
                      s.currPageStack = [...currPageStack, 'PreorderPackage'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_PRODUCT_MANAGE_PREORDER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_PRODUCT_MANAGE_PREORDER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'PreorderPackage' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'PreorderPackage'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Manage\nPreorder`}</Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Catalog') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Catalog');
                  }}
                  onPress={() => {
                    navigation.navigate('Catalog');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = 'Catalog';
                      s.currPageStack = [...currPageStack, 'Catalog'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Catalog' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Catalog'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 10, height: '150%' } : {},
                    ]}>{`Catalog`}</Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Live_Product') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'LiveProduct');
                  }}
                  onPress={() => {
                    navigation.navigate('LiveProduct');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = 'LiveProduct';
                      s.currPageStack = [...currPageStack, 'LiveProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'LiveProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color: currPage == 'LiveProduct'
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Live\nSelling`}</Text>
                </TouchableOpacity>
                : <></>}
            </View>
          ) : null}
        </View>}

      {/* {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.PRODUCT))
        && privileges.includes(PRIVILEGES_NAME.PRODUCT)) && <View style={[sideBarItemsContainerStyleScale]}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              if (expandTab !== EXPAND_TAB_TYPE.EDITOR_MODEL) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.EDITOR_MODEL;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.EDITOR_MODEL ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <ProductG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      3D Model
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Product
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>3D Model</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.EDITOR_MODEL ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.EDITOR_MODEL
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              <TouchableOpacity
                onLayout={(e) => {
                  layoutHandler(e, 'ModelEditor');
                }}
                onPress={() => {
                  navigation.navigate('ModelEditor');
                  // setSelectedTab(2), expandAction(2),
                  CommonStore.update((s) => {
                    s.currPage = 'ModelEditor';
                    s.currPageStack = [...currPageStack, 'ModelEditor'];
                  });

                  if (expandTab !== EXPAND_TAB_TYPE.EDITOR_MODEL) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.EDITOR_MODEL;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === 'ModelEditor' && highlightStyle),
                  },
                ]}>
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == 'ModelEditor'
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 10, height: '150%' } : {},
                  ]}>{`Model\nEditor`}</Text>
              </TouchableOpacity>
            </View>
          ) : null}
        </View>} */}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.INVENTORY))
        && privileges.includes(PRIVILEGES_NAME.INVENTORY)) && <View style={sideBarItemsContainerStyleScale}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandInventory: !expandInventory, selectedTab: 3 });
              // setExpandInventory(!expandInventory);
              // setSelectedTab(3);
              // expandAction(3)

              if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_INVENTORY,
                eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.INVENTORY ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <InventoryG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Inventory
                    </Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                      <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                        <Arrow width={arrowSize} height={arrowSize} />
                      </View>
                    </View> */}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Inventory
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Inventory</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                      <Arrow width={arrowSize} height={arrowSize} />
                    </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>

          {expandTab === EXPAND_TAB_TYPE.INVENTORY ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.INVENTORY
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('Supplier') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'SupplierProduct');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'SupplierProduct',
                      params: {
                        supplier: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'SupplierProduct';
                      s.currPageStack = [...currPageStack, 'SupplierProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_INVENTORY_SUPPLIER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_SUPPLIER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'SupplierProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'SupplierProduct'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Supplier
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Inventory_Overview') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'InventoryProduct');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'InventoryProduct',
                      params: {
                        lowStockAlert: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'InventoryProduct';
                      s.currPageStack = [...currPageStack, 'InventoryProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_INVENTORY_I_OVERVIEW,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_I_OVERVIEW,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'InventoryProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'InventoryProduct'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Inventory\nOverview`}</Text>
                </TouchableOpacity> :
                <></>}
              {!screensToBlock.includes('Purchase_Order') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'PurchaseOrderProduct');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'PurchaseOrderProduct',
                      params: {
                        purchaseOrder: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'PurchaseOrderProduct';
                      s.currPageStack = [...currPageStack, 'PurchaseOrderProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_INVENTORY_PURCHASE_ORDER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_PURCHASE_ORDER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'PurchaseOrderProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'PurchaseOrderProduct'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Purchase\nOrder`}</Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Stock_Transfer') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'StockTransferProduct');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'StockTransferProduct',
                      params: {
                        stockTransfer: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'StockTransferProduct';
                      s.currPageStack = [...currPageStack, 'StockTransferProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'StockTransferProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'StockTransferProduct'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Stock\nTransfer'}
                  </Text>
                </TouchableOpacity>
                :
                <></>}
              {!screensToBlock.includes('Stock_Take') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'StockTakeProduct');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'StockTakeProduct',
                      params: {
                        stockTake: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'StockTakeProduct';
                      s.currPageStack = [...currPageStack, 'StockTakeProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TAKE,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TAKE,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'StockTakeProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'StockTakeProduct'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Stock\nTake'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Stock_Refund') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'StockReturnProduct');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'StockReturnProduct',
                      params: {
                        stockTake: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'StockReturnProduct';
                      s.currPageStack = [...currPageStack, 'StockReturnProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_INVENTORY_STOCK_RETURN,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_RETURN,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'StockReturnProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'StockReturnProduct'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Stock\nReturn'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Rack_List') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'RackList');
                  }}
                  onPress={() => {
                    navigation.navigate('RackList');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = 'RackList';
                      s.currPageStack = [...currPageStack, 'RackList'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'RackList' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'RackList'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Rack\nList`}</Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Inventory_Forecasting') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'InventoryForecasting');
                  }}
                  onPress={() => {
                    navigation.navigate('InventoryForecasting');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = 'InventoryForecasting';
                      s.currPageStack = [...currPageStack, 'InventoryForecasting'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'InventoryForecasting' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'InventoryForecasting'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Inventory\nForecasting`}</Text>
                </TouchableOpacity>
                : <></>}
            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.INVENTORY_COMPOSITE))
        && privileges.includes(PRIVILEGES_NAME.INVENTORY_COMPOSITE)) && <View style={sideBarItemsContainerStyleScale}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandInventory: !expandInventory, selectedTab: 3 });
              // setExpandInventory(!expandInventory);
              // setSelectedTab(3);
              // expandAction(3)

              if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_COMPOSITE,
                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE,
              });
            }}
            style={[sideBarItemsStyleScale, {
              // marginTop: '10%',
              // marginBottom: '5%',
            }]}>
            {expandTab == EXPAND_TAB_TYPE.INVENTORY_COMPOSITE ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <InventoryG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={[styles.sidebarText, {
                  }]}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        {
                          color: Colors.primaryColor,
                          textAlign: 'center',
                        },
                      ]}>
                      {'Composite'}
                    </Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                      <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                        <Arrow width={arrowSize} height={arrowSize} />
                      </View>
                    </View> */}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Inventory
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={[sidebarTextStyleScale, {
                      textAlign: 'center',
                    }]}>{'Composite'}</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                      <Arrow width={arrowSize} height={arrowSize} />
                    </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>

          {expandTab === EXPAND_TAB_TYPE.INVENTORY_COMPOSITE ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.INVENTORY_COMPOSITE
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('Supplier_Composite') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Supplier');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'Supplier',
                      params: {
                        supplier: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'Supplier';
                      s.currPageStack = [...currPageStack, 'Supplier'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_SUPPLIER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_SUPPLIER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Supplier' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Supplier'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Supplier
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Inventory_Overview_Composite') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Inventory');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'Inventory',
                      params: {
                        lowStockAlert: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'Inventory';
                      s.currPageStack = [...currPageStack, 'Inventory'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_I_OVERVIEW,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_I_OVERVIEW,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Inventory' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Inventory'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Inventory\nOverview`}</Text>
                </TouchableOpacity>
                : <></>}

              {/* 2025-03-21 - hide first */}
              {!screensToBlock.includes('Purchase_Order_Composite') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'PurchaseOrder');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'PurchaseOrder',
                      params: {
                        purchaseOrder: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'PurchaseOrder';
                      s.currPageStack = [...currPageStack, 'PurchaseOrder'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'PurchaseOrder' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'PurchaseOrder'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Purchase\nOrder`}</Text>
                </TouchableOpacity> :
                <></>}
              {!screensToBlock.includes('Stock_Transfer_Composite') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'StockTransfer');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'StockTransfer',
                      params: {
                        stockTransfer: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'StockTransfer';
                      s.currPageStack = [...currPageStack, 'StockTransfer'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_STOCK_TRANSFER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_STOCK_TRANSFER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'StockTransfer' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'StockTransfer'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Stock\nTransfer'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Stock_Take_Composite') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'StockTake');
                  }}
                  onPress={() => {
                    navigation.navigate({
                      name: 'StockTake',
                      params: {
                        stockTake: true,
                      },
                      merge: true,
                    });

                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'StockTake';
                      s.currPageStack = [...currPageStack, 'StockTake'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_STOCK_TAKE,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_STOCK_TAKE,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'StockTake' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'StockTake'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Stock\nTake'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* JJ */}
              {!screensToBlock.includes('WorkOrderItemList') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'WorkOrderItemList');
                  }}
                  onPress={() => {
                    navigation.navigate('WorkOrderItemList', {
                      WorkOrderItemList: true,
                    }),
                      // setSelectedTab(3), expandAction(3),
                      CommonStore.update((s) => {
                        s.currPage = 'WorkOrderItemList';
                        s.currPageStack = [...currPageStack, 'WorkOrderItemList'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_STOCK_TRANSFER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_STOCK_TRANSFER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'WorkOrderItemList' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'WorkOrderItemList'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Work Order\nItem List'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('WorkOrderList') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'WorkOrderList');
                  }}
                  onPress={() => {
                    navigation.navigate('WorkOrderList', {
                      WorkOrderList: true,
                    }),
                      // setSelectedTab(3), expandAction(3),
                      CommonStore.update((s) => {
                        s.currPage = 'WorkOrderList';
                        s.currPageStack = [...currPageStack, 'WorkOrderList'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_STOCK_TRANSFER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_STOCK_TRANSFER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'WorkOrderList' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'WorkOrderList'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Work\nOrder List'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('CompositeReport') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'CompositeReport');
                  }}
                  onPress={() => {
                    navigation.navigate('CompositeReport', {
                      CompositeReport: true,
                    }),
                      // setSelectedTab(3), expandAction(3),
                      CommonStore.update((s) => {
                        s.currPage = 'CompositeReport';
                        s.currPageStack = [...currPageStack, 'CompositeReport'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_STOCK_TRANSFER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_STOCK_TRANSFER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'CompositeReport' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'CompositeReport'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Composite\nReport'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* WO Report */}
              {/* <TouchableOpacity
                onLayout={(e) => {
                  layoutHandler(e, 'WorkOrderReportScreen');
                }}
                onPress={() => {
                  props.navigation.navigate('WorkOrderReportScreen', {
                    stockTransfer: true,
                  }),
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = 'WorkOrderReportScreen';
                      s.currPageStack = [...currPageStack, 'WorkOrderReportScreen'];
                    });

                  if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === 'WorkOrderReportScreen' && highlightStyle),
                  },
                ]}>
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == 'WorkOrderReportScreen'
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9, height: '150%' } : {},
                  ]}>
                  {'Work\nOrder Report'}
                </Text>
              </TouchableOpacity> */}

            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.DOCKET))
        && privileges.includes(PRIVILEGES_NAME.DOCKET))
        ?
        <View style={sideBarItemsContainerStyleScale}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              //setState({ expandRedemption: !expandRedemption, selectedTab: 4 });
              // setExpandRedemption(!expandRedemption);
              // setSelectedTab(4);
              // expandAction(4)

              if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_DOCKET,
                eventNameParsed: ANALYTICS_PARSED.MODULE_DOCKET,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.DOCKET ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <RedemptionG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Docket
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Redemption
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Docket</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>

          {expandTab === EXPAND_TAB_TYPE.DOCKET ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.DOCKET
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('Active_Docket') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Redemption');
                  }}
                  onPress={() => {
                    setSelectedTab(4), navigation.navigate("Redemption"), setSelectedTab(4), expandAction(4), CommonStore.update(s => {
                      s.currPage = 'Redemption';
                      s.currPageStack = [
                        ...currPageStack,
                        'Redemption',
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_DOCKET_ACTIVE_D,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_DOCKET_ACTIVE_D,
                    });
                  }}
                  style={[styles.subBar, {
                    ...(currPage === 'Redemption' && highlightStyle),
                  }]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Redemption'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{'Active\nDocket'}</Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Expired_Docket') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'RedemptionExpired');
                  }}
                  onPress={() => {
                    setSelectedTab(4), navigation.navigate("RedemptionExpired"), setSelectedTab(4), expandAction(4), CommonStore.update(s => {
                      s.currPage = 'RedemptionExpired';
                      s.currPageStack = [
                        ...currPageStack,
                        'RedemptionExpired',
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_DOCKET_EXPIRED_D,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_DOCKET_EXPIRED_D,
                    });
                  }}
                  style={[styles.subBar, {
                    ...(currPage === 'RedemptionExpired' && highlightStyle),
                  }]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'RedemptionExpired'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{'Expired\nDocket'}</Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Redeemed_Docket') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'RedemptionRedeemed');
                  }}
                  onPress={() => {
                    setSelectedTab(4),
                      navigation.navigate('RedemptionRedeemed'),
                      // setSelectedTab(4), expandAction(4),
                      CommonStore.update((s) => {
                        s.currPage = 'RedemptionRedeemed';
                        s.currPageStack = [...currPageStack, 'RedemptionRedeemed'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_DOCKET_REDEEMED_D,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_DOCKET_REDEEMED_D,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'RedemptionRedeemed' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'RedemptionRedeemed'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Redeemed\nDocket`}</Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Manage_Docket') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'SettingRedemption');
                  }}
                  onPress={() => {
                    navigation.navigate('SettingRedemption');
                    // setSelectedTab(4), expandAction(4),
                    CommonStore.update((s) => {
                      s.currPage = 'SettingRedemption';
                      s.currPageStack = [...currPageStack, 'SettingRedemption'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_DOCKET_MANAGE_D,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_DOCKET_MANAGE_D,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'SettingRedemption' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'SettingRedemption'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Manage\nDocket`}</Text>
                </TouchableOpacity>
                : <></>}
              {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate("Campaign"); setSelectedTab(4), expandAction(4), CommonStore.update(s => {
                  s.currPage = 'Campaign';
                });
              }}
              style={[styles.subBar, {
                ...(currPage === 'Campaign') && highlightStyle,
              }]}>
              <Text style={[styles.expandedItems, { color: currPage == "Campaign" ? Colors.primaryColor : Colors.descriptionColor }]}>Campaign</Text>
            </TouchableOpacity> */}
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {/* voucher wip */}
      {/* {(isBetaUser && privileges.includes(PRIVILEGES_NAME.VOUCHER)) */}
      {(
        (currOutlet && currOutlet.privileges &&
          currOutlet.privileges.includes(PRIVILEGES_NAME.VOUCHER))
        && privileges.includes(PRIVILEGES_NAME.VOUCHER))
        ?
        <View style={sideBarItemsContainerStyleScale}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              //setExpandPromotions(!expandPromotions);
              //setSelectedTab(11);
              //expandAction(11)
              //navigation.navigate("PromotionList")
              // setExpandCRM(!expandPromotions);
              // setSelectedTab(11);
              // expandAction(11)

              if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_VOUCHER,
                eventNameParsed: ANALYTICS_PARSED.MODULE_VOUCHER,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.VOUCHER ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Icon
                      name="ticket-confirmation-outline"
                      // color="#0F1A3C"
                      color={Colors.descriptionColor}
                      size={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Voucher
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Icon
                      name="ticket-confirmation-outline"
                      // color="#0F1A3C"
                      color={Colors.descriptionColor}
                      size={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Voucher</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.VOUCHER ? (
            <View>
              <View
                style={{
                  paddingVertical: 16,
                  paddingTop: 5,
                  backgroundColor:
                    expandTab == EXPAND_TAB_TYPE.VOUCHER
                      ? Colors.lightPrimary
                      : null,
                  alignItems: 'center',
                }}>
                {!screensToBlock.includes('Voucher_List') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'TaggableVoucherList');
                    }}
                    onPress={() => {
                      navigation.navigate('TaggableVoucherList', { selectedTab: 5 }),
                        // setSelectedTab(11), expandAction(11),
                        CommonStore.update((s) => {
                          s.currPage = 'TaggableVoucherList';
                          s.currPageStack = [...currPageStack, 'TaggableVoucherList'];
                        });

                      if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_VOUCHER_V_LIST,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_VOUCHER_V_LIST,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'TaggableVoucherList' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'TaggableVoucherList'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>
                      {'Voucher\nList'}
                    </Text>
                  </TouchableOpacity>
                  : <></>}
                {!screensToBlock.includes('Add_Voucher') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'NewTaggableVoucher');
                    }}
                    onPress={() => {
                      navigation.navigate('NewTaggableVoucher', { selectedTab: 5 }), setSelectedTab(5), expandAction(5), CommonStore.update(s => {
                        s.currPage = 'NewTaggableVoucher';
                        s.currPageStack = [
                          ...currPageStack,
                          'NewTaggableVoucher',
                        ];

                        s.selectedTaggableVoucherEdit = null;
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_VOUCHER_ADD_V,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_VOUCHER_ADD_V,
                      });
                    }}
                    style={[styles.subBar, {
                      ...(currPage === 'NewTaggableVoucher' && highlightStyle),
                    }]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'NewTaggableVoucher'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{'Add\nVoucher'}</Text>
                  </TouchableOpacity>
                  :
                  <></>}
                {!screensToBlock.includes('Voucher_Report') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'TaggableVoucherReport');
                    }}
                    onPress={() => {
                      navigation.navigate('TaggableVoucherReport', { selectedTab: 5 }), setSelectedTab(5), expandAction(5), CommonStore.update(s => {
                        s.currPage = 'TaggableVoucherReport';
                        s.currPageStack = [
                          ...currPageStack,
                          'TaggableVoucherReport',
                        ];

                        s.selectedTaggableVoucherEdit = null;
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_VOUCHER_V_REPORT,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_VOUCHER_V_REPORT,
                      });
                    }}
                    style={[styles.subBar, {
                      ...(currPage === 'TaggableVoucherReport' && highlightStyle),
                    }]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'RedemptionRedeemed'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{'Voucher\nReport'}</Text>
                  </TouchableOpacity>
                  :
                  <></>}
                {/* <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('VoucherReport', {
                      selectedTab: 5,
                    }),
                      // setSelectedTab(11), expandAction(11),
                      CommonStore.update((s) => {
                        s.currPage = 'VoucherReport';
                        s.currPageStack = [...currPageStack, 'VoucherReport'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'VoucherReport' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'VoucherReport'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Voucher\nReport`}</Text>
                </TouchableOpacity> */}
              </View>
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {/* {privileges.includes(PRIVILEGES_NAME.VOUCHER) &&
      <View style={sideBarItemsContainerStyleScale}>
        {
          isAlphaUser
            ?
            <TouchableHighlight
              underlayColor={Colors.lightPrimary}
              onPress={() => {
                // setState({ expandPromotions: !expandPromotions, selectedTab: 5 }),
                // setExpandVoucher(!expandVoucher);
                // setSelectedTab(5);
                // expandAction(5)

                if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                  });
                }
              }}
              style={sideBarItemsStyleScale}>
              {expandTab == EXPAND_TAB_TYPE.VOUCHER ? (
                <View>
                  <View
                    style={[
                      styles.sidebarView,
                      { backgroundColor: Colors.lightPrimary },
                      switchMerchant
                        ? {
                          // borderWidth: 1,
                          // width: '48%'
                        }
                        : {},
                    ]}>
                    <View style={sidebarIconStyleScale}>
                      <Icon
                        name="ticket-confirmation-outline"
                        color="#0F1A3C"
                        size={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                      />
                    </View>
                    <View style={styles.sidebarText}>
                      <Text
                        style={[
                          sidebarTextStyleScale,
                          { color: Colors.primaryColor },
                        ]}>
                        Voucher
                      </Text>
                    </View> */}
      {/* <View style={[styles.sidebarArrow]}>
                      <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                        <Arrow width={arrowSize} height={arrowSize} />
                      </View>
                    </View> */}
      {/* </View>
                </View>
              ) : (
                <View>
                  <View
                    style={[
                      styles.sidebarView,
                      switchMerchant
                        ? {
                          // borderWidth: 1,
                          // width: '48%'
                        }
                        : {},
                    ]}>
                    <View style={sidebarIconStyleScale}>
                      <Icon
                        name="ticket-confirmation-outline"
                        color="#ACACAC"
                        size={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                      />
                    </View>
                    <View style={styles.sidebarText}>
                      <Text style={sidebarTextStyleScale}>Voucher</Text>
                    </View> */}
      {/* <View style={[styles.sidebarArrow]}>
                      <Arrow width={arrowSize} height={arrowSize} />
                    </View> */}
      {/* </View>
                </View>
              )}
            </TouchableHighlight>
            :
            <></>
        } */}

      {/* {expandTab == EXPAND_TAB_TYPE.VOUCHER ? (
          <View
            style={{
              paddingVertical: 16,
              paddingTop: 5,
              backgroundColor:
                expandTab == EXPAND_TAB_TYPE.VOUCHER
                  ? Colors.lightPrimary
                  : null,
              alignItems: 'center',
            }}>
            <TouchableOpacity
              onPress={() => {
                // setSelectedTab(5), expandAction(5),
                navigation.navigate('Voucher'),
                  CommonStore.update((s) => {
                    s.currPage = 'Voucher';
                    s.currPageStack = [...currPageStack, 'Voucher'];
                  });

                if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'Voucher' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'Voucher'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9 } : {},
                ]}>
                E-vouchers
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                // setSelectedTab(5), expandAction(5),
                navigation.navigate('VoucherReport'),
                  CommonStore.update((s) => {
                    s.currPage = 'VoucherReport';
                    s.currPageStack = [...currPageStack, 'VoucherReport'];
                  });

                if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'VoucherReport' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'VoucherReport'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>
                {'Voucher\nReport'}
              </Text>
            </TouchableOpacity>
          </View>
        ) : null
        } */}
      {/* </View>
      } */}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.PROMOTION))
        && privileges.includes(PRIVILEGES_NAME.PROMOTION)) &&
        <View style={sideBarItemsContainerStyleScale}>
          {/* <TouchableOpacity */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              //setExpandPromotions(!expandPromotions);
              //setSelectedTab(11);
              //expandAction(11)
              //navigation.navigate("PromotionList")
              // setExpandCRM(!expandPromotions);
              // setSelectedTab(11);
              // expandAction(11)

              if (expandTab !== EXPAND_TAB_TYPE.PROMOTION) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.PROMOTION;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_PROMOTION,
                eventNameParsed: ANALYTICS_PARSED.MODULE_PROMOTION,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.PROMOTION ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <PromotionsG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Promotion
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Promotions
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Promotion</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                  <Arrow width={arrowSize} height={arrowSize} />
                </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>
          {/* </TouchableOpacity> */}
          {expandTab === EXPAND_TAB_TYPE.PROMOTION ? (
            <View>
              <View
                style={{
                  paddingVertical: 16,
                  paddingTop: 5,
                  backgroundColor:
                    expandTab == EXPAND_TAB_TYPE.PROMOTION
                      ? Colors.lightPrimary
                      : null,
                  alignItems: 'center',
                }}>
                {!screensToBlock.includes('Promotion_List') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'PromotionList');
                    }}
                    onPress={() => {
                      navigation.navigate('PromotionList', { selectedTab: 11 }),
                        // setSelectedTab(11), expandAction(11),
                        CommonStore.update((s) => {
                          s.currPage = 'PromotionList';
                          s.currPageStack = [...currPageStack, 'PromotionList'];
                        });

                      if (expandTab !== EXPAND_TAB_TYPE.PROMOTION) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.PROMOTION;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_PROMOTION_P_LIST,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_PROMOTION_P_LIST,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'PromotionList' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'PromotionList'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>
                      {'Promotion\nList'}
                    </Text>
                  </TouchableOpacity>
                  : <></>}
                {!screensToBlock.includes('Add_Promotion') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'NewCampaign');
                    }}
                    onPress={() => {
                      navigation.navigate('NewCampaign', { selectedTab: 11 }), setSelectedTab(11), expandAction(11), CommonStore.update(s => {
                        s.currPage = 'NewCampaign';
                        s.currPageStack = [
                          ...currPageStack,
                          'NewCampaign',
                        ];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.PROMOTION) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.PROMOTION;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_PROMOTION_ADD_P,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_PROMOTION_ADD_P,
                      });
                    }}
                    style={[styles.subBar, {
                      ...(currPage === 'NewCampaign' && highlightStyle),
                    }]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'NewCampaign'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{'Add\nPromotion'}</Text>
                  </TouchableOpacity>
                  : <></>}
                {!screensToBlock.includes('Promotion_Report') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'PromotionReport');
                    }}
                    onPress={() => {
                      navigation.navigate('PromotionReport', {
                        selectedTab: 11,
                      }),
                        // setSelectedTab(11), expandAction(11),
                        CommonStore.update((s) => {
                          s.currPage = 'PromotionReport';
                          s.currPageStack = [...currPageStack, 'PromotionReport'];
                        });

                      if (expandTab !== EXPAND_TAB_TYPE.PROMOTION) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.PROMOTION;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_PROMOTION_P_REPORT,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_PROMOTION_P_REPORT,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'PromotionReport' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'PromotionReport'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: '150%' } : {},
                      ]}>{`Promotion\nReport`}</Text>
                  </TouchableOpacity>
                  : <></>}
              </View>
            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.CRM))
        && privileges.includes(PRIVILEGES_NAME.CRM)) && <View style={sideBarItemsContainerStyleScale}>
          {/* <TouchableOpacity */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandPromotions: !expandPromotions, selectedTab: 6 }),
              // setExpandCRM(!expandCRM);
              // setSelectedTab(6);
              // expandAction(6)

              if (expandTab !== EXPAND_TAB_TYPE.CRM) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.CRM;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_CRM,
                eventNameParsed: ANALYTICS_PARSED.MODULE_CRM,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.CRM ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <CRMG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      color={Colors.primaryColor}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      CRM
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <CRM
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>CRM</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {/* </TouchableOpacity> */}
          {expandTab === EXPAND_TAB_TYPE.CRM ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab === EXPAND_TAB_TYPE.CRM ? Colors.lightPrimary : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('Customer') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'CrmScreen');
                  }}
                  onPress={() => {
                    navigation.navigate('CrmScreen', { selectedTab: 6 }),
                      // setSelectedTab(6), expandAction(6),
                      CommonStore.update((s) => {
                        s.currPage = 'CrmScreen';
                        s.currPageStack = [...currPageStack, 'CrmScreen'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.CRM) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.CRM;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_CRM_CUSTOMER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'CrmScreen' && highlightStyle),
                    },
                  ]}>
                  {/* <Text style={[styles.expandedItems, { color: currPage == "CrmScreen" ? Colors.primaryColor : Colors.descriptionColor }]}>Manage Customers</Text> */}
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'CrmScreen'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Customers
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Segment') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'SegmentScreen');
                  }}
                  onPress={() => {
                    navigation.navigate('SegmentScreen', { selectedTab: 6 }),
                      // setSelectedTab(6), expandAction(6),
                      CommonStore.update((s) => {
                        s.currPage = 'SegmentScreen';
                        s.currPageStack = [...currPageStack, 'SegmentScreen'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.CRM) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.CRM;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_CRM_SEGMENT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'SegmentScreen' && highlightStyle),
                    },
                  ]}>
                  {/* <Text style={[styles.expandedItems, { color: currPage == "SegmentScreen" ? Colors.primaryColor : Colors.descriptionColor }]}>Manage Segment</Text> */}
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'SegmentScreen'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Segment
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Customer_Review') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'CustomerReviewScreen');
                  }}
                  onPress={() => {
                    navigation.navigate('CustomerReviewScreen', { selectedTab: 6 }),
                      // setSelectedTab(6), expandAction(6),
                      CommonStore.update((s) => {
                        s.currPage = 'CustomerReviewScreen';
                        s.currPageStack = [...currPageStack, 'CustomerReviewScreen'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.CRM) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.CRM;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_CRM_CUSTOMER_REVIEW,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_CUSTOMER_REVIEW,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'CustomerReviewScreen' && highlightStyle),
                    },
                  ]}>
                  {/* <Text style={[styles.expandedItems, { color: currPage == "CrmScreen" ? Colors.primaryColor : Colors.descriptionColor }]}>Manage Customers</Text> */}
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'CustomerReviewScreen'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Reviews
                  </Text>
                </TouchableOpacity>
                : <></>}

              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate('NewCustomer', { selectedTab: 6 }), setSelectedTab(6), expandAction(6), CommonStore.update(s => {
                                    s.currPage = 'NewCustomer';
                                    s.currPageStack = [
                                        ...currPageStack,
                                        'NewCustomer',
                                    ];
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'NewCustomer') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "NewCustomer" ? Colors.primaryColor : Colors.descriptionColor }]}>New Customers</Text>
                        </TouchableOpacity> */}
            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.LOYALTY))
        && privileges.includes(PRIVILEGES_NAME.LOYALTY)) && <View style={sideBarItemsContainerStyleScale}>
          {
            ((currOutlet && currOutlet.privileges &&
              currOutlet.privileges.includes(PRIVILEGES_NAME.LOYALTY))
              && privileges.includes(PRIVILEGES_NAME.LOYALTY))
              ?
              <TouchableHighlight
                underlayColor={Colors.lightPrimary}
                onPress={() => {
                  // setExpandLoyaltyPoints(!expandLoyaltyPoints);
                  // setSelectedTab(12);
                  // expandAction(12)

                  if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                    });
                  } else {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.NONE;
                    });
                  }

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_LOYALTY,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY,
                  });
                }}
                style={sideBarItemsStyleScale}>
                {expandTab == EXPAND_TAB_TYPE.LOYALTY ? (
                  <View>
                    <View
                      style={[
                        styles.sidebarView,
                        {
                          backgroundColor: 'Colors.lightPrimary',
                          height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                            isTablet()
                              ? windowHeight * 0.08
                              : windowHeight * 0.16,
                          width: isTablet()
                            ? windowWidth * Styles.sideBarWidth
                            : '100%',
                        },
                        switchMerchant
                          ? {
                            // borderWidth: 1,
                            // width: '48%'
                          }
                          : {},
                      ]}>
                      <View style={[sidebarIconStyleScale, {}]}>
                        <MaterialIcons
                          name="loyalty"
                          size={
                            switchMerchant
                              ? windowWidth * 0.02
                              : windowWidth * iconSize
                          }
                          color={Colors.primaryColor}
                        />
                      </View>
                      <View style={styles.sidebarText}>
                        <Text
                          style={[
                            sidebarTextStyleScale,
                            { color: Colors.primaryColor },
                          ]}>
                          Loyalty
                        </Text>
                      </View>
                    </View>
                    <View style={{ borderWidth: 0.5, borderColor: '#E5E5E5' }} />
                  </View>
                ) : (
                  <View>
                    <View
                      style={[
                        styles.sidebarView,
                        {
                          backgroundColor: 'Colors.lightPrimary',
                          height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                            isTablet()
                              ? windowHeight * 0.08
                              : windowHeight * 0.16,
                          width: isTablet()
                            ? windowWidth * Styles.sideBarWidth
                            : '100%',
                        },
                        switchMerchant
                          ? {
                            // borderWidth: 1,
                            // width: '48%'
                          }
                          : {},
                      ]}>
                      <View tyle={[sidebarIconStyleScale, {}]}>
                        <MaterialIcons
                          name="loyalty"
                          size={
                            switchMerchant
                              ? windowWidth * 0.02
                              : windowWidth * iconSize
                          }
                          color={Colors.descriptionColor}
                          style={{ opacity: 0.75 }}
                        />
                      </View>
                      <View style={styles.sidebarText}>
                        <Text style={sidebarTextStyleScale}>Loyalty</Text>
                      </View>
                    </View>
                  </View>
                )}
              </TouchableHighlight>
              :
              <></>
          }

          {expandTab === EXPAND_TAB_TYPE.LOYALTY ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.LOYALTY
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('Loyalty_Campaign') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'NewLoyaltyCampaign');
                  }}
                  onPress={() => {
                    navigation.navigate('NewLoyaltyCampaign');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = 'NewLoyaltyCampaign';
                      s.currPageStack = [...currPageStack, 'NewLoyaltyCampaign'];

                      s.selectedLoyaltyCampaignEdit = null;
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_CAMPAIGN,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_CAMPAIGN,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'NewLoyaltyCampaign' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'NewLoyaltyCampaign'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Campaign'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* {!screensToBlock.includes('Sign_Up_Reward') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'LoyaltySignUpCampaign');
                  }}
                  onPress={() => {
                    navigation.navigate('LoyaltySignUpCampaign');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = 'LoyaltySignUpCampaign';
                      s.currPageStack = [...currPageStack, 'LoyaltySignUpCampaign'];

                      // s.selectedLoyaltySignUpCampaignEdit = null;
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_SIGN_UP_REWARD,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_SIGN_UP_REWARD,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'LoyaltySignUpCampaign' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'LoyaltySignUpCampaign'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Sign Up\nReward'}
                  </Text>
                </TouchableOpacity>
                : <></>} */}
              {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('SettingCredit');
                // setSelectedTab(12), expandAction(12),
                CommonStore.update((s) => {
                  s.currPage = 'SettingCredit';
                  s.currPageStack = [...currPageStack, 'SettingCredit'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'SettingCredit' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'SettingCredit'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>
                {'Loyalty\nPoints'}
              </Text>
            </TouchableOpacity> */}
              {!screensToBlock.includes('Stamps') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'LoyaltyStampScreen');
                  }}
                  onPress={() => {
                    navigation.navigate('LoyaltyStampScreen');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = 'LoyaltyStampScreen';
                      s.currPageStack = [...currPageStack, 'LoyaltyStampScreen'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_STAMPS,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_STAMPS,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'LoyaltyStampScreen' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'LoyaltyStampScreen'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Stamps'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('LoyaltyPhone');
                // setSelectedTab(9), expandAction(9),
                CommonStore.update((s) => {
                  s.currPage = 'LoyaltyPhone';
                  s.currPageStack = [...currPageStack, 'LoyaltyPhone'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'LoyaltyPhone' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'LoyaltyPhone'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>{`Earn & \n Redeem`}</Text>
            </TouchableOpacity> */}
              {!screensToBlock.includes('Pay_Earn') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'LoyaltyPayEarn');
                  }}
                  onPress={() => {
                    navigation.navigate('LoyaltyPayEarn');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'LoyaltyPayEarn';
                      s.currPageStack = [...currPageStack, 'LoyaltyPayEarn'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_PAY_EARN,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_PAY_EARN,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'LoyaltyPayEarn' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'LoyaltyPayEarn'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Pay & \n Earn`}</Text>
                </TouchableOpacity>
                : <></>}

              {/* Reward redemption */}
              {!screensToBlock.includes('Reward_Redemption') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'LoyaltyRewardRedemption');
                  }}
                  onPress={() => {
                    navigation.navigate('LoyaltyRewardRedemption');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'LoyaltyRewardRedemption';
                      s.currPageStack = [...currPageStack, 'LoyaltyRewardRedemption'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_REWARD_REDEMPTION,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_REWARD_REDEMPTION,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'LoyaltyRewardRedemption' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'LoyaltyRewardRedemption'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Reward &\nRedemption`}</Text>
                </TouchableOpacity>
                :
                <></>}
              {!screensToBlock.includes('Credit_Type') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'TopupCreditType');
                  }}
                  onPress={() => {
                    navigation.navigate('TopupCreditType');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = 'TopupCreditType';
                      s.currPageStack = [...currPageStack, 'TopupCreditType'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_CREDIT_TYPE,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_CREDIT_TYPE,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'TopupCreditType' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'TopupCreditType'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Credit\nType'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Credit_Type_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'TopUpCreditReport');
                  }}
                  onPress={() => {
                    navigation.navigate('TopUpCreditReport');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'TopUpCreditReport';
                      s.currPageStack = [...currPageStack, 'TopUpCreditReport'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_CREDIT_TYPE_REPORT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_CREDIT_TYPE_REPORT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'TopUpCreditReport' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'TopUpCreditReport'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Credit\nType\nReport`}</Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Loyalty_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'LoyaltyReport');
                  }}
                  onPress={() => {
                    navigation.navigate('LoyaltyReport');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'LoyaltyReport';
                      s.currPageStack = [...currPageStack, 'LoyaltyReport'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_L_REPORT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_L_REPORT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'LoyaltyReport' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'LoyaltyReport'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Loyalty\nReport`}</Text>
                </TouchableOpacity>
                : <></>}


              {!screensToBlock.includes('Loyalty_Setting') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'LoyaltySettingsScreen');
                  }}
                  onPress={() => {
                    navigation.navigate('LoyaltySettingsScreen');
                    CommonStore.update((s) => {
                      s.currPage = 'LoyaltySettingsScreen';
                      s.currPageStack = [...currPageStack, 'LoyaltySettingsScreen'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_SETTINGS_LOYALTY,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_LOYALTY,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'LoyaltySettingsScreen' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'LoyaltySettingsScreen'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    {'Loyalty\nSettings'}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Ai_Campaign') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'AiCampaignScreen');
                  }}
                  onPress={() => {
                    navigation.navigate('AiCampaignScreen');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'AiCampaignScreen';
                      s.currPageStack = [...currPageStack, 'AiCampaignScreen'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_LOYALTY_L_REPORT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_LOYALTY_L_REPORT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'AiCampaignScreen' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'AiCampaignScreen'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`AI\nCampaign`}</Text>
                </TouchableOpacity>
                : <></>}

              {/* stamp type  */}
              {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('LoyaltyStampType');
                // setSelectedTab(12), expandAction(12),
                CommonStore.update((s) => {
                  s.currPage = 'LoyaltyStampType';
                  s.currPageStack = [...currPageStack, 'LoyaltyStampType'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'LoyaltyStampType' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'LoyaltyStampType'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>
                {'Stamps\nType'}
              </Text>
            </TouchableOpacity> */}

              {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('LoyaltyPointsRate');
                // setSelectedTab(12), expandAction(12),
                CommonStore.update((s) => {
                  s.currPage = 'LoyaltyPointsRate';
                  s.currPageStack = [...currPageStack, 'LoyaltyPointsRate'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'LoyaltyPointsRate' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'LoyaltyPointsRate'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: '150%' } : {},
                ]}>
                {'Points\nRate'}
              </Text>
            </TouchableOpacity> */}

              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("LoyaltyStampScreen"); setSelectedTab(12), expandAction(12), CommonStore.update(s => {
                                    s.currPage = 'LoyaltyStampScreen';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'LoyaltyStampScreen') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "LoyaltyStampScreen" ? Colors.primaryColor : Colors.descriptionColor }]}>{`Loyalty\nStamp`}</Text>
                        </TouchableOpacity> */}
            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.UPSELLING))
        && privileges.includes(PRIVILEGES_NAME.UPSELLING)) && <View style={sideBarItemsContainerStyleScale}>
          {
            ((currOutlet && currOutlet.privileges &&
              currOutlet.privileges.includes(PRIVILEGES_NAME.UPSELLING))
              && privileges.includes(PRIVILEGES_NAME.UPSELLING))
              ?
              <TouchableHighlight
                underlayColor={Colors.lightPrimary}
                onPress={() => {
                  // setExpandLoyaltyPoints(!expandLoyaltyPoints);
                  // setSelectedTab(12);
                  // expandAction(12)

                  if (expandTab !== EXPAND_TAB_TYPE.UPSELLING) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.UPSELLING;
                    });
                  } else {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.NONE;
                    });
                  }

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_UPSELLING,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING,
                  });
                }}
                style={sideBarItemsStyleScale}>
                {expandTab == EXPAND_TAB_TYPE.UPSELLING ? (
                  <View>
                    <View
                      style={[
                        styles.sidebarView,
                        {
                          backgroundColor: 'Colors.lightPrimary',
                          height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                            isTablet()
                              ? windowHeight * 0.08
                              : windowHeight * 0.16,
                          width: isTablet()
                            ? windowWidth * Styles.sideBarWidth
                            : '100%',
                        },
                        switchMerchant
                          ? {
                            // borderWidth: 1,
                            // width: '48%'
                          }
                          : {},
                      ]}>
                      <View style={[sidebarIconStyleScale, {}]}>
                        <MaterialIcons
                          name="trending-up"
                          size={
                            switchMerchant
                              ? windowWidth * 0.02
                              : windowWidth * iconSize
                          }
                          color={Colors.primaryColor}
                        />
                      </View>
                      <View style={styles.sidebarText}>
                        <Text
                          style={[
                            sidebarTextStyleScale,
                            { color: Colors.primaryColor },
                          ]}>
                          Upselling
                        </Text>
                      </View>
                    </View>
                    <View style={{ borderWidth: 0.5, borderColor: '#E5E5E5' }} />
                  </View>
                ) : (
                  <View>
                    <View
                      style={[
                        styles.sidebarView,
                        {
                          backgroundColor: 'Colors.lightPrimary',
                          height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                            isTablet()
                              ? windowHeight * 0.08
                              : windowHeight * 0.16,
                          width: isTablet()
                            ? windowWidth * Styles.sideBarWidth
                            : '100%',
                        },
                        switchMerchant
                          ? {
                            // borderWidth: 1,
                            // width: '48%'
                          }
                          : {},
                      ]}>
                      <View tyle={[sidebarIconStyleScale, {}]}>
                        <MaterialIcons
                          name="trending-up"
                          size={
                            switchMerchant
                              ? windowWidth * 0.02
                              : windowWidth * iconSize
                          }
                          color={Colors.descriptionColor}
                          style={{ opacity: 0.75 }}
                        />
                      </View>
                      <View style={styles.sidebarText}>
                        <Text style={sidebarTextStyleScale}>Upselling</Text>
                      </View>
                    </View>
                  </View>
                )}
              </TouchableHighlight>
              :
              <></>
          }

          {expandTab === EXPAND_TAB_TYPE.UPSELLING ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.UPSELLING
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>

              {!screensToBlock.includes('Upselling_List') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'UpsellingList');
                  }}
                  onPress={() => {
                    navigation.navigate('UpsellingList');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = 'UpsellingList';
                      s.currPageStack = [...currPageStack, 'UpsellingList'];

                      s.selectedLoyaltyCampaignEdit = null;
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.UPSELLING) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.UPSELLING;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_UPSELLING_LIST,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_LIST,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'UpsellingList' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'UpsellingList'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Upselling\nList'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Upselling_Campaign') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'UpsellingCampaign');
                  }}
                  onPress={() => {
                    navigation.navigate('UpsellingCampaign');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = 'UpsellingCampaign';
                      s.currPageStack = [...currPageStack, 'UpsellingCampaign'];

                      s.selectedUpsellingCampaignEdit = null;
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.UPSELLING) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.UPSELLING;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_UPSELLING_CAMPAIGN,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_CAMPAIGN,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'UpsellingCampaign' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'UpsellingCampaign'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Upselling\nCampaign'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {/* {!screensToBlock.includes('Upselling_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('UpsellingReport');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'UpsellingReport';
                      s.currPageStack = [...currPageStack, 'UpsellingReport'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.UPSELLING) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.UPSELLING;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_UPSELLING_REPORT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_REPORT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'UpsellingReport' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'UpsellingReport'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Upselling\nReport`}</Text>
                </TouchableOpacity>
                : <></>} */}
            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.TRANSACTIONS))
        && privileges.includes(PRIVILEGES_NAME.TRANSACTIONS) && !screensToBlock.includes('All_Transcation')) && <View style={sideBarItemsContainerStyleScale}>
          {/* <TouchableOpacity */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandTransactions: !expandTransactions, selectedTab: 7 }),
              // setExpandTransactions(!expandTransactions);
              // setSelectedTab(7);
              // expandAction(7)

              if (expandTab !== EXPAND_TAB_TYPE.TRANSACTIONS) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.TRANSACTIONS;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_TRANSACTION,
                eventNameParsed: ANALYTICS_PARSED.MODULE_TRANSACTION,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.TRANSACTIONS ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <TransactionsG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Transactions
                    </Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                                        <Arrow width={arrowSize} height={arrowSize} />
                                    </View>
                                </View> */}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Transactions
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Transactions</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <Arrow width={arrowSize} height={arrowSize} />
                                </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>
          {/* </TouchableOpacity> */}
          {expandTab === EXPAND_TAB_TYPE.TRANSACTIONS ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.TRANSACTIONS
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              <TouchableOpacity
                onLayout={(e) => {
                  layoutHandler(e, 'AllTransaction');
                }}
                onPress={() => {
                  navigation.navigate('AllTransaction', { selectedTab: 7 });
                  // setSelectedTab(7), expandAction(7),
                  CommonStore.update((s) => {
                    s.currPage = 'AllTransaction';
                    s.currPageStack = [...currPageStack, 'AllTransaction'];
                  });

                  if (expandTab !== EXPAND_TAB_TYPE.TRANSACTIONS) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.TRANSACTIONS;
                    });
                  }

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_TRANSACTION_ALL_T,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_TRANSACTION_ALL_T,
                  });
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === 'AllTransaction' && highlightStyle),
                  },
                ]}>
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == 'AllTransaction'
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9, height: '150%' } : {},
                  ]}>{`All\nTransactions`}</Text>
              </TouchableOpacity>
            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.REPORT))
        && privileges.includes(PRIVILEGES_NAME.REPORT)) && <View style={sideBarItemsContainerStyleScale}>
          {/* <TouchableOpacity */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandReport: !expandReport, selectedTab: 8 }),
              // setExpandReport(!expandReport);
              // setSelectedTab(8);
              // expandAction(8)

              if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.REPORT;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_REPORT,
                eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.REPORT ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <ReportG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Report
                    </Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <View style={{ transform: [{ rotate: '90deg' }], width: 18 }}>
                                        <Arrow width={arrowSize} height={arrowSize} />
                                    </View>
                                </View> */}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Report
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Report</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <Arrow width={arrowSize} height={arrowSize} />
                                </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>
          {/* </TouchableOpacity> */}
          {expandTab == EXPAND_TAB_TYPE.REPORT ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab === EXPAND_TAB_TYPE.REPORT
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('Dashboard') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Dashboard');
                  }}
                  onPress={() => {
                    navigation.navigate('Dashboard');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'Dashboard';
                      s.currPageStack = [...currPageStack, 'Dashboard'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_DASHBOARD,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_DASHBOARD,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Dashboard' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Dashboard'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Dashboard
                  </Text>
                </TouchableOpacity>
                :
                <></>}
              {/* <TouchableOpacity
                            onPress={() => { navigation.navigate("ReportStockValue"), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Stock Value</Text>
                        </TouchableOpacity> */}

              {/* {!screensToBlock.includes('Analysis_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('ReportSalesAnalysis'),
                      // setSelectedTab(8), expandAction(8),
                      CommonStore.update((s) => {
                        s.currPage = 'ReportSalesAnalysis';
                        s.currPageStack = [...currPageStack, 'ReportSalesAnalysis'];
                      });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_ANALYSIS,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_ANALYSIS,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesAnalysis' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesAnalysis'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Analysis
                  </Text>
                </TouchableOpacity>
                : <></>} */}

              {!screensToBlock.includes('Aov_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesAov');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesAov')
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesAov';
                      s.currPageStack = [...currPageStack, 'ReportSalesAov'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_AOV,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_AOV,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesAov' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesAov'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    AOV
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Order_Count_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesOrderCount');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesOrderCount');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesOrderCount';
                      s.currPageStack = [...currPageStack, 'ReportSalesOrderCount'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_ORDER_COUNT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_ORDER_COUNT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesOrderCount' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesOrderCount'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    {'Order\nCount'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Revisit_Count_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesRevisitCount');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesRevisitCount');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesRevisitCount';
                      s.currPageStack = [...currPageStack, 'ReportSalesRevisitCount'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_REVISIT_COUNT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_REVISIT_COUNT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesRevisitCount' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesRevisitCount'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    {'Revisit\nCount'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Overview_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesOvertime');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesOvertime');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesOvertime';
                      s.currPageStack = [...currPageStack, 'ReportSalesOvertime'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_OVERVIEW,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_OVERVIEW,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesOvertime' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesOvertime'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Overview
                  </Text>
                </TouchableOpacity>
                : <></>}

              {(!screensToBlock.includes('Revisit_Report')) ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesRevisit');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesRevisit');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesRevisit';
                      s.currPageStack = [...currPageStack, 'ReportSalesRevisit'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_OVERVIEW,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_OVERVIEW,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesRevisit' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesRevisit'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Revisit
                  </Text>
                </TouchableOpacity>
                : <></>}

              {(!screensToBlock.includes('Upselling_Report')) ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesUpselling');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesUpselling');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesUpselling';
                      s.currPageStack = [...currPageStack, 'ReportSalesUpselling'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_UPSELLING,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_UPSELLING,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesUpselling' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesUpselling'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Upselling
                  </Text>
                </TouchableOpacity>
                : <></>}

              {/* 2025-05-21 hide product and category since got product & category */}
              {/* {!screensToBlock.includes('Product_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesProduct');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesProduct');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesProduct';
                      s.currPageStack = [...currPageStack, 'ReportSalesProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_PRODUCT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_PRODUCT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesProduct'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Product
                  </Text>
                </TouchableOpacity>
                : <></>} */}

              {/* {!screensToBlock.includes('Category_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesCategory');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesCategory');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesCategory';
                      s.currPageStack = [...currPageStack, 'ReportSalesCategory'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_CATEGORY,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CATEGORY,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesCategory' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesCategory'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Category
                  </Text>
                </TouchableOpacity>
                : <></>} */}

              {!screensToBlock.includes('Category_Product_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportCategoryProduct');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportCategoryProduct');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportCategoryProduct';
                      s.currPageStack = [...currPageStack, 'ReportCategoryProduct'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_CATEGORY_PRODUCT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CATEGORY_PRODUCT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportCategoryProduct' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportCategoryProduct'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    {`Product &\nCategory`}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Variant_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesVariant');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesVariant');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesVariant';
                      s.currPageStack = [...currPageStack, 'ReportSalesVariant'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_VARIANT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_VARIANT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesVariant' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesVariant'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Variants
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Add_On_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesAddOns');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesAddOns');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesAddOns';
                      s.currPageStack = [...currPageStack, 'ReportSalesAddOns'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_ADDON,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_ADDON,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesAddOns' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesAddOns'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Add-Ons
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* <TouchableOpacity
                            onPress={() => { navigation.navigate('ReportSalesSKU'), setSelectedTab(8), expandAction(8) }}
                            style={[styles.subBar,]}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>SKU</Text>
                        </TouchableOpacity> */}
              {!screensToBlock.includes('Channel_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesTransaction');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesTransaction');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesTransaction';
                      s.currPageStack = [
                        ...currPageStack,
                        'ReportSalesTransaction',
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_CHANNEL,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_CHANNEL,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesTransaction' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesTransaction'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    {'Channel'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Payment_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesPaymentMethod');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesPaymentMethod');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesPaymentMethod';
                      s.currPageStack = [
                        ...currPageStack,
                        'ReportSalesPaymentMethod',
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_PAYMENT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_PAYMENT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesPaymentMethod' &&
                        highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesPaymentMethod'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Payment
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate('ReportSalesByRedemption'), setSelectedTab(8), expandAction(8), CommonStore.update(s => {
                                    s.currPage = 'ReportSalesByRedemption';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'ReportSalesByRedemption') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "ReportSalesByRedemption" ? Colors.primaryColor : Colors.descriptionColor }]}>Redemption</Text>
                        </TouchableOpacity> */}
              {!screensToBlock.includes('Shift_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSaleByShift');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSaleByShift');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSaleByShift';
                      s.currPageStack = [...currPageStack, 'ReportSaleByShift'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_SHIFT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_SHIFT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSaleByShift' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSaleByShift'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Shift
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('PayIn_Out_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportShiftPayInOut');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportShiftPayInOut');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportShiftPayInOut';
                      s.currPageStack = [...currPageStack, 'ReportShiftPayInOut'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_PAY_IN_OUT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_PAY_IN_OUT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportShiftPayInOut' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportShiftPayInOut'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Pay In/Out
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Refund_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportSalesRefund');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportSalesRefund');
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportSalesRefund';
                      s.currPageStack = [...currPageStack, 'ReportSalesRefund'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_REPORT_REFUND,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_REPORT_REFUND,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportSalesRefund' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportSalesRefund'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Refund
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* <TouchableOpacity
                            onPress={() => { navigation.navigate('AllTransaction', {selectedTab: 8}), setSelectedTab(8), expandAction(8) }}
                            style={[styles.subBar, ]}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>All Transactions</Text>
                        </TouchableOpacity> */}
              {/* <TouchableOpacity
                            onPress={() => { navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Promotions report</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Stock Value</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Customer payment owing {'\n'}report</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Commission /overriding</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Loyalty Points</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Package/credit purchase & {'\n'}redemption</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>History</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { setState({ expandSales: true }) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Sales</Text>
                        </TouchableOpacity> */}
              {/* {expandSales == true ? (
                            <View>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('ReportSalesOvertime'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Overtime</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('ReportSalesProduct'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Product</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('Inventory'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Inventory</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('ReportSalesCategory'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Category / Tags</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('ReportSalesVariant'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Variants</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('ReportSalesSKU'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>SKU</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('ReportSalesTransaction'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Transaction</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('ReportSalesPaymentMethod'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Payment Method</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('ReportSaleByShift'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Shift</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { navigation.navigate('AllTransaction'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: windowWidth * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>All Transactions</Text>
                                </TouchableOpacity>
                            </View>

                        ) : null} */}
            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.EMPLOYEES))
        && privileges.includes(PRIVILEGES_NAME.EMPLOYEES)) && <View style={sideBarItemsContainerStyleScale}>
          {/* <TouchableOpacity */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandEmployees: !expandEmployees, selectedTab: 9 }),
              // setExpandEmployees(!expandEmployees);
              // setSelectedTab(9);
              // expandAction(9)

              if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_EMPLOYEE,
                eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.EMPLOYEES ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <EmployeesG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Employees
                    </Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                                        <Arrow width={arrowSize} height={arrowSize} />
                                    </View>
                                </View> */}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Employees
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Employees</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <Arrow width={arrowSize} height={arrowSize} />
                                </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.EMPLOYEES ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab === EXPAND_TAB_TYPE.EMPLOYEES
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('Manage_Employee') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Employee');
                  }}
                  onPress={() => {
                    navigation.navigate('Employee');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'Employee';
                      s.currPageStack = [...currPageStack, 'Employee'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_EMPLOYEE_MANAGE_E,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_MANAGE_E,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Employee' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Employee'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Manage\nEmployee`}</Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Active_Log') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'ReportActivityLog');
                  }}
                  onPress={() => {
                    navigation.navigate('ReportActivityLog');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'ReportActivityLog';
                      s.currPageStack = [...currPageStack, 'ReportActivityLog'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_EMPLOYEE_ACTIVITY_LOG,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_ACTIVITY_LOG,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'ReportActivityLog' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'ReportActivityLog'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Activity\nLog`}</Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Employee_Timesheet') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'EmployeeTimeSheet');
                  }}
                  onPress={() => {
                    navigation.navigate('EmployeeTimeSheet');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'EmployeeTimeSheet';
                      s.currPageStack = [...currPageStack, 'EmployeeTimeSheet'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'EmployeeTimeSheet' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'EmployeeTimeSheet'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Employee\nTimesheet`}</Text>
                </TouchableOpacity>
                : <></>}
              {/* {!screensToBlock.includes('Employee_Performance_Report') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'EmployeePerformanceReport');
                  }}
                  onPress={() => {
                    props.navigation.navigate('EmployeePerformanceReport');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'EmployeePerformanceReport';
                      s.currPageStack = [...currPageStack, 'EmployeePerformanceReport'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'EmployeePerformanceReport' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'EmployeePerformanceReport'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Performance\nReport`}</Text>
                </TouchableOpacity>
                : <></>} */}
            </View>
          ) : null}
        </View>}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.SETTINGS)) &&
        privileges.includes(PRIVILEGES_NAME.SETTINGS))
        ?
        <View style={sideBarItemsContainerStyleScale}>
          {/* <TouchableOpacity */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandSettings: !expandSettings, selectedTab: 10 }),
              // setExpandSettings(!expandSettings);
              // setSelectedTab(10);
              // expandAction(10)

              if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_SETTINGS,
                eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS,
              });
            }}
            style={sideBarItemsStyleScale}>
            {expandTab === EXPAND_TAB_TYPE.SETTINGS ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <SettingsG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Settings
                    </Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                                        <Arrow width={arrowSize} height={arrowSize} />
                                    </View>
                                </View> */}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: 'Colors.lightPrimary',
                      height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                        isTablet()
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                      width: isTablet()
                        ? windowWidth * Styles.sideBarWidth
                        : '100%',
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}>
                  <View style={sidebarIconStyleScale}>
                    <Settings
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Settings</Text>
                  </View>
                  {/* <View style={[styles.sidebarArrow]}>
                                    <Arrow width={arrowSize} height={arrowSize} />
                                </View> */}
                </View>
              </View>
            )}
          </TouchableHighlight>
          {/* </TouchableOpacity> */}
          {expandTab === EXPAND_TAB_TYPE.SETTINGS ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab === EXPAND_TAB_TYPE.SETTINGS
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>
              {!screensToBlock.includes('General_Setting') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Setting');
                  }}
                  onPress={() => {
                    navigation.navigate('Setting');
                    // setSelectedTab(10), expandAction(10),
                    CommonStore.update((s) => {
                      s.currPage = 'Setting';
                      s.currPageStack = [...currPageStack, 'Setting'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_SETTINGS_GENERAL,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_GENERAL,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Setting' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Setting'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    General
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("GlobalCreditScreen"); setSelectedTab(10), expandAction(10), CommonStore.update(s => {
                                    s.currPage = 'GlobalCreditScreen';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'GlobalCreditScreen') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "GlobalCreditScreen" ? Colors.primaryColor : Colors.descriptionColor }]}>Global Credit</Text>
                        </TouchableOpacity> */}

              {console.log('screensToBlock')}
              {console.log(screensToBlock)}

              {!screensToBlock.includes('Shift_Setting') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'SettingShift');
                  }}
                  onPress={() => {
                    navigation.navigate('SettingShift');
                    // setSelectedTab(10), expandAction(10),
                    CommonStore.update((s) => {
                      s.currPage = 'SettingShift';
                      s.currPageStack = [...currPageStack, 'SettingShift'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_SETTINGS_SHIFT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_SHIFT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'SettingShift' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'SettingShift'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Shift
                  </Text>
                </TouchableOpacity>
                : <></>}
              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("SettingTax"); setSelectedTab(10), expandAction(10), CommonStore.update(s => {
                                    // s.currPage = 'SettingTax';
                                    s.currPageStack = [
                                        ...currPageStack,
                                        'SettingTax',
                                    ];
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'SettingTax') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "SettingTax" ? Colors.primaryColor : Colors.descriptionColor }]}>Tax</Text>
                        </TouchableOpacity> */}
              {!screensToBlock.includes('Receipt_Setting') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'SettingReceipt');
                  }}
                  onPress={() => {
                    navigation.navigate('SettingReceipt');
                    // setSelectedTab(10), expandAction(10),
                    CommonStore.update((s) => {
                      s.currPage = 'SettingReceipt';
                      s.currPageStack = [...currPageStack, 'SettingReceipt'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_SETTINGS_RECEIPT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_RECEIPT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'SettingReceipt' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'SettingReceipt'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Receipt
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Order_Setting') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'SettingOrder');
                  }}
                  onPress={() => {
                    navigation.navigate('SettingOrder');
                    // setSelectedTab(10), expandAction(10),
                    CommonStore.update((s) => {
                      s.currPage = 'SettingOrder';
                      s.currPageStack = [...currPageStack, 'SettingOrder'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_SETTINGS_ORDER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_ORDER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'SettingOrder' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'SettingOrder'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Order
                  </Text>
                </TouchableOpacity>
                : <></>}
              {!screensToBlock.includes('Printer_Setting') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'SettingPrinter');
                  }}
                  onPress={() => {
                    navigation.navigate('SettingPrinter');
                    CommonStore.update((s) => {
                      s.currPage = 'SettingPrinter';
                      s.currPageStack = [...currPageStack, 'SettingPrinter'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_SETTINGS_PRINTER,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_PRINTER,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'SettingPrinter' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'SettingPrinter'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Printer
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Payment_Setting') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'SettingPayment');
                  }}
                  onPress={() => {
                    // scrollHandler('SettingPayment');

                    navigation.navigate('SettingPayment');
                    CommonStore.update((s) => {
                      s.currPage = 'SettingPayment';
                      s.currPageStack = [...currPageStack, 'SettingPayment'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_SETTINGS_PAYMENT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_PAYMENT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'SettingPayment' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'SettingPayment'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Payment
                  </Text>
                </TouchableOpacity>
                : <></>}

              {
                !screensToBlock.includes('Grab_Setting') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'SettingGrab');
                    }}
                    onPress={() => {
                      // scrollHandler('SettingPayment');
                      navigation.navigate('SettingGrab');
                      CommonStore.update((s) => {
                        s.currPage = 'SettingGrab';
                        s.currPageStack = [...currPageStack, 'SettingGrab'];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'SettingGrab' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'SettingGrab'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}>
                      Grab
                    </Text >
                  </TouchableOpacity >
                  : <></>}

              {
                !screensToBlock.includes('Easy_Parcel') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'EasyParcel');
                    }}
                    onPress={() => {
                      // scrollHandler('SettingPayment');

                      navigation.navigate('EasyParcel');
                      CommonStore.update((s) => {
                        s.currPage = 'EasyParcel';
                        s.currPageStack = [...currPageStack, 'EasyParcel'];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                        });
                      }

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_SETTINGS_PAYMENT,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_PAYMENT,
                      });
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'EasyParcel' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'EasyParcel'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}>
                      Easy Parcel
                    </Text>
                  </TouchableOpacity>
                  : <></>}


              {/* {!screensToBlock.includes('Fulfillment') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'Fulfillment');
                  }}
                  onPress={() => {
                    // scrollHandler('SettingPayment');

                    props.navigation.navigate('Fulfillment');
                    CommonStore.update((s) => {
                      s.currPage = 'Fulfillment';
                      s.currPageStack = [...currPageStack, 'Fulfillment'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_SETTINGS_PAYMENT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_SETTINGS_PAYMENT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Fulfillment' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Fulfillment'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Fulfillment
                  </Text>
                </TouchableOpacity>
                : <></>} */}

              {/* {!screensToBlock.includes('Live_Selling') ?
                <TouchableOpacity
                  onLayout={(e) => {
                    layoutHandler(e, 'LiveSelling');
                  }}
                  onPress={() => {
                    navigation.navigate('LiveSelling');
                    CommonStore.update((s) => {
                      s.currPage = 'LiveSelling';
                      s.currPageStack = [...currPageStack, 'LiveSelling'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'LiveSelling' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'LiveSelling'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    {'Live Selling'}
                  </Text>
                </TouchableOpacity>
                : <></>} */}

              {/* <TouchableOpacity
                onPress={() => {
                  navigation.navigate('SettingReservation');
                  CommonStore.update((s) => {
                    s.currPage = 'SettingReservation';
                    s.currPageStack = [...currPageStack, 'SettingReservation'];
                  });

                  if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                    });
                  }
                }}
                style={[
                  expandTab === EXPAND_TAB_TYPE.SETTINGS
                    ? Colors.lightPrimary
                    : null,
                  styles.subBar,
                  {
                    ...(currPage === 'SettingReservation' && highlightStyle),
                  },
                ]}>
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == 'SettingReservation'
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9 } : {},
                  ]}>
                  Reservation
                </Text>
              </TouchableOpacity> */}
              {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('NewSettingsScreen');
                // setSelectedTab(9), expandAction(9),
                CommonStore.update((s) => {
                  s.currPage = 'NewSettingsScreen';
                  s.currPageStack = [...currPageStack, 'NewSettingsScreen'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'NewSettingsScreen' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'NewSettingsScreen'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? {fontSize: 9, height: '150%'} : {},
                ]}>{`New\nSettings`}</Text>
            </TouchableOpacity> */}
              {
                !screensToBlock.includes('Barcode_Setting') ?
                  <TouchableOpacity
                    onLayout={(e) => {
                      layoutHandler(e, 'SettingBarcode');
                    }}
                    onPress={() => {
                      navigation.navigate('SettingBarcode');
                      // setSelectedTab(10), expandAction(10),
                      CommonStore.update((s) => {
                        s.currPage = 'SettingBarcode';
                        s.currPageStack = [...currPageStack, 'SettingBarcode'];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                        });
                      }


                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'SettingBarcode' && highlightStyle),
                      },
                    ]}>
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'SettingBarcode'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}>
                      Barcode
                    </Text>
                  </TouchableOpacity>
                  : <></>
              }
              {/* <TouchableOpacity
              onPress={() => {
                navigation.navigate('SettingLoyalty');
                CommonStore.update((s) => {
                  s.currPage = 'SettingLoyalty';
                  s.currPageStack = [...currPageStack, 'SettingLoyalty'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === 'SettingLoyalty' && highlightStyle),
                },
              ]}>
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == 'SettingLoyalty'
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? {fontSize: 9} : {},
                ]}>
                Loyalty
              </Text>
            </TouchableOpacity> */}

              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("SettingE-indemnityForm"); setSelectedTab(10), expandAction(10), CommonStore.update(s => {
                                    s.currPage = 'SettingE-indemnityForm';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'SettingE-indemnityForm') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "SettingE-indemnityForm" ? Colors.primaryColor : Colors.descriptionColor }]}>E-indemnity Form</Text>
                        </TouchableOpacity> */}
              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("SettingQuestionaire"); setSelectedTab(10), expandAction(10), CommonStore.update(s => {
                                    s.currPage = 'SettingQuestionaire';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'SettingQuestionaire') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "SettingQuestionaire" ? Colors.primaryColor : Colors.descriptionColor }]}>Questionaire</Text>
                        </TouchableOpacity> */}
              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("SettingCredit"); setSelectedTab(10), expandAction(10), CommonStore.update(s => {
                                    s.currPage = 'SettingCredit';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'SettingCredit') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "SettingCredit" ? Colors.primaryColor : Colors.descriptionColor }]}>Loyalty Points</Text>
                        </TouchableOpacity> */}
              {/* TakeOutFirst! */}
              {/* <TouchableOpacity
                            onPress={() => { navigation.navigate("SettingBooking"); setSelectedTab(10), expandAction(10), CommonStore.update(s => {
                                    s.currPage = 'SettingBooking';
                                }); }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Booking</Text>
                        </TouchableOpacity> */}

              {/* <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("SettingNotification"); setSelectedTab(10), expandAction(10), CommonStore.update(s => {
                                    s.currPage = 'SettingNotification';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'SettingNotification') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "SettingNotification" ? Colors.primaryColor : Colors.descriptionColor }]}>Notification</Text>
                        </TouchableOpacity> */}
            </View >
          ) : null}
        </View >
        :
        <></>}
      <View style={sideBarItemsContainerStyleScale}>
        <TouchableOpacity
          underlayColor={Colors.lightPrimary}
          style={[sideBarItemsStyleScale, {}]}
          onPress={() => {
            CommonStore.update((s) => {
              s.chatbotModalVisibility = true;
            });

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_SUPPORT,
              eventNameParsed: ANALYTICS_PARSED.MODULE_SUPPORT,
            });
          }}>
          <View
            style={[
              styles.sidebarView,
              {
                backgroundColor: 'Colors.lightPrimary',
                height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                  isTablet()
                    ? windowHeight * 0.08
                    : windowHeight * 0.16,
                width: isTablet()
                  ? windowWidth * Styles.sideBarWidth
                  : '100%',
              },
            ]}>
            <View style={sidebarIconStyleScale}>
              <MaterialIcons
                name="support-agent"
                size={
                  switchMerchant
                    ? windowWidth * 0.02
                    : windowWidth * iconSize
                }
                color={Colors.descriptionColor}
              />
            </View>
            <View style={styles.sidebarText}>
              <Text style={[sidebarTextStyleScale,]}>
                Support
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
      <View style={sideBarItemsContainerStyleScale}>
        {/* <TouchableOpacity */}
        <TouchableHighlight
          underlayColor={Colors.lightPrimary}
          style={[sideBarItemsStyleScale, {}]}
          onPress={async () => {

            // User.setlogin(false);
            // User.getRefreshMainScreen();
            if (currOutlet.forceCloseShiftBeforeSignOut === true && currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
              Alert.alert('Info', 'Please close the shift first before sign out or log out.')
            }
            else {
              signOutButton();
            }
          }}>
          <View
            style={[
              styles.sidebarView,
              {
                backgroundColor: 'Colors.lightPrimary',
                height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                  isTablet()
                    ? windowHeight * 0.08
                    : windowHeight * 0.16,
                width: isTablet()
                  ? windowWidth * Styles.sideBarWidth
                  : '100%',
              },
              switchMerchant
                ? {
                  // borderWidth: 1,
                  // width: '48%'
                }
                : {},
            ]}>
            <View style={sidebarIconStyleScale}>
              <FontAwesome5
                name="exchange-alt"
                size={
                  switchMerchant
                    ? windowWidth * 0.02
                    : windowWidth * iconSize
                }
                color={Colors.descriptionColor}
                style={{
                  opacity: 0.75,
                }}
              />
            </View>
            <View style={styles.sidebarText}>
              <Text style={[sidebarTextStyleScale, {
                // textAlign: 'center'
              }]}>{'Sign Out'}</Text>
            </View>
          </View>
        </TouchableHighlight>
        {/* </TouchableOpacity> */}
      </View>
      {/* <View style={sideBarItemsContainerStyleScale}>
        <TouchableHighlight
          underlayColor={Colors.lightPrimary}
          style={[sideBarItemsStyleScale, {}]}
          onPress={async () => {

            // User.setlogin(false);
            // User.getRefreshMainScreen();
            if (currOutlet.forceCloseShiftBeforeSignOut === true && currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
              Alert.alert('Info', 'Please close the shift first before sign out or log out.')
            }
            else {
              logOutButton();
            }
          }}>
          <View
            style={[
              styles.sidebarView,
              {
                backgroundColor: 'Colors.lightPrimary',
                height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.12 :
                  isTablet()
                    ? windowHeight * 0.08
                    : windowHeight * 0.16,
                width: isTablet()
                  ? windowWidth * Styles.sideBarWidth
                  : '100%',
              },
              switchMerchant
                ? {
                  // borderWidth: 1,
                  // width: '48%'
                }
                : {},
            ]}>
            <View style={sidebarIconStyleScale}>
              <MaterialIcons
                name="logout"
                size={
                  switchMerchant
                    ? windowWidth * 0.02
                    : windowWidth * iconSize
                }
                color={Colors.descriptionColor}
              />
            </View>
            <View style={styles.sidebarText}>
              <Text style={[sidebarTextStyleScale, {
                // textAlign: 'center'
              }]}>{'Logout'}</Text>
            </View>
          </View>
        </TouchableHighlight>
      </View> */}

    </ScrollView >
  );
});

var styles = StyleSheet.create({
  icon: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  scrollView: {
    backgroundColor: Colors.whiteColor,
    // flex: 1,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,

    elevation: 3,
    // width: windowWidth * 0.163,
    // width: windowWidth * 0.15,
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
  },
  menuIcon: {
    width: 20,
    height: 20,
    alignSelf: 'center',
    marginBottom: 10,
  },
  sidebarItems: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#6e6e6e',
  },
  //sidebarView include dashboard
  sidebarView: {
    alignItems: 'center',
    height: isTablet() && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? Dimensions.get('screen').height * 0.12 :
      isTablet()
        ? Dimensions.get('screen').height * 0.08
        : Dimensions.get('screen').height * 0.16,
    // flexDirection: 'row',
    // paddingLeft: 16,
    // width: windowWidth * 0.155,
    width: isTablet()
      ? Dimensions.get('screen').width * Styles.sideBarWidth
      : '100%',

    justifyContent: 'center',
  },
  sidebarIcon: {
    flex: 1,
  },
  sidebarText: {
    // flex: 2,
    marginTop: 3,
  },
  sidebarArrow: {
    flex: 0.5,
    alignItems: 'flex-start',

    display: 'none',
  },
  sidebarTextStyle: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 15,
    color: Colors.descriptionColor,
  },
  expandedItems: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 14,
    color: Colors.descriptionColor,
    marginLeft: Dimensions.get('screen').width * 0.01,

    // width: '60%',
    width: Dimensions.get('screen').width * 0.075,
    // width: '20%',
    //height: windowHeight * 0.05,
    marginLeft: -Dimensions.get('screen').width * 0.001,
    textAlign: 'center',
    // backgroundColor: 'red',
    // justifyContent: 'center',
    // textAlign: 'center',
    alignSelf: 'center',
    // paddingVertical: 10,
  },
  subBar: {
    // left: windowWidth * 0.01,
    // height: 35,
    justifyContent: 'center',
    // flexWrap: 'wrap',
    // height: isTablet() ? windowHeight * 0.05 : windowHeight * 0.1,
    ...(!isTablet() && {
      height: Dimensions.get('screen').height * 0.1,
    }),
    alignItems: 'center',
    paddingVertical: 10,
  },

  // quantity badge style (JJ's comment)
  quantityContainer: {
    position: 'relative',
  },
  badgeContainer: {
    position: 'absolute',
    top: -10,
    right: -18,
    backgroundColor: 'red',
    borderRadius: 15,
    height: 18,
    width: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 8,
    fontWeight: 'bold',
  },
});

export default SideBar;