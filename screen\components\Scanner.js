import React, { useState, useEffect } from 'react';
import { View, Text, Alert, StyleSheet, TouchableOpacity, PermissionsAndroid, Platform } from 'react-native';
import { Camera, CameraType } from 'react-native-camera-kit';

const Scanner = ({ onScanned, frameColor = 'white', laserColor = 'red' }) => {
    const [hasPermission, setHasPermission] = useState(false);
    const [isScanning, setIsScanning] = useState(true);
    const [barcodeValue, setBarcodeValue] = useState('');

    // Request permissions on mount
    useEffect(() => {
        const requestPermission = async () => {
            if (Platform.OS === 'android') {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.CAMERA,
                    {
                        title: 'Camera Permission',
                        message: 'This app needs access to your camera for barcode scanning.',
                        buttonNeutral: 'Ask Me Later',
                        buttonNegative: 'Cancel',
                        buttonPositive: 'OK',
                    }
                );
                setHasPermission(granted === PermissionsAndroid.RESULTS.GRANTED);
            } else {
                setHasPermission(true); // iOS auto-handles
            }
        };
        requestPermission();
    }, []);

    // Handle barcode read
    const handleBarcodeRead = (event) => {
        const code = event.nativeEvent.codeStringValue;

        if (code === barcodeValue) return; // ignore same code
        setBarcodeValue(code);
        setIsScanning(false);
        onScanned?.(code);

        console.log(event.nativeEvent.codeStringValue);
        console.log(code);
        console.log('barcode here');

        Alert.alert('Barcode scanned', code, [
            { text: 'OK', onPress: () => setIsScanning(true) }
        ]);
    };

    if (!hasPermission) {
        return (
            <View style={styles.centered}>
                <Text style={styles.permissionText}>No camera permission</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            {barcodeValue ? (
                <View style={styles.resultContainer}>
                    <Text style={styles.resultText}>Scanned Code: {barcodeValue}</Text>
                    <TouchableOpacity
                        style={styles.button}
                        onPress={() => {
                            setBarcodeValue('');
                            setIsScanning(true);
                        }}
                    >
                        <Text style={styles.buttonText}>Scan Again</Text>
                    </TouchableOpacity>
                </View>
            ) : (
                <Camera
                    cameraType={CameraType.Back}
                    style={styles.camera}
                    scanBarcode={true}
                    onReadCode={handleBarcodeRead}
                    onError={(error) => {
                        console.log('Camera error:', error);
                        Alert.alert('Info', 'Camera error detect');
                    }}
                    showFrame={true}
                    laserColor={laserColor}
                    frameColor={frameColor}
                    cameraOptions={{
                        useCamera2Api: true, // 对部分 Android 设备提升兼容性
                    }}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: '#000' },
    camera: { flex: 1 },
    centered: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    permissionText: { fontSize: 16, color: 'white' },
    resultContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    resultText: { fontSize: 18, marginBottom: 20, color: 'white' },
    button: {
        paddingHorizontal: 20,
        paddingVertical: 10,
        backgroundColor: '#1e90ff',
        borderRadius: 8
    },
    buttonText: { color: 'white', fontSize: 16 },
});

export default Scanner;