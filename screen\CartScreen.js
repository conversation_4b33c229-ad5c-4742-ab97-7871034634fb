import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  TextInput,
  RefreshControl,
  Alert,
  Modal as ModalComponent,
  Dimensions,
  ActivityIndicator,
  Platform,
  useWindowDimensions,
  InteractionManager,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import SideBar from './SideBar';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
import Entypo from 'react-native-vector-icons/Entypo';
import Close from 'react-native-vector-icons/AntDesign';
import AntDesign from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import Back from 'react-native-vector-icons/EvilIcons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  checkIsAllowPromotionVoucherToApply,
  isTablet,
  checkQualifiedItemsQuantityAndAmountForPromotion,
  getTransformForScreenInsideNavigation,
  checkRPQtyStatus,
  checkToApplyTaxOrNot,
  checkToApplyScOrNot,
} from '../util/common';
import { CommonStore } from '../store/commonStore';
import { ORDER_TYPE, CHARGES_TYPE, EXPAND_TAB_TYPE, APP_TYPE, PRODUCT_PRICE_TYPE, UNIT_TYPE, UNIT_TYPE_SHORT, ORDER_TYPE_DETAILS, ORDER_TYPE_SUB, DISCOUNT_SEQUENCE_TYPES, } from '../constant/common';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
import { OutletStore } from '../store/outletStore';
import { APPLY_BEFORE, APPLY_DISCOUNT_PER, APPLY_DISCOUNT_TYPE, PROMOTION_TYPE_VARIATION } from '../constant/promotions';
import { useNetInfo } from "@react-native-community/netinfo";
import APILocal from '../util/apiLocalReplacers';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import { printUserOrder } from '../util/printer';
import DropDownPicker from 'react-native-dropdown-picker';
import { checkApplyDiscountPerValidity } from '../util/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import BigNumber from 'bignumber.js';
// import Hashids from 'hashids';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

// const hashids = new Hashids(TABLE_QR_SALT);

const CartScreen = React.memo((props) => {
  const { navigation, route } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const netInfo = useNetInfo();

  const testParam = route.params.test;
  const test1Param = route.params.test1;
  const test2Param = route.params.test2;
  const paymentMethodParam = route.params.paymentMethod;
  const outletDataParam = route.params.outletData;
  const navFromParam = route.params.navFrom;
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [paymentMethod, setPaymentMethod] = useState(paymentMethodParam);
  const [cartItem, setCartItem] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [value, setValue] = useState('');
  const [editingItemId, setEditingItemId] = useState(null);
  const [outletData, setOutletData] = useState({});
  const [menu, setMenu] = useState([]);
  const [menuItem, setMenuItem] = useState([]);
  const [category, setCategory] = useState([]);
  const [outletMenu, setOutletMenu] = useState([]);
  const [menuItemDetails, setMenuItemDetails] = useState([]);
  const [qty, setQty] = useState([]);
  const [test, setTest] = useState(testParam);
  const [test1, setTest1] = useState(test1Param);
  const [test2, setTest2] = useState(test2Param);
  const [popularOutlet, setPopularOutlet] = useState([]);
  const [popular, setPopular] = useState([]);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [currentMenu, setCurrentMenu] = useState([]);
  const [type, setType] = useState(Cart.getOrderType()); // 0 "Dine In";
  const [deliveryAddress, setDeliveryAddress] = useState(null);
  const [totalFloat, setTotalFloat] = useState(0);
  const [taxFloat, setTaxFloat] = useState(0);

  const [promotionIdAppliedList, setPromotionIdAppliedList] = useState([]);

  const [totalPrice, setTotalPrice] = useState(0);
  const [totalTax, setTotalTax] = useState(0);
  const [totalSc, setTotalSc] = useState(0);
  const [totalDiscount, setTotalDiscount] = useState(0);
  const [discountPromotionsTotal, setDiscountPromotionsTotal] = useState(0);

  const [totalPrepareTime, setTotalPrepareTime] = useState(0);

  const [isCartLoading, setIsCartLoading] = useState(false);

  const [promoCodePromotionDropdownList, setPromoCodePromotionDropdownList] = useState([]);
  const [selectedPromoCodePromotionId, setSelectedPromoCodePromotionId] = useState('');

  const selectedPromoCodePromotion = CommonStore.useState(s => s.selectedPromoCodePromotion);

  const allOutletsItemAddOnIdDict = CommonStore.useState((s) => s.allOutletsItemAddOnIdDict);
  const allOutletsItemAddOnChoiceIdDict = CommonStore.useState((s) => s.allOutletsItemAddOnChoiceIdDict);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const cartItems = CommonStore.useState((s) => s.cartItems);
  const cartOutletItemsDict = CommonStore.useState(
    (s) => s.cartOutletItemsDict,
  );
  const cartOutletItemAddOnDict = CommonStore.useState(
    (s) => s.cartOutletItemAddOnDict,
  );
  const cartOutletItemAddOnChoiceDict = CommonStore.useState(
    (s) => s.cartOutletItemAddOnChoiceDict,
  );

  const selectedOutletTable = CommonStore.useState(
    (s) => s.selectedOutletTable,
  );
  const userCart = CommonStore.useState((s) => s.userCart);

  const cartItemsProcessed = CommonStore.useState((s) => s.cartItemsProcessed);

  const outletsTaxDict = CommonStore.useState((s) => s.outletsTaxDict);

  const orderType = CommonStore.useState((s) => s.orderType);

  const orderTypeSub = CommonStore.useState((s) => s.orderTypeSub);

  const selectedUserAddress = UserStore.useState((s) => s.selectedUserAddress);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const role = UserStore.useState((s) => s.role)

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const name = UserStore.useState((s) => s.name);

  // const name = MerchantStore.useState((s) => s.merchantId);

  const userName = UserStore.useState(s => s.name);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const availablePromoCodePromotions = CommonStore.useState((s) => s.availablePromoCodePromotions);

  // const promotions = OutletStore.useState((s) => s.promotions);
  const promotionsDict = OutletStore.useState((s) => s.promotionsDict);

  const outletItemsDict = OutletStore.useState((s) => s.outletItemsDict);

  const outletItems = OutletStore.useState((s) => s.outletItems);

  const currTableQRUrl = OutletStore.useState((s) => s.currTableQRUrl);

  const [showSuccess, setShowSuccess] = useState(false);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  /////////////////////////////////////////////////////////

  const overrideItemPriceSkuDict = CommonStore.useState(
    (s) => s.overrideItemPriceSkuDict,
  );
  const amountOffItemSkuDict = CommonStore.useState(
    (s) => s.amountOffItemSkuDict,
  );
  const percentageOffItemSkuDict = CommonStore.useState(
    (s) => s.percentageOffItemSkuDict,
  );
  const buy1Free1ItemSkuDict = CommonStore.useState(
    (s) => s.buy1Free1ItemSkuDict,
  );
  const deliveryItemSkuDict = CommonStore.useState(
    (s) => s.deliveryItemSkuDict,
  );
  const takeawayItemSkuDict = CommonStore.useState(
    (s) => s.takeawayItemSkuDict,
  );

  const overrideCategoryPriceNameDict = CommonStore.useState(
    (s) => s.overrideCategoryPriceNameDict,
  );
  const amountOffCategoryNameDict = CommonStore.useState(
    (s) => s.amountOffCategoryNameDict,
  );
  const percentageOffCategoryNameDict = CommonStore.useState(
    (s) => s.percentageOffCategoryNameDict,
  );
  const buy1Free1CategoryNameDict = CommonStore.useState(
    (s) => s.buy1Free1CategoryNameDict,
  );
  const deliveryCategoryNameDict = CommonStore.useState(
    (s) => s.deliveryCategoryNameDict,
  );
  const takeawayCategoryNameDict = CommonStore.useState(
    (s) => s.takeawayCategoryNameDict,
  );

  const selectedOutletItemCategoriesDict = OutletStore.useState(s => s.outletCategoriesDict);

  const selectedOutletItemsSkuDict = OutletStore.useState(s => s.outletItemsSkuDict);

  const isCounterOrdering = CommonStore.useState(s => s.isCounterOrdering);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  /////////////////////////////////////////////////////////

  const cartItemsTableIdDict = CommonStore.useState(s => s.cartItemsTableIdDict);

  /////////////////////////////////////////////////////////

  const [takeawayDateTime, setTakeawayDateTime] = useState('');
  const [isLater, setIsLater] = useState('NOW');
  const [takeawayDateModal, setTakeawayDateModal] = useState(false);
  const [takeawayTimeModal, setTakeawayTimeModal] = useState(false);
  const [takeawayLaterDate, setTakeawayLaterDate] = useState(moment().startOf('day').valueOf());
  const [takeawayLaterTime, setTakeawayLaterTime] = useState(moment().valueOf());
  const [scheduleDateTime, setScheduleDateTime] = useState(null)
  const takeawayTimeOption = [
    {
      label: 'Now',
      value: 'NOW',
    },
    {
      label: 'Later',
      value: 'LATER'
    },];

  // useEffect(() => {
  //   console.log('date time check', takeawayLaterDateTime)
  //   const dateTemp =
  //     takeawayLaterDate + '-' + takeawayLaterTime;

  //   setTakeawayLaterDateTime(moment(dateTemp).format('DD MMM YYYY hh:mm A'));
  // }, [takeawayLaterDate, takeawayLaterTime,]);

  useEffect(() => {
    var cartItemsTemp = [];
    var existedCartItemDict = {};

    for (var i = 0; i < cartItems.length; i++) {
      var cartItem = cartItems[i];
      if (existedCartItemDict[cartItem.itemId + cartItem.cartItemDate]) {
        // skip
      }
      else {
        existedCartItemDict[cartItem.itemId + cartItem.cartItemDate] = true;

        cartItemsTemp.push(cartItem);
      }
    }

    CommonStore.update(s => {
      s.cartItems = cartItemsTemp;
    });
  }, [cartItems]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var promoCodePromotionDropdownListTemp = [{
        label: 'N/A',
        value: '',
      }].concat(availablePromoCodePromotions.map(item => ({
        label: `${item.campaignName} (${item.promoCodeUsageLimit} left)`,
        value: item.uniqueId,
      })));

      if (selectedPromoCodePromotionId === '' && promoCodePromotionDropdownListTemp.length > 0) {
        setSelectedPromoCodePromotionId(promoCodePromotionDropdownListTemp[0].value);

        CommonStore.update(s => {
          s.selectedPromoCodePromotion = availablePromoCodePromotions.find(promotion => promotion.uniqueId === promoCodePromotionDropdownListTemp[0].value) || {};
        });
      }

      setPromoCodePromotionDropdownList(promoCodePromotionDropdownListTemp);
    });
  }, [
    // availableLoyaltyCampaigns,
    // selectedTaggableVoucherId
    availablePromoCodePromotions,
  ]);

  useEffect(() => {
    // if (cartItems.length > 0) {
    //   updateCartItemsDict();
    // }

    // InteractionManager.runAfterInteractions(() => {
    //   updateCartItemsDict();
    // });    

    if (isMounted) {
      updateCartItemsDict();
    }
  }, [
    isMounted,

    cartItems,

    currOutlet,

    overrideItemPriceSkuDict,
    amountOffItemSkuDict,
    percentageOffItemSkuDict,
    buy1Free1ItemSkuDict,
    deliveryItemSkuDict,
    takeawayItemSkuDict,

    overrideCategoryPriceNameDict,
    amountOffCategoryNameDict,
    percentageOffCategoryNameDict,
    buy1Free1CategoryNameDict,
    deliveryCategoryNameDict,
    takeawayCategoryNameDict,

    allOutletsItemAddOnIdDict,
    allOutletsItemAddOnChoiceIdDict,
  ]);

  const updateCartItemsDict = async () => {
    setIsCartLoading(true);

    var tempCartOutletItemsDict = {
      // ...cartOutletItemsDict, // avoid cache
    };

    var tempCartOutletItemAddOnDict = {
      // ...cartOutletItemAddOnDict, // avoid cache
    };

    var tempCartOutletItemAddOnChoiceDict = {
      // ...cartOutletItemAddOnChoiceDict, // avoid cache
    };

    var tempOutletsTaxDict = {
      ...outletsTaxDict,
    };

    var promotionIdAppliedListTemp = [];

    var tempCartItemsProcessed = [];

    var tempTotalPrice = 0;
    var tempTotalPrepareTime = 0;

    var currOutletId = '';

    ////////////////////////////////////////

    var cartItemQuantitySummaries = [];

    var discountPromotionsTotalTemp = 0;

    var takeawayDiscountAmount = 0;

    let rpQtyByPromotionIdDict = {};

    global.rpCategoryIdUsedPromoDict = {};

    ////////////////////////////////////////

    const cartItemsClone = [...cartItems];

    ////////////////////////////////////////

    //store the original sorting index first
    let cartItemIdOrderIndexDict = cartItems.reduce((dict, cartItem, cartItemIndex) => {
      dict[cartItem.itemId + cartItem.cartItemDate] = cartItemIndex;
      return dict;
    }, {});

    //here can sort based on low to high prices, or high to low prices, or didn't do anything
    //rmb to replace the bottom 'cartItems' with cartItemsSorted also
    let cartItemsSorted = [...cartItems]
    // if (/* (selectedTaggableVoucher && selectedTaggableVoucher.discSequence === DISCOUNT_SEQUENCE_TYPES.LOWEST_PRICE) || */
    //   (selectedPromoCodePromotion && selectedPromoCodePromotion.discSequence === DISCOUNT_SEQUENCE_TYPES.LOWEST_PRICE)) {
    //   cartItemsSorted.sort((a, b) => a.priceTemp - b.priceTemp);
    // } else if (/* (selectedTaggableVoucher && selectedTaggableVoucher.discSequence === DISCOUNT_SEQUENCE_TYPES.HIGHEST_PRICE) || */
    //   (selectedPromoCodePromotion && selectedPromoCodePromotion.discSequence === DISCOUNT_SEQUENCE_TYPES.HIGHEST_PRICE)) {
    //   cartItemsSorted.sort((a, b) => b.priceTemp - a.priceTemp);
    // } else if (/* (selectedTaggableVoucher && selectedTaggableVoucher.discSequence === DISCOUNT_SEQUENCE_TYPES.FIRST) || */
    //   (selectedPromoCodePromotion && selectedPromoCodePromotion.discSequence === DISCOUNT_SEQUENCE_TYPES.FIRST)) {
    //   // Already in the original order, no need to sort
    //   cartItemsSorted = [...cartItems];
    // }

    cartItemsSorted.sort((a, b) => (a.priceTemp / a.quantity) - (b.priceTemp / b.quantity));

    ////////////////////////////////////////

    let rpCategoryIdUsedPromoDict = {};

    for (var i = 0; i < cartItemsSorted.length; i++) {
      const tempCartItem = cartItemsSorted[i];

      var tempCartItemPrice = 0;

      var tempCartItemAddOnCategorized = {};
      var tempCartItemAddOnCategorizedPrice = {};

      var tempCartItemAddOnParsed = [];

      if (tempCartOutletItemsDict[tempCartItem.itemId] === undefined) {
        // need retrive the actual item, to show price, pic, etc

        // const outletItemSnapshot = await firestore()
        //   .collection(Collections.OutletItem)
        //   .where('uniqueId', '==', tempCartItem.itemId)
        //   .limit(1)
        //   .get();

        // if (!outletItemSnapshot.empty) {
        //   tempCartOutletItemsDict[tempCartItem.itemId] =
        //     outletItemSnapshot.docs[0].data();
        // }

        let foundItem = outletItems.find(findItem => findItem.uniqueId === tempCartItem.itemId);

        if (foundItem) {
          tempCartOutletItemsDict[tempCartItem.itemId] =
            foundItem;
        }
      }

      // tempCartItemPrice = tempCartItem.quantity * tempCartOutletItemsDict[tempCartItem.itemId].price;
      tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price;

      currOutletId = tempCartOutletItemsDict[tempCartItem.itemId].outletId;

      // if (tempOutletsTaxDict[currOutletId] === undefined) {
      //   // need retrieve the tax rate of this outlet

      //   const outletTaxSnapshot = await firestore()
      //     .collection(Collections.OutletTax)
      //     .where('outletId', '==', currOutletId)
      //     .limit(1)
      //     .get();

      //   if (!outletTaxSnapshot.empty) {
      //     tempOutletsTaxDict[currOutletId] = outletTaxSnapshot.docs[0].data();
      //   }
      // }

      //////////////////////////////////////////////////////////

      var extraPrice = 0;
      if (orderType === ORDER_TYPE.DELIVERY &&
        currOutlet &&
        currOutlet.deliveryPrice) {
        extraPrice = currOutlet.deliveryPrice;
      }
      else if (orderType === ORDER_TYPE.PICKUP &&
        orderTypeSub === ORDER_TYPE_SUB.NORMAL &&
        currOutlet &&
        currOutlet.pickUpPrice) {
        extraPrice = currOutlet.pickUpPrice;
      }

      if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].deliveryCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }

        if (!tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesActive) {
          extraPrice = 0;
        }
      }
      // else {
      //   extraPrice = 0;
      // }

      if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].pickUpCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }

        if (!tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].otherDCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].otherDChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }

        if (!tempCartOutletItemsDict[tempCartItem.itemId].otherDChargesActive) {
          extraPrice = 0;
        }
      }

      // else {
      //   extraPrice = 0;
      // }

      // var tempCartItemPriceOriginal =
      //   tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;

      var tempCartItemPriceOriginal =
        BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();

      //////////////////////////////////////////////////////////

      if (tempCartItem.priceVariable !== undefined) {
        tempCartItemPrice = tempCartItem.priceVariable;

        tempCartItemPriceOriginal = tempCartItem.priceVariable;
      }

      //////////////////////////////////////////////////////////

      //////////////////////////////////////////////////////////

      var promotionIdTemp = '';
      // var applyDiscountPer = '';

      var cartItemPromotionIdListTemp = [];

      //////////////////////////////////////////////////////////

      // promotion without promo code

      var overrideCategoryPrice = undefined;
      var overridePromotionId = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        overrideCategoryPriceNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        // overrideCategoryPrice =
        //   overrideCategoryPriceNameDict[
        //     selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        //   ].overridePrice + extraPrice;
        overrideCategoryPrice =
          BigNumber(overrideCategoryPriceNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].overridePrice).toNumber();
        overridePromotionId = overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].promotionId;
      }

      if (overrideItemPriceSkuDict[tempCartItem.itemSku] !== undefined
        && overrideItemPriceSkuDict[tempCartItem.itemSku].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          overrideItemPriceSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
        // &&
        // (overrideItemPriceSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || overrideItemPriceSkuDict[tempCartItem.itemSku].applyBefore === undefined)
      ) {
        // tempCartItemPrice = overrideItemPriceSkuDict[tempCartItem.itemSku].overridePrice + extraPrice;
        tempCartItemPrice = BigNumber(overrideItemPriceSkuDict[tempCartItem.itemSku].overridePrice).plus(extraPrice).toNumber();
        promotionIdAppliedListTemp.push(overrideItemPriceSkuDict[tempCartItem.itemSku].promotionId);

        promotionIdTemp = overrideItemPriceSkuDict[tempCartItem.itemSku].promotionId;
        // applyDiscountPer = overrideItemPriceSkuDict[tempCartItem.itemSku].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER;

        cartItemPromotionIdListTemp.push(promotionIdTemp);

      } else if (overrideCategoryPrice !== undefined
        // &&
        // (overrideCategoryPrice.applyBefore === APPLY_BEFORE.ORDER_PLACED || overrideCategoryPrice.applyBefore === undefined)
      ) {
        // tempCartItemPrice = overrideCategoryPrice + extraPrice;
        tempCartItemPrice = BigNumber(overrideCategoryPrice).plus(extraPrice).toNumber();
        promotionIdAppliedListTemp.push(overridePromotionId);

        promotionIdTemp = overridePromotionId;
        // applyDiscountPer = APPLY_DISCOUNT_PER.ORDER;

        cartItemPromotionIdListTemp.push(promotionIdTemp);

      } else if (tempCartItem.priceVariable === undefined) {
        // tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
        tempCartItemPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();
      }
      else {
        tempCartItemPrice = tempCartItem.priceVariable;
      }

      // =============================================================

      overrideCategoryPrice = undefined;
      overridePromotionId = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        overrideCategoryPriceNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        // overrideCategoryPrice =
        //   overrideCategoryPriceNameDict[
        //     selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        //   ].overridePrice + extraPrice;
        overrideCategoryPrice =
          BigNumber(overrideCategoryPriceNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].overridePrice).toNumber();
        overridePromotionId = overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].promotionId;
      }

      if (overrideItemPriceSkuDict[tempCartItem.itemSku] !== undefined
        && overrideItemPriceSkuDict[tempCartItem.itemSku].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          overrideItemPriceSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
        // &&
        // (overrideItemPriceSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || overrideItemPriceSkuDict[tempCartItem.itemSku].applyBefore === undefined)
      ) {
        // tempCartItemPrice = overrideItemPriceSkuDict[tempCartItem.itemSku].overridePrice + extraPrice;
        tempCartItemPrice = BigNumber(overrideItemPriceSkuDict[tempCartItem.itemSku].overridePrice).plus(extraPrice).toNumber();
        promotionIdAppliedListTemp.push(overrideItemPriceSkuDict[tempCartItem.itemSku].promotionId);

        promotionIdTemp = overrideItemPriceSkuDict[tempCartItem.itemSku].promotionId;
        // applyDiscountPer = overrideItemPriceSkuDict[tempCartItem.itemSku].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER;

        cartItemPromotionIdListTemp.push(promotionIdTemp);

      } else if (overrideCategoryPrice !== undefined
        // &&
        // (overrideCategoryPrice.applyBefore === APPLY_BEFORE.ORDER_PLACED || overrideCategoryPrice.applyBefore === undefined)
      ) {
        // tempCartItemPrice = overrideCategoryPrice + extraPrice;
        tempCartItemPrice = BigNumber(overrideCategoryPrice).plus(extraPrice).toNumber();
        promotionIdAppliedListTemp.push(overridePromotionId);

        promotionIdTemp = overridePromotionId;
        // applyDiscountPer = APPLY_DISCOUNT_PER.ORDER;

        cartItemPromotionIdListTemp.push(promotionIdTemp);

      } else {
        // 2023-04-06 - no need this first

        // if (tempCartItem.priceVariable === undefined) {
        //   // tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
        //   tempCartItemPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();
        // }
        // else {
        //   tempCartItemPrice = tempCartItem.priceVariable;
        // }
      }

      //////////////////////////////////////////////////////////

      if (tempCartItem.choices) {
        const tempCartItemChoices = Object.entries(tempCartItem.choices).map(
          ([key, value]) => ({ key, value }),
        );

        for (var j = 0; j < tempCartItemChoices.length; j++) {
          if (tempCartItemChoices[j].value) {
            // means the addon of this item is picked, need to retrieve the actual addon

            if (
              tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] ===
              undefined
            ) {
              // const outletItemAddOnChoiceSnapshot = await firestore()
              //   .collection(Collections.OutletItemAddOnChoice)
              //   .where('uniqueId', '==', tempCartItemChoices[j].key)
              //   .limit(1)
              //   .get();

              // if (!outletItemAddOnChoiceSnapshot.empty) {
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
              //     outletItemAddOnChoiceSnapshot.docs[0].data();
              // }

              if (allOutletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key]) {
                tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
                  allOutletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key];
              }
            }

            if (tempCartItem.priceVariable === undefined) {
              // tempCartItemPrice +=
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
              //     .price;

              // tempCartItemPriceOriginal +=
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
              //     .price;

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).plus(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
                  .price).toNumber();

              tempCartItemPriceOriginal =
                BigNumber(tempCartItemPriceOriginal).plus(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key].price).toNumber();
            }

            // need to retrieve the description/type name of this addon choice

            const tempCartItemAddOnChoice =
              tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key];

            if (
              tempCartOutletItemAddOnDict[
              tempCartItemAddOnChoice.outletItemAddOnId
              ] === undefined
            ) {
              // const outletItemAddOnSnapshot = await firestore()
              //   .collection(Collections.OutletItemAddOn)
              //   .where(
              //     'uniqueId',
              //     '==',
              //     tempCartItemAddOnChoice.outletItemAddOnId,
              //   )
              //   .limit(1)
              //   .get();

              // if (!outletItemAddOnSnapshot.empty) {
              //   tempCartOutletItemAddOnDict[
              //     tempCartItemAddOnChoice.outletItemAddOnId
              //   ] = outletItemAddOnSnapshot.docs[0].data();
              // }

              if (allOutletsItemAddOnIdDict[tempCartItemAddOnChoice.outletItemAddOnId]) {
                tempCartOutletItemAddOnDict[tempCartItemAddOnChoice.outletItemAddOnId] =
                  allOutletsItemAddOnIdDict[tempCartItemAddOnChoice.outletItemAddOnId];
              }
            }

            if (
              tempCartItemAddOnCategorized[
              tempCartItemAddOnChoice.outletItemAddOnId
              ] === undefined
            ) {
              tempCartItemAddOnCategorized[
                tempCartItemAddOnChoice.outletItemAddOnId
              ] = [];
            }

            tempCartItemAddOnCategorized[
              tempCartItemAddOnChoice.outletItemAddOnId
            ].push(tempCartItemAddOnChoice.name);

            if (
              tempCartItemAddOnCategorizedPrice[
              tempCartItemAddOnChoice.outletItemAddOnId
              ] === undefined
            ) {
              tempCartItemAddOnCategorizedPrice[
                tempCartItemAddOnChoice.outletItemAddOnId
              ] = [];
            }

            tempCartItemAddOnCategorizedPrice[
              tempCartItemAddOnChoice.outletItemAddOnId
            ].push(tempCartItemAddOnChoice.price);
          }
        }

        const tempCartItemAddOnCategorizedList = Object.entries(
          tempCartItemAddOnCategorized,
        ).map(([key, value]) => ({ key, value }));
        const tempCartItemAddOnCategorizedPriceList = Object.entries(
          tempCartItemAddOnCategorizedPrice,
        ).map(([key, value]) => ({ key, value }));

        if (tempCartItemAddOnCategorizedList.length > 0) {
          for (var j = 0; j < tempCartItemAddOnCategorizedList.length; j++) {
            const tempCartItemAddOnName =
              tempCartOutletItemAddOnDict[
                tempCartItemAddOnCategorizedList[j].key
              ].name;

            const tempCartItemAddOnOrderIndex =
              tempCartOutletItemAddOnDict[
                tempCartItemAddOnCategorizedList[j].key
              ].orderIndex !== undefined
                ?
                tempCartOutletItemAddOnDict[
                  tempCartItemAddOnCategorizedList[j].key
                ].orderIndex
                :
                0;

            let pal = null;
            if (tempCartOutletItemAddOnDict[
              tempCartItemAddOnCategorizedList[j].key
            ].pal !== undefined) {
              pal = tempCartOutletItemAddOnDict[
                tempCartItemAddOnCategorizedList[j].key
              ].pal;
            }

            if (pal && !Array.isArray(pal)) {
              // means is shared variant/addon

              if (pal[tempCartItem.itemId]) {
                pal = pal[tempCartItem.itemId];
              }
              else {
                pal = null;
              }
            }

            tempCartItemAddOnParsed.push({
              name: tempCartItemAddOnName,
              choiceNames: Array.isArray(tempCartItemAddOnCategorizedList[j].value) ? tempCartItemAddOnCategorizedList[j].value.sort((a, b) => {
                return a.localeCompare(b);
              }) : tempCartItemAddOnCategorizedList[j].value,
              prices: tempCartItemAddOnCategorizedPriceList[j].value,

              oi: tempCartItemAddOnOrderIndex,

              pal: pal,
            });
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // for add-on group

      if (tempCartItem.addOnGroupList) {
        for (var j = 0; j < tempCartItem.addOnGroupList.length; j++) {
          // now separate all, might change in future

          const addOnGroup = tempCartItem.addOnGroupList[j];

          let pal = null;
          if (addOnGroup.pal) {
            pal = addOnGroup.pal;
          }

          if (pal && !Array.isArray(pal)) {
            // means is shared variant/addon

            if (pal[tempCartItem.itemId]) {
              pal = pal[tempCartItem.itemId];
            }
            else {
              pal = null;
            }
          }

          tempCartItemAddOnParsed.push({
            name: addOnGroup.addOnName,
            addOnId: addOnGroup.outletItemAddOnId,
            choiceNames: [addOnGroup.choiceName],
            prices: [addOnGroup.quantity * addOnGroup.price],
            quantities: [addOnGroup.quantity],
            singlePrices: [addOnGroup.price],
            addOnChoiceIdList: [addOnGroup.outletItemAddOnChoiceId],
            minSelectList: [addOnGroup.minSelect],
            maxSelectList: [addOnGroup.maxSelect],

            oi: addOnGroup.oi !== undefined ? addOnGroup.oi : 0,

            pal: pal,
          });

          if (tempCartItem.priceVariable === undefined) {
            // tempCartItemPrice += addOnGroup.quantity * addOnGroup.price;
            // tempCartItemPriceOriginal += addOnGroup.quantity * addOnGroup.price;

            tempCartItemPrice = BigNumber(tempCartItemPrice).plus(BigNumber(addOnGroup.quantity).multipliedBy(addOnGroup.price)).toNumber();
            tempCartItemPriceOriginal = BigNumber(tempCartItemPriceOriginal).plus(BigNumber(addOnGroup.quantity).multipliedBy(addOnGroup.price)).toNumber();
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // track single item price | 2022-04-12 | Herks

      var tempCartItemPriceSingle = tempCartItemPrice;
      var tempCartItemPriceSingleOriginal = tempCartItemPriceOriginal;

      //////////////////////////////////////////////////////////////////////

      // 2022-10-05 - Fixes for variable pricing (should use the inputed price already)

      if (tempCartItem.priceVariable === undefined) {
        // tempCartItemPrice = tempCartItemPrice * tempCartItem.quantity;
        // tempCartItemPriceOriginal =
        //   tempCartItemPriceOriginal * tempCartItem.quantity;

        tempCartItemPrice = BigNumber(tempCartItemPrice).multipliedBy(tempCartItem.quantity).toNumber();
        tempCartItemPriceOriginal =
          BigNumber(tempCartItemPriceOriginal).multipliedBy(tempCartItem.quantity).toNumber();
      }

      //////////////////////////////////////////////////////////////////////

      cartItemQuantitySummaries.push({
        item: tempCartItem,
        quantity: tempCartItem.quantity,
      });

      //////////////////////////////////////////////////////////////////////

      var amountOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        amountOffCategory =
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
      }

      if (amountOffItemSkuDict[tempCartItem.itemSku] !== undefined &&
        amountOffItemSkuDict[tempCartItem.itemSku].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          amountOffItemSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )) {
        var promotionResult = checkApplyDiscountPerValidity(amountOffItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            amountOffItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            amountOffItemSkuDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            false,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderType,
            currOutlet,

            orderTypeSub,
          );

          // const currPromoId = amountOffCategoryNameDict[
          //   selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          // ];
          // const currPromoId = amountOffItemSkuDict[tempCartItem.itemSku];

          const currPromo = amountOffItemSkuDict[tempCartItem.itemSku];
          const currPromoId = amountOffItemSkuDict[tempCartItem.itemSku].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus
            &&
            qualifiedCartItemsInfo.quantity >=
            amountOffItemSkuDict[tempCartItem.itemSku].quantityMin
            // &&
            // tempCartItem.quantity <=
            // amountOffItemSkuDict[tempCartItem.itemSku].quantityMax
          ) {
            if (
              qualifiedCartItemsInfo.price >=
              amountOffItemSkuDict[tempCartItem.itemSku].priceMin
            ) {
              // tempCartItemPrice -=
              //   (amountOffItemSkuDict[tempCartItem.itemSku].amountOff * promotionResult.discountQuantity);
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(BigNumber(amountOffItemSkuDict[tempCartItem.itemSku].amountOff).multipliedBy(((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                  ? rpDiscountQty : promotionResult.discountQuantity))).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, 0);
              promotionIdAppliedListTemp.push(amountOffItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = amountOffItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);

            }
          }
        }
      } else if (amountOffCategory !== undefined
        // &&
        // (amountOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || amountOffCategory.applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(amountOffCategory, promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            amountOffCategoryNameDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            false,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderType,
            currOutlet,

            orderTypeSub,
          );

          // const currPromoId = amountOffItemSkuDict[tempCartItem.itemSku];
          // const currPromoId = amountOffCategoryNameDict[
          //   selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          // ];

          const currPromo = amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
          const currPromoId = amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus
            &&
            qualifiedCartItemsInfo.quantity >= amountOffCategory.quantityMin
            // &&
            // tempCartItem.quantity <= amountOffCategory.quantityMax
          ) {
            if (
              qualifiedCartItemsInfo.price >= amountOffCategory.priceMin
            ) {
              // tempCartItemPrice -= (amountOffCategory.amountOff * promotionResult.discountQuantity);
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(BigNumber(amountOffCategory.amountOff).multipliedBy(((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                  ? rpDiscountQty : promotionResult.discountQuantity))).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, 0);
              promotionIdAppliedListTemp.push(amountOffCategory.promotionId);

              promotionIdTemp = amountOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      var percentageOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        percentageOffCategory =
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
      }

      if (percentageOffItemSkuDict[tempCartItem.itemSku] !== undefined
        && percentageOffItemSkuDict[tempCartItem.itemSku].usePromoCode === false
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION,
          percentageOffItemSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
        // &&
        // (percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(percentageOffItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            percentageOffItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            percentageOffItemSkuDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            false,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderType,
            currOutlet,

            orderTypeSub,
          );

          // const currPromoId = percentageOffItemSkuDict[tempCartItem.itemSku];

          const currPromo = percentageOffItemSkuDict[tempCartItem.itemSku];
          const currPromoId = percentageOffItemSkuDict[tempCartItem.itemSku].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus
            &&
            qualifiedCartItemsInfo.quantity >=
            percentageOffItemSkuDict[tempCartItem.itemSku].quantityMin
            // &&
            // tempCartItem.quantity <=
            // percentageOffItemSkuDict[tempCartItem.itemSku].quantityMax
          ) {
            if (
              qualifiedCartItemsInfo.price >=
              percentageOffItemSkuDict[tempCartItem.itemSku].priceMin
            ) {
              // var individualItemPrice = tempCartItemPrice / tempCartItem.quantity;
              var individualItemPrice = BigNumber(tempCartItemPrice).dividedBy(tempCartItem.quantity).toNumber();

              // tempCartItemPrice -=
              //   (individualItemPrice *
              //     (percentageOffItemSkuDict[tempCartItem.itemSku].percentageOff /
              //       100)) * promotionResult.discountQuantity;
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(
                    individualItemPrice
                  ).multipliedBy(
                    BigNumber(
                      percentageOffItemSkuDict[tempCartItem.itemSku].percentageOff
                    ).dividedBy(100)
                  )
                    .multipliedBy(
                      ((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                        ? rpDiscountQty : promotionResult.discountQuantity)
                    )
                ).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, 0);
              promotionIdAppliedListTemp.push(percentageOffItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = percentageOffItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      } else if (percentageOffCategory !== undefined
        // &&
        // (percentageOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffCategory.applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(percentageOffCategory, promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            percentageOffCategoryNameDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            false,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderType,
            currOutlet,

            orderTypeSub,
          );

          const currPromo = percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
          const currPromoId = percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus
            &&
            tempCartItem.quantity >= percentageOffCategory.quantityMin
            // &&
            // tempCartItem.quantity <= percentageOffCategory.quantityMax
          ) {
            if (
              tempCartItemPrice >= percentageOffCategory.priceMin
            ) {
              // var individualItemPrice = tempCartItemPrice / tempCartItem.quantity;
              var individualItemPrice = BigNumber(tempCartItemPrice).dividedBy(tempCartItem.quantity).toNumber();

              // tempCartItemPrice -=
              //   ((individualItemPrice * percentageOffCategory.percentageOff) / 100) * promotionResult.discountQuantity;
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(
                    individualItemPrice
                  ).multipliedBy(
                    BigNumber(
                      percentageOffCategory.percentageOff
                    ).dividedBy(100)
                  )
                    .multipliedBy(
                      (
                        (rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                          ? rpDiscountQty : promotionResult.discountQuantity
                      )
                    )
                ).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, 0);
              promotionIdAppliedListTemp.push(percentageOffCategory.promotionId);

              promotionIdTemp = percentageOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // calculate takeaway promotions

      if (orderType === ORDER_TYPE.PICKUP) {
        var takeawayOffCategory = undefined;
        if (
          selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
          takeawayCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ] !== undefined
          && takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].usePromoCode === false
          &&
          checkIsAllowPromotionVoucherToApply(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,
          )
        ) {
          takeawayOffCategory =
            takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ];
        }

        if (takeawayItemSkuDict[tempCartItem.itemSku] !== undefined &&
          takeawayItemSkuDict[tempCartItem.itemSku].usePromoCode === false
          &&
          checkIsAllowPromotionVoucherToApply(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION,
            takeawayItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,
          )
          // &&
          // (takeawayItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || takeawayItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
        ) {
          var promotionResult = checkApplyDiscountPerValidity(takeawayItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

          if (promotionResult.validity) {
            if (
              tempCartItemPrice >=
              takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAboveAmount
            ) {
              // takeawayDiscountAmount +=
              //   takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount;

              // tempCartItemPrice -=
              //   takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount * promotionResult.discountQuantity;

              takeawayDiscountAmount =
                BigNumber(takeawayDiscountAmount).plus(takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount).toNumber();

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount).multipliedBy(promotionResult.discountQuantity)
                ).toNumber();

              tempCartItemPrice = Math.max(tempCartItemPrice, 0);

              promotionIdAppliedListTemp.push(takeawayItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = takeawayItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        } else if (takeawayOffCategory !== undefined
          // &&
          // (takeawayOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || takeawayOffCategory.applyBefore === undefined)
        ) {
          var promotionResult = checkApplyDiscountPerValidity(takeawayOffCategory, promotionIdAppliedListTemp, tempCartItem);

          if (promotionResult.validity) {
            if (
              tempCartItemPrice >= takeawayOffCategory.takeawayDiscountAboveAmount
            ) {
              // takeawayDiscountAmount += takeawayOffCategory.takeawayDiscountAmount;

              // tempCartItemPrice -=
              //   takeawayOffCategory.takeawayDiscountAmount * promotionResult.discountQuantity;              

              takeawayDiscountAmount =
                BigNumber(takeawayDiscountAmount).plus(takeawayOffCategory.takeawayDiscountAmount).toNumber();

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(takeawayOffCategory.takeawayDiscountAmount).multipliedBy(promotionResult.discountQuantity)
                ).toNumber();

              tempCartItemPrice = Math.max(tempCartItemPrice, 0);

              promotionIdAppliedListTemp.push(takeawayOffCategory.promotionId);

              promotionIdTemp = takeawayOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////

      // promotions with promo code

      amountOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        amountOffCategory =
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
      }

      if (amountOffItemSkuDict[tempCartItem.itemSku] !== undefined &&
        amountOffItemSkuDict[tempCartItem.itemSku].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          amountOffItemSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )) {
        var promotionResult = checkApplyDiscountPerValidity(amountOffItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            amountOffItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            amountOffItemSkuDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            true,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderType,
            currOutlet,

            orderTypeSub,
          );

          // const currPromoId = amountOffItemSkuDict[tempCartItem.itemSku];

          const currPromo = amountOffItemSkuDict[tempCartItem.itemSku];
          const currPromoId = amountOffItemSkuDict[tempCartItem.itemSku].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus
            &&
            qualifiedCartItemsInfo.quantity >=
            amountOffItemSkuDict[tempCartItem.itemSku].quantityMin
            // &&
            // tempCartItem.quantity <=
            // amountOffItemSkuDict[tempCartItem.itemSku].quantityMax
          ) {
            if (
              qualifiedCartItemsInfo.price >=
              amountOffItemSkuDict[tempCartItem.itemSku].priceMin
            ) {
              // tempCartItemPrice -=
              //   (amountOffItemSkuDict[tempCartItem.itemSku].amountOff * promotionResult.discountQuantity);
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(BigNumber(amountOffItemSkuDict[tempCartItem.itemSku].amountOff).multipliedBy(((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                  ? rpDiscountQty : promotionResult.discountQuantity))).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, 0);
              promotionIdAppliedListTemp.push(amountOffItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = amountOffItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);

            }
          }
        }
      } else if (amountOffCategory !== undefined
        // &&
        // (amountOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || amountOffCategory.applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(amountOffCategory, promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            amountOffCategoryNameDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            true,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderType,
            currOutlet,

            orderTypeSub,
          );

          // const currPromoId = amountOffCategoryNameDict[
          //   selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          // ];

          const currPromo = amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
          const currPromoId = amountOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus
            &&
            qualifiedCartItemsInfo.quantity >= amountOffCategory.quantityMin
            // &&
            // tempCartItem.quantity <= amountOffCategory.quantityMax
          ) {
            if (
              qualifiedCartItemsInfo.price >= amountOffCategory.priceMin
            ) {
              // tempCartItemPrice -= (amountOffCategory.amountOff * promotionResult.discountQuantity);
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(BigNumber(amountOffCategory.amountOff).multipliedBy(((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                  ? rpDiscountQty : promotionResult.discountQuantity))).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, 0);
              promotionIdAppliedListTemp.push(amountOffCategory.promotionId);

              promotionIdTemp = amountOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      percentageOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ] !== undefined
        && percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
      ) {
        percentageOffCategory =
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
      }

      if (percentageOffItemSkuDict[tempCartItem.itemSku] !== undefined
        && percentageOffItemSkuDict[tempCartItem.itemSku].usePromoCode === true
        &&
        checkIsAllowPromotionVoucherToApply(
          currOutlet.allowStackedPromotionVoucher,
          APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
          percentageOffItemSkuDict[tempCartItem.itemSku],
          selectedPromoCodePromotion,
          {},
          promotionIdAppliedListTemp,
        )
        // &&
        // (percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(percentageOffItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            percentageOffItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            percentageOffItemSkuDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            true,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderType,
            currOutlet,

            orderTypeSub,
          );

          // const currPromoId = percentageOffItemSkuDict[tempCartItem.itemSku];

          const currPromo = percentageOffItemSkuDict[tempCartItem.itemSku];
          const currPromoId = percentageOffItemSkuDict[tempCartItem.itemSku].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus
            &&
            qualifiedCartItemsInfo.quantity >=
            percentageOffItemSkuDict[tempCartItem.itemSku].quantityMin
            // &&
            // tempCartItem.quantity <=
            // percentageOffItemSkuDict[tempCartItem.itemSku].quantityMax
          ) {
            if (
              qualifiedCartItemsInfo.price >=
              percentageOffItemSkuDict[tempCartItem.itemSku].priceMin
            ) {
              // var individualItemPrice = tempCartItemPrice / tempCartItem.quantity;
              var individualItemPrice = BigNumber(tempCartItemPrice).dividedBy(tempCartItem.quantity).toNumber();

              // tempCartItemPrice -=
              //   (individualItemPrice *
              //     (percentageOffItemSkuDict[tempCartItem.itemSku].percentageOff /
              //       100)) * promotionResult.discountQuantity;
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(
                    individualItemPrice
                  ).multipliedBy(
                    BigNumber(
                      percentageOffItemSkuDict[tempCartItem.itemSku].percentageOff
                    ).dividedBy(100)
                  )
                    .multipliedBy(
                      ((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                        ? rpDiscountQty : promotionResult.discountQuantity)
                    )
                ).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, 0);
              promotionIdAppliedListTemp.push(percentageOffItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = percentageOffItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      } else if (percentageOffCategory !== undefined
        // &&
        // (percentageOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffCategory.applyBefore === undefined)
      ) {
        var promotionResult = checkApplyDiscountPerValidity(percentageOffCategory, promotionIdAppliedListTemp, tempCartItem);

        if (promotionResult.validity) {
          const qualifiedCartItemsInfo = checkQualifiedItemsQuantityAndAmountForPromotion(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,

            percentageOffCategoryNameDict,
            selectedOutletItemCategoriesDict,

            cartItemsClone,
            true,

            outletItems,
            allOutletsItemAddOnIdDict,
            allOutletsItemAddOnChoiceIdDict,
            orderType,
            currOutlet,

            orderTypeSub,
          );

          // const currPromoId = percentageOffCategoryNameDict[
          //   selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          // ];

          const currPromo = percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ];
          const currPromoId = percentageOffCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].promotionId;

          let { rpQtyStatus, rpQty, currQty, matchedCartItems } = checkRPQtyStatus(
            currPromo,
            cartItemsClone,
            selectedOutletItemCategoriesDict,
            rpCategoryIdUsedPromoDict

          );

          let rpValid = false;
          let rpDiscountQty = 0;
          if (rpQtyStatus && currQty > 0) {
            // means is the required purchase promo

            if (
              rpQtyByPromotionIdDict[currPromoId] !== undefined
                ?
                rpQtyByPromotionIdDict[currPromoId] > 0
                :
                true
            ) {
              let maxApplicableQty = tempCartItem.quantity;

              let applyQty = (rpQty * maxApplicableQty);

              if (rpQtyByPromotionIdDict[currPromoId] !== undefined) {
                if (rpQtyByPromotionIdDict[currPromoId] - applyQty < 0) {
                  applyQty = rpQtyByPromotionIdDict[currPromoId];
                }

                rpQtyByPromotionIdDict[currPromoId] -= applyQty;
              }
              else {
                if (currQty - applyQty < 0) {
                  applyQty = currQty;
                }

                rpQtyByPromotionIdDict[currPromoId] = currQty - applyQty;
              }

              rpDiscountQty = Math.floor(applyQty / rpQty);

              //////////////////////////

              let consumedQty = 0;
              let initialQty = 0;
              for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
                const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

                if (rpCategoryIdUsedPromoDict[rpKey]) {
                  if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'boolean') {
                    consumedQty += matchedCartItems[rpIndex].quantity;
                  }
                  else if (typeof rpCategoryIdUsedPromoDict[rpKey] === 'number') {
                    consumedQty += rpCategoryIdUsedPromoDict[rpKey];
                  }
                }

                initialQty += matchedCartItems[rpIndex].quantity;
              }

              let remainingQty = initialQty - consumedQty;

              console.log(`remainingQty: ${remainingQty}`);
              console.log(`initialQty: ${initialQty}`);
              console.log(`consumedQty: ${consumedQty}`);
              console.log(`rpDiscountQty: ${rpDiscountQty}`);

              if (rpDiscountQty > remainingQty) {
                rpDiscountQty = remainingQty;
              }

              //////////////////////////

              if (rpDiscountQty > 0) {
                rpValid = true;
              }
              else {
                rpQtyStatus = false;
                rpValid = false;
              }
            }
            else {
              rpQtyStatus = false;
              rpValid = false;
            }
          }

          console.log('==test start==');
          console.log(tempCartItem);
          console.log(rpQtyStatus);
          console.log(rpQty);
          console.log(currQty);
          console.log(rpValid);
          console.log(rpDiscountQty);
          console.log('==test end==');

          if (rpQtyStatus && rpValid && rpDiscountQty > 0) {
            // can track this product is already been 'consumed', and not counted into the other required purchased promotion

            let rpDiscountQtyTrack = rpDiscountQty;

            for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
              const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

              if (rpCategoryIdUsedPromoDict[rpKey] === true) {
                // existed already, can skip
                continue;
              }
              else {
                // rpCategoryIdUsedPromoDict[rpKey] = true;

                let qtyToDeduct = 0;
                if (matchedCartItems[rpIndex].quantity >= rpDiscountQtyTrack) {
                  qtyToDeduct = rpDiscountQtyTrack;
                }
                else {
                  qtyToDeduct = matchedCartItems[rpIndex].quantity;
                }

                rpDiscountQtyTrack -= qtyToDeduct;

                if (rpCategoryIdUsedPromoDict[rpKey] === undefined) {
                  rpCategoryIdUsedPromoDict[rpKey] = qtyToDeduct;
                }
                else {
                  rpCategoryIdUsedPromoDict[rpKey] += qtyToDeduct;
                }

                if (rpCategoryIdUsedPromoDict[rpKey] >= matchedCartItems[rpIndex].quantity) {
                  rpCategoryIdUsedPromoDict[rpKey] = true;
                }

                if (rpDiscountQtyTrack <= 0) {
                  break;
                }
              }
            }
          }

          if (
            rpQtyStatus
            &&
            qualifiedCartItemsInfo.quantity >= percentageOffCategory.quantityMin
            // &&
            // tempCartItem.quantity <= percentageOffCategory.quantityMax
          ) {
            if (
              qualifiedCartItemsInfo.price >= percentageOffCategory.priceMin
            ) {
              // var individualItemPrice = tempCartItemPrice / tempCartItem.quantity;
              var individualItemPrice = BigNumber(tempCartItemPrice).dividedBy(tempCartItem.quantity).toNumber();

              // tempCartItemPrice -=
              //   ((individualItemPrice * percentageOffCategory.percentageOff) / 100) * promotionResult.discountQuantity;
              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(
                    individualItemPrice
                  ).multipliedBy(
                    BigNumber(
                      percentageOffCategory.percentageOff
                    ).dividedBy(100)
                  )
                    .multipliedBy(
                      ((rpValid && (promotionResult.discountQuantity === tempCartItem.quantity))
                        ? rpDiscountQty : promotionResult.discountQuantity)
                    )
                ).toNumber();
              tempCartItemPrice = Math.max(tempCartItemPrice, 0);
              promotionIdAppliedListTemp.push(percentageOffCategory.promotionId);

              promotionIdTemp = percentageOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // calculate takeaway promotions

      if (orderType === ORDER_TYPE.PICKUP) {
        var takeawayOffCategory = undefined;
        if (
          selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
          takeawayCategoryNameDict[
          selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ] !== undefined
          && takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
          ].usePromoCode === true
          &&
          checkIsAllowPromotionVoucherToApply(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,
          )
        ) {
          takeawayOffCategory =
            takeawayCategoryNameDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ];
        }

        if (takeawayItemSkuDict[tempCartItem.itemSku] !== undefined &&
          takeawayItemSkuDict[tempCartItem.itemSku].usePromoCode === true
          &&
          checkIsAllowPromotionVoucherToApply(
            currOutlet.allowStackedPromotionVoucher,
            APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
            takeawayItemSkuDict[tempCartItem.itemSku],
            selectedPromoCodePromotion,
            {},
            promotionIdAppliedListTemp,
          )
          // &&
          // (takeawayItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || takeawayItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
        ) {
          var promotionResult = checkApplyDiscountPerValidity(takeawayItemSkuDict[tempCartItem.itemSku], promotionIdAppliedListTemp, tempCartItem);

          if (promotionResult.validity) {
            if (
              tempCartItemPrice >=
              takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAboveAmount
            ) {
              // takeawayDiscountAmount +=
              //   takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount;

              // tempCartItemPrice -=
              //   takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount * promotionResult.discountQuantity;

              takeawayDiscountAmount =
                BigNumber(takeawayDiscountAmount).plus(takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount).toNumber();

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(takeawayItemSkuDict[tempCartItem.itemSku].takeawayDiscountAmount).multipliedBy(promotionResult.discountQuantity)
                ).toNumber();

              tempCartItemPrice = Math.max(tempCartItemPrice, 0);

              promotionIdAppliedListTemp.push(takeawayItemSkuDict[tempCartItem.itemSku].promotionId);

              promotionIdTemp = takeawayItemSkuDict[tempCartItem.itemSku].promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        } else if (takeawayOffCategory !== undefined
          // &&
          // (takeawayOffCategory.applyBefore === APPLY_BEFORE.ORDER_PLACED || takeawayOffCategory.applyBefore === undefined)
        ) {
          var promotionResult = checkApplyDiscountPerValidity(takeawayOffCategory, promotionIdAppliedListTemp, tempCartItem);

          if (promotionResult.validity) {
            if (
              tempCartItemPrice >= takeawayOffCategory.takeawayDiscountAboveAmount
            ) {
              // takeawayDiscountAmount += takeawayOffCategory.takeawayDiscountAmount;

              // tempCartItemPrice -=
              //   takeawayOffCategory.takeawayDiscountAmount * promotionResult.discountQuantity;              

              takeawayDiscountAmount =
                BigNumber(takeawayDiscountAmount).plus(takeawayOffCategory.takeawayDiscountAmount).toNumber();

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).minus(
                  BigNumber(takeawayOffCategory.takeawayDiscountAmount).multipliedBy(promotionResult.discountQuantity)
                ).toNumber();

              tempCartItemPrice = Math.max(tempCartItemPrice, 0);

              promotionIdAppliedListTemp.push(takeawayOffCategory.promotionId);

              promotionIdTemp = takeawayOffCategory.promotionId;

              cartItemPromotionIdListTemp.push(promotionIdTemp);
            }
          }
        }
      }

      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////
      //////////////////////////////////////////////////////////////////////

      // var discountPromotions = 0; // use previous or new? (previous = redeem points)
      var discountPromotions = 0; // use previous or new? (previos = redeem points)
      // var discountPromotionsLCC = 0;

      if (tempCartItemPrice < tempCartItemPriceOriginal) {
        // means got deducted by promotions/points

        // discountPromotions = tempCartItemPriceOriginal - tempCartItemPrice;
        discountPromotions = BigNumber(tempCartItemPriceOriginal).minus(tempCartItemPrice).toNumber();
        // discountPromotionsLCC = tempCartItemPriceOriginal - tempCartItemPrice;
      }

      //////////////////////////////////////////////////////////////////////

      tempCartItemsProcessed.push({
        isFreeItem: tempCartItem.isFreeItem || false,
        promotionId: tempCartItem.promotionId || promotionIdTemp,

        promotionIdList: cartItemPromotionIdListTemp,

        voucherId: tempCartItem.voucherId || '',
        voucherName: tempCartItem.voucherName || '',

        voucherId: tempCartItem.voucherId || '',
        voucherName: tempCartItem.voucherName || '',

        priceOriginal: +tempCartItemPriceOriginal.toFixed(2),

        ...(tempCartItemPrice < tempCartItemPriceOriginal && {
          discount: +(tempCartItemPriceOriginal - tempCartItemPrice).toFixed(2),

          discountPromotions: +(tempCartItemPriceOriginal - tempCartItemPrice).toFixed(2),
        }),

        itemId: tempCartItem.itemId,
        choices: tempCartItem.choices,
        remarks: tempCartItem.remarks,
        fireOrder: tempCartItem.fireOrder,
        cartItemDate: moment(tempCartItem.cartItemDate).valueOf(),

        weight: tempCartItem.weight ? tempCartItem.weight : 0.1,

        image: tempCartOutletItemsDict[tempCartItem.itemId].image,
        name: tempCartOutletItemsDict[tempCartItem.itemId].name,
        itemName: tempCartOutletItemsDict[tempCartItem.itemId].name,
        itemSku: tempCartOutletItemsDict[tempCartItem.itemId].sku,
        printerAreaList: tempCartOutletItemsDict[tempCartItem.itemId].printerAreaList || [],
        printingTypeList: tempCartOutletItemsDict[tempCartItem.itemId].printingTypeList || null,
        price: +tempCartItemPrice.toFixed(2),
        quantity: tempCartItem.quantity,
        addOns: tempCartItemAddOnParsed.sort((a, b) => {
          return (
            ((a.oi !== undefined)
              ? a.oi
              : tempCartItemAddOnParsed.length) -
            ((b.oi !== undefined)
              ? b.oi
              : tempCartItemAddOnParsed.length)
          );
        }),

        deliveredAt: null,
        cookedAt: null,
        isChecked: false,

        orderType, // might relocate in other places in future

        prepareTime:
          tempCartOutletItemsDict[tempCartItem.itemId].prepareTime *
          tempCartItem.quantity,

        categoryId: tempCartItem.categoryId,

        discountPromotions, // to record deducted amount

        isDocket: tempCartOutletItemsDict[tempCartItem.itemId].isDocket || false,
        printDocketQuantity: tempCartOutletItemsDict[tempCartItem.itemId].printDocketQuantity || 1,

        originCartItemId: tempCartItem.originCartItemId || null,

        extraPrice: +parseFloat(extraPrice).toFixed(2),

        ...(tempCartItem.isFreeItem && {
          priceBundle: +tempCartItemPrice.toFixed(2),
        }),

        ...(tempCartItem.priceVariable !== undefined && {
          priceVariable: tempCartItem.priceVariable,
        }),

        ...((tempCartItem.bc !== undefined && tempCartItem.bc !== '') && {
          bc: tempCartItem.bc,
        }),

        priceType: tempCartItem.priceType ? tempCartItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
        unitType: tempCartItem.unitType ? tempCartItem.unitType : UNIT_TYPE.GRAM,

        itemCostPrice: tempCartItem.itemCostPrice ? tempCartItem.itemCostPrice : 0,
      });

      tempTotalPrice = BigNumber(tempTotalPrice).plus(tempCartItemPrice).toNumber();
      tempTotalPrepareTime +=
        tempCartOutletItemsDict[tempCartItem.itemId].prepareTime *
        tempCartItem.quantity;

      discountPromotionsTotalTemp += discountPromotions;
    }

    //here can undo the sorting back (need do operate on the tempCartItemsProccessed)
    tempCartItemsProcessed.sort((a, b) => {
      return cartItemIdOrderIndexDict[a.itemId + a.cartItemDate] -
        cartItemIdOrderIndexDict[b.itemId + b.cartItemDate];
    });

    //////////////////////////////////////////////////////////

    // add free items to processed carts

    var freeItemQuantitySummaries = [];

    var redeemedB1F1PromotionIdList = [];

    const buy1Free1ItemSkuDictList = Object.entries(buy1Free1ItemSkuDict).map(
      ([key, value]) => ({
        ...value,
        key, // the item sku
      }),
    );

    for (var i = 0; i < buy1Free1ItemSkuDictList.length; i++) {
      if (
        true
        // buy1Free1ItemSkuDictList[i].applyBefore === APPLY_BEFORE.ORDER_PLACED || buy1Free1ItemSkuDictList[i].applyBefore === undefined
      ) {
        var quantity = 0;

        for (var j = 0; j < cartItemQuantitySummaries.length; j++) {
          if (buy1Free1ItemSkuDictList[i].key === cartItemQuantitySummaries[j].item.sku ||
            buy1Free1ItemSkuDictList[i].key === cartItemQuantitySummaries[j].item.itemSku) {
            // means eligible item, accum quantity

            var promotionResult = checkApplyDiscountPerValidity(buy1Free1ItemSkuDictList[i], promotionIdAppliedListTemp, { quantity: cartItemQuantitySummaries[j].quantity });

            if (promotionResult.validity
              &&
              checkIsAllowPromotionVoucherToApply(
                currOutlet.allowStackedPromotionVoucher,
                APPLY_DISCOUNT_TYPE.PROMOTION,
                buy1Free1ItemSkuDictList[i],
                selectedPromoCodePromotion,
                {},
                promotionIdAppliedListTemp,
              )) {
              quantity += cartItemQuantitySummaries[j].quantity;
              if (buy1Free1ItemSkuDictList[i].key === cartItemQuantitySummaries[j].item.sku) {
                promotionIdAppliedListTemp.push(buy1Free1ItemSkuDictList[i].promotionId);
              }
              else if (buy1Free1ItemSkuDictList[i].key === cartItemQuantitySummaries[j].item.itemSku) {
                promotionIdAppliedListTemp.push(buy1Free1ItemSkuDictList[i].promotionId);
              }
            }
          }
        }

        if (
          quantity >= buy1Free1ItemSkuDictList[i].buyAmount
          &&
          !redeemedB1F1PromotionIdList.includes(
            buy1Free1ItemSkuDictList[i].promotionId,
          )
        ) {
          // need add more than 1, if lets said purchased 2, but the buyAmount condition is 1, then free 2 b1f1 sets

          var freeSetsNum = Math.floor(quantity / buy1Free1ItemSkuDictList[i].buyAmount);

          for (var k = 0; k < freeSetsNum; k++) {
            redeemedB1F1PromotionIdList.push(
              buy1Free1ItemSkuDictList[i].promotionId,
            );
          }
        }
      }
    }

    const buy1Free1CategoryNameDictList = Object.entries(
      buy1Free1CategoryNameDict,
    ).map(([key, value]) => ({
      ...value,
      key,
    }));

    for (var i = 0; i < buy1Free1CategoryNameDictList.length; i++) {
      if (
        true
        // buy1Free1CategoryNameDictList[i].applyBefore === APPLY_BEFORE.ORDER_PLACED || buy1Free1CategoryNameDictList[i].applyBefore === undefined
      ) {
        var quantity = 0;

        for (var j = 0; j < cartItemQuantitySummaries.length; j++) {
          if (
            selectedOutletItemCategoriesDict[
            cartItemQuantitySummaries[j].item.categoryId
            ] &&
            buy1Free1CategoryNameDictList[i].key ===
            selectedOutletItemCategoriesDict[
              cartItemQuantitySummaries[j].item.categoryId
            ].name
          ) {
            // means eligible item, accum quantity

            var promotionResult = checkApplyDiscountPerValidity(buy1Free1CategoryNameDictList[i], promotionIdAppliedListTemp, { quantity: cartItemQuantitySummaries[j].quantity });

            if (promotionResult.validity
              &&
              checkIsAllowPromotionVoucherToApply(
                currOutlet.allowStackedPromotionVoucher,
                APPLY_DISCOUNT_TYPE.PROMOTION,
                buy1Free1CategoryNameDictList[i],
                selectedPromoCodePromotion,
                {},
                promotionIdAppliedListTemp,
              )) {
              promotionIdAppliedListTemp.push(buy1Free1CategoryNameDictList[i].promotionId);
              quantity += cartItemQuantitySummaries[j].quantity;
            }
          }
        }

        if (
          quantity >= buy1Free1CategoryNameDictList[i].buyAmount
          &&
          !redeemedB1F1PromotionIdList.includes(
            buy1Free1CategoryNameDictList[i].promotionId,
          )
        ) {
          // redeemedB1F1PromotionIdList.push(
          //   buy1Free1CategoryNameDictList[i].promotionId,
          // );

          // need add more than 1, if lets said purchased 2, but the buyAmount condition is 1, then free 2 b1f1 sets

          var freeSetsNum = Math.floor(quantity / buy1Free1CategoryNameDictList[i].buyAmount);

          for (var k = 0; k < freeSetsNum; k++) {
            redeemedB1F1PromotionIdList.push(
              buy1Free1CategoryNameDictList[i].promotionId,
            );
          }
        }
      }
    }

    const buy1Free1ItemAllList = buy1Free1ItemSkuDictList.concat(
      buy1Free1CategoryNameDictList,
    );

    for (var i = 0; i < redeemedB1F1PromotionIdList.length; i++) {
      const b1f1Promotion = buy1Free1ItemAllList.find(
        (promotion) => promotion.promotionId === redeemedB1F1PromotionIdList[i],
      );

      if (
        b1f1Promotion &&
        b1f1Promotion.getVariation ===
        PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
      ) {
        for (var j = 0; j < b1f1Promotion.getVariationItemsSku.length; j++) {
          // const freeItem =
          //   selectedOutletItemsSkuDict[b1f1Promotion.getVariationItemsSku[j]];

          let foundItem = outletItems.find(findItem => findItem.sku === b1f1Promotion.getVariationItemsSku[j]);

          const freeItem =
            foundItem;

          if (freeItem) {
            const getItemPrice = b1f1Promotion.getPrice > 0 ? +(b1f1Promotion.getPrice / b1f1Promotion.getVariationItemsSku.length).toFixed(2) : b1f1Promotion.getPrice;

            tempCartItemsProcessed.push({
              isFreeItem: true,
              promotionId: b1f1Promotion.promotionId,
              promotionName: b1f1Promotion.campaignName,

              promotionIdList: [b1f1Promotion.promotionId],

              voucherId: '',
              voucherName: '',

              // priceOriginal: getItemPrice,
              priceOriginal: freeItem.price * b1f1Promotion.getAmount,

              ...(getItemPrice < freeItem.price && {
                discount: +(freeItem.price - getItemPrice).toFixed(2),

                discountPromotions: +(freeItem.price - getItemPrice).toFixed(2),
              }),

              itemId: freeItem.uniqueId,
              choices: [],
              remarks: '',
              fireOrder: false,
              // cartItemDate: Date.now(),
              cartItemDate: moment().add(i, 'second').valueOf(), // if multiple items might caused issue

              weight: (freeItem.weight ? freeItem.weight : 0.1) * b1f1Promotion.getAmount,

              image: freeItem.image,
              name: freeItem.name,
              itemName: freeItem.name,
              itemSku: freeItem.sku,
              // price: b1f1Promotion.getPrice * b1f1Promotion.getAmount, 
              price: getItemPrice, // try different logic
              quantity: b1f1Promotion.getAmount,
              addOns: [],

              deliveredAt: null,
              cookedAt: null,
              isChecked: false,

              orderType, // might relocate in other places in future

              prepareTime: freeItem.prepareTime * b1f1Promotion.getAmount,

              cartItemPointsDeducted: 0,
              cartItemAmountDeducted: 0,

              categoryId: freeItem.categoryId,

              printerAreaList: freeItem.printerAreaList || [],
              printingTypeList: freeItem.printingTypeList || null,

              isDocket: freeItem.isDocket || false,
              printDocketQuantity: freeItem.printDocketQuantity || 1,

              priceBundle: getItemPrice,

              originCartItemId: freeItem.originCartItemId || null,
            });

            // tempTotalPrice += b1f1Promotion.getPrice * b1f1Promotion.getAmount;

            // tempTotalPrice += getItemPrice;
            tempTotalPrice = BigNumber(tempTotalPrice).plus(getItemPrice).toNumber();
            tempTotalPrepareTime +=
              freeItem.prepareTime * b1f1Promotion.getAmount;
          }
        }
      }
    }

    //////////////////////////////////////////////////////////

    // console.log('tempOutletsTaxDict');
    // console.log(tempOutletsTaxDict);
    // console.log('tempCartOutletItemsDict');
    // console.log(tempCartOutletItemsDict);
    // console.log('tempCartOutletItemAddOnDict');
    // console.log(tempCartOutletItemAddOnDict);
    // console.log('tempCartOutletItemAddOnChoiceDict');
    // console.log(tempCartOutletItemAddOnChoiceDict);
    // console.log('tempCartItemsProcessed');
    // console.log(tempCartItemsProcessed);

    //////////////////////////////////////////////////////////////////////

    CommonStore.update((s) => {
      s.outletsTaxDict = tempOutletsTaxDict;
      s.cartOutletItemsDict = tempCartOutletItemsDict;
      s.cartOutletItemAddOnDict = tempCartOutletItemAddOnDict;
      s.cartOutletItemAddOnChoiceDict = tempCartOutletItemAddOnChoiceDict;
      s.cartItemsProcessed = tempCartItemsProcessed;
    });

    setPromotionIdAppliedList(promotionIdAppliedListTemp);

    setTotalPrice(tempTotalPrice);

    // if (tempOutletsTaxDict[currOutletId]) {
    //   setTotalTax(tempTotalPrice * tempOutletsTaxDict[currOutletId].rate);
    // }
    // else {
    //   setTotalTax(tempTotalPrice * 0.06);
    // }

    if (checkToApplyTaxOrNot(currOutlet, orderType, orderTypeSub)) {
      // setTotalTax(tempTotalPrice * currOutlet.taxRate);
      setTotalTax(BigNumber(tempTotalPrice).multipliedBy(currOutlet.taxRate).toNumber());
    }
    else {
      setTotalTax(0);
    }

    if (
      checkToApplyScOrNot(currOutlet, orderType, orderTypeSub)
      // currOutlet.scActive && currOutlet.scOrderTypes.includes(orderType)
      // &&
      // (currOutlet.scOrderTypeDetails === undefined || (currOutlet.scOrderTypeDetails && currOutlet.scOrderTypeDetails.includes(ORDER_TYPE_DETAILS.POS)))
    ) {
      // setTotalSc(tempTotalPrice * currOutlet.scRate);
      setTotalSc(BigNumber(tempTotalPrice).multipliedBy(currOutlet.scRate).toNumber());
    }
    else {
      setTotalSc(0);
    }

    setTotalPrepareTime(tempTotalPrepareTime);

    setDiscountPromotionsTotal(discountPromotionsTotalTemp);

    setIsCartLoading(false);
  };

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={{
          width: windowWidth * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Cart
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const MenuItem = (param) => {
    ApiClient.GET(API.getItemAddOnChoice + param).then((result) => {
      setState({ menuItemDetails: result });
    });
  };

  // componentDidMount() {
  //   const { navigation } = props;
  //   _unsubscribe = navigation.addListener('focus', () => {
  //     getCartItem()
  //   });
  //   getCartItem();
  //   Cart.setRefreshCartPage(getCartItem.bind(this));
  //   // console.log("CART OUTLET", outletData)
  //   ApiClient.GET(API.merchantMenu + outletData.id).then(
  //     (result) => {
  //       if (result != undefined) {
  //         if (result.length > 0) {
  //           setState({
  //             category: result[0].category,
  //             menu: result[0].items,
  //           });
  //         }
  //         setState({ outletMenu: result });
  //       }
  //       else {
  //         setState({
  //           category: result[0].category,
  //           menu: result[0].items,
  //         });
  //         setState({ outletMenu: [] });
  //       }
  //     }
  //   );

  //   getPopular()
  // }

  // componentWillUnmount() {
  //   _unsubscribe();
  // }

  // function here
  const sum = (key) => {
    return reduce((a, b) => a + (b[key] || 0), 0);
  };

  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() }, () => {
      calculateFinalTotal();
    });
    // console.log('888', Cart.getCartItem());
  };

  const checkDuplicate = (item, i, j) => {
    for (const r of item) {
      if (r[0] == i && r[1] == j) {
        return true;
      }
    }
    return false;
  };

  const getPopular = () => {
    var newList = [];
    var randomList = [];
    ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
      var maxItem = 0;
      for (const item of result) {
        maxItem += item.items.length;
      }
      var maxIteration = 5;
      if (maxItem < 5) {
        maxIteration = maxIteration;
      }
      var k;
      for (k = 0; k < maxIteration; k++) {
        var i = Math.floor(Math.random() * result.length);
        var j = Math.floor(Math.random() * result[i].items.length);

        while (checkDuplicate(randomList, i, j)) {
          i = Math.floor(Math.random() * result.length);
          j = Math.floor(Math.random() * result[i].items.length);
        }
        newList.push(result[i].items[j]);
        randomList.push([i, j]);
        setState({ popular: newList });
      }
    });
  };

  const goToPopular = (item) => {
    // console.log(outletData, 'outletData');
    props.navigation.navigate('MenuItemDetails', {
      refresh: refresh.bind(this),
      menuItem: item,
      outletData,
      test,
    });
  };

  const renderPopularItem = ({ item, index }) => {
    return (
      <View
        style={{
          width: 180,
          height: 80,
          borderWidth: StyleSheet.hairlineWidth,
          marginRight: 10,
          borderRadius: 10,
          justifyContent: 'space-around',
        }}>
        <Text
          numberOfLines={2}
          style={{
            fontSize: switchMerchant ? 10 : 16,
            fontWeight: '400',
            marginLeft: 10,
          }}>
          {item.name}
        </Text>
        <View style={{ flexDirection: 'row', marginLeft: 10, marginTop: 10 }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 16, color: '#9c9c9c' }}>
            {item.price}
          </Text>
          <TouchableOpacity
            style={{ marginLeft: 70 }}
            onPress={() => {
              goToPopular(item);
            }}>
            <Close name="pluscircle" size={22} color={Colors.primaryColor} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const refresh = () => {
    setState({ refresh: true });
  };

  const onChangeQty = (e, id) => {
    // const cartItem = cartItem;
    const cartItem = Cart.getCartItem();
    const item = cartItem.find((obj) => obj.itemId === id);
    item.quantity -= e;
    if (item.quantity == 0) {
      Cart.deleteCartItem(id);
    } else {
      setState({
        cartItem,
      });
      Cart.updateCartItem(
        Cart.getCartItem().find((obj) => obj.itemId === id),
        item,
      );
    }
    // console.log(
    //   'CART',
    //   Cart.getCartItem().find((obj) => obj.itemId === id),
    // );
  };

  const optional = (id) => {
    const cartItem = cartItem;
    // console.log(cartItem);
    const item = cartItem.find((obj) => obj.itemId === id);
    if (item.fireOrder == 0) {
      item.fireOrder = 1;
    } else if (item.fireOrder == 1) {
      item.fireOrder = 0;
    }
    setState({
      cartItem,
    });
  };

  const extend = (id, day) => {
    const body = {
      parkingId: id,
      dayExtend: day,
      paymentMethod: '',
    };
    // console.log('PARKING BODY', body);
    ApiClient.POST(API.extendParking, body).then((result) => {
      // console.log(result);
      if (result != null) {
        Alert.alert(
          'Success',
          'You have extended successfully',
          [
            {
              text: 'OK',
              onPress: () => {
                props.navigation.navigate('ConfirmOrder', {
                  outletData,
                });
              },
            },
          ],
          { cancelable: false },
        );
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    });
  };

  const newPurchase = (outletId, itemId, qty) => {
    const body = {
      userId: User.getUserId(),
      outletId,
      itemId,
      quantity: qty,
      waiterId: 1,
    };
    ApiClient.POST(API.createUserParking, body).then((result) => {
      // console.log(result);
      if (result != null) {
        Alert.alert(
          'Success',
          'You have parked successfully',
          [
            {
              text: 'OK',
              onPress: () => {
                props.navigation.navigate('ConfirmOrder', {
                  outletData,
                });
              },
            },
          ],
          { cancelable: false },
        );
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    });
  };

  const clearCartItems = async () => {
    // await AsyncStorage.removeItem(`${firebaseUid}.cartItems`);

    CommonStore.update((s) => {
      s.cartItems = [];
      s.cartItemsProcessed = [];
    });

    if (selectedOutletTable && selectedOutletTable.uniqueId) {
      CommonStore.update((s) => {
        s.cartItemsTableIdDict = {
          ...cartItemsTableIdDict,
          [selectedOutletTable.uniqueId]: [],
        };
      });
    }
  };

  const placeUserOrder = async () => {
    // var orderBodyType = '';
    // switch (Cart.getOrderType()) {
    //   case 0:
    //     orderBodyType = 'Dine In';
    //     break;
    //   case 1:
    //     orderBodyType = 'Take Away';
    //     break;
    //   case 2:
    //     orderBodyType = 'Pick Up';
    //     break;
    // }

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // const outletSnapshot = await firestore()
    //   .collection(Collections.Outlet)
    //   .where(
    //     'uniqueId',
    //     '==',
    //     cartOutletItemsDict[cartItems[0].itemId].outletId,
    //   )
    //   .limit(1)
    //   .get();

    // var merchant = {};
    // var outlet = {};

    // if (!outletSnapshot.empty) {
    //   outlet = outletSnapshot.docs[0].data();

    //   const merchantSnapshot = await firestore()
    //     .collection(Collections.Merchant)
    //     .where('uniqueId', '==', outlet.merchantId)
    //     .limit(1)
    //     .get();

    //   if (!merchantSnapshot.empty) {
    //     merchant = merchantSnapshot.docs[0].data();
    //   }
    // }

    ////////////////////////////////////////////////////////

    // generate register url for this order

    // var orderIdEncrypted = hashids.encodeHex(
    //   selectedOutletTable.uniqueId.replaceAll('-', ''),
    // );

    ////////////////////////////////////////////////////////
    if ((selectedOutletTable.uniqueId !== undefined && orderType === ORDER_TYPE.DINEIN) ||
      (selectedOutletTable.uniqueId === undefined && orderType === ORDER_TYPE.PICKUP)) {
      if (selectedPromoCodePromotion && selectedPromoCodePromotion.uniqueId) {
        if (promotionsDict[selectedPromoCodePromotion.uniqueId] &&
          promotionsDict[selectedPromoCodePromotion.uniqueId].promoCodeUsageLimit <= 0) {
          Alert.alert('Info', `Promotion '${selectedPromoCodePromotion.campaignName}' has been fully redeemed.`);

          CommonStore.update(s => {
            s.isLoading = false;
          });

          global.isPlacingOrder = false;

          return;
        }

        if (promotionsDict[selectedPromoCodePromotion.uniqueId] &&
          promotionsDict[selectedPromoCodePromotion.uniqueId].promoCodeCustomerLimit <= 0) {
          Alert.alert('Info', `Promotion '${selectedPromoCodePromotion.campaignName}' usage limit per customer must be more than 0 to proceed.`);

          CommonStore.update(s => {
            s.isLoading = false;
          });

          global.isPlacingOrder = false;

          return;
        }
      }

      ////////////////////////////////////////////////////////

      const orderDateTemp = Date.now();

      var body = {
        // cartItems: cartItems,
        cartItems: cartItemsProcessed,
        promotionIdList: promotionIdAppliedList,
        promoCodePromotionIdList: promotionIdAppliedList.filter(promotionId => {
          if (availablePromoCodePromotions.find(promoCodePromotion => promoCodePromotion.uniqueId === promotionId)) {
            return true;
          }
          else {
            return false;
          }
        }),

        weight: cartItemsProcessed.reduce((accum, item) => {
          return accum + (item.weight ? item.weight : 0.1);
        }, 0),

        // tableId: Cart.getTableNumber(),
        // outletId: Cart.getOutletId(),
        // type: Cart.getOrderType() == 0 ? "Dine In" : "Take Away",
        orderType,
        paymentMethod: 'Online Banking',
        userVoucherId: null,
        userAddressId: selectedUserAddress ? selectedUserAddress.uniqueId : null,
        orderDate: orderDateTemp,
        // voucherType: "",
        // customTable: "",
        // sessionId: 0,
        // remarks: null,
        // collectionTime: rev_date === undefined ? new Date() : new Date(rev_date),
        totalPrice,
        outletId: cartOutletItemsDict[cartItems[0].itemId].outletId,

        dineInRequiredAuthorization: currOutlet.dineInRequiredAuthorization ? currOutlet.dineInRequiredAuthorization : false,

        merchantId,
        outletCover: currOutlet.cover,
        merchantLogo,
        outletName: currOutlet.name,
        merchantName,

        tableId: orderType !== ORDER_TYPE.DINEIN ? '' : (selectedOutletTable.uniqueId || ''),
        tablePax: orderType !== ORDER_TYPE.DINEIN ? 0 : (selectedOutletTable.seated || 0),
        tableCode: orderType !== ORDER_TYPE.DINEIN ? '' : (selectedOutletTable.code || ''),
        tax: checkToApplyTaxOrNot(currOutlet, orderType, orderTypeSub) ? totalTax : 0,
        taxId: currOutlet && outletsTaxDict[currOutlet.uniqueId] ? outletsTaxDict[currOutlet.uniqueId].uniqueId : '',
        sc: checkToApplyScOrNot(currOutlet, orderType, orderTypeSub) ? totalSc : 0,
        discount: totalDiscount,
        discountPromotionsTotal,

        waiterName: name,
        waiterId: firebaseUid,

        totalPrepareTime,
        estimatedPreparedDate: moment(orderDateTemp)
          .add(totalPrepareTime, 'second')
          .valueOf(),

        remarks: '',

        outletAddress: currOutlet.address,
        outletPhone: currOutlet.phone,
        outletTaxId: currOutlet && outletsTaxDict[currOutlet.uniqueId]
          ? outletsTaxDict[currOutlet.uniqueId].uniqueId
          : '',
        outletTaxNumber: currOutlet && outletsTaxDict[currOutlet.uniqueId]
          ? outletsTaxDict[currOutlet.uniqueId].taxNumber
          : '',
        outletTaxName: currOutlet && outletsTaxDict[currOutlet.uniqueId] ? outletsTaxDict[currOutlet.uniqueId].name : 'SST',
        // outletTaxRate: (currOutlet && outletsTaxDict[currOutlet.uniqueId]) ? outletsTaxDict[currOutlet.uniqueId].rate : 0.06,
        outletTaxRate: checkToApplyTaxOrNot(currOutlet, orderType, orderTypeSub) ? currOutlet.taxRate : 0,

        outletTaxOrderTypes: currOutlet.taxOrderTypes ? currOutlet.taxOrderTypes : '',

        outletScRate: checkToApplyScOrNot(currOutlet, orderType, orderTypeSub) ? currOutlet.scRate : 0,

        outletScOrderTypes: currOutlet.scOrderTypes ? currOutlet.scOrderTypes : [ORDER_TYPE.DINEIN],

        paymentDetails: null,

        tableQRUrl: currTableQRUrl ? currTableQRUrl : '',

        deliveryPackagingFee: currOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY ? currOutlet.deliveryPackagingFee : 0,
        pickupPackagingFee: currOutlet.pickupPackagingFee && (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL) ? currOutlet.pickupPackagingFee : 0,

        isCounterOrdering,

        noUserId: true,

        appType: APP_TYPE.MERCHANT,

        scName: currOutlet.scName ? currOutlet.scName : '',

        isLater: isLater ? isLater : 'NOW',
        scheduleDateTime: scheduleDateTime ? scheduleDateTime : null,

        packed: false,
      };

      // (
      //   netInfo.isInternetReachable && netInfo.isConnected
      //     ?
      //     ApiClient.POST(API.createUserOrder, body)
      //     :
      //     APILocal.createUserOrder({
      //       body: body,
      //       uid: firebaseUid,
      //       role: role,
      //     })
      // )
      APILocal.createUserOrder({
        body,
        uid: firebaseUid,
        role,
      })
        .then(async (result) => {
          // console.log('placeOrder', result);

          //////////////////////////////////////////

          // reset promo code selection

          CommonStore.update(s => {
            s.selectedPromoCodePromotion = {};
          });

          setSelectedPromoCodePromotionId('');

          //////////////////////////////////////////

          // CommonStore.update((s) => {
          //   s.isLoading = false;
          // });

          // if (result) {
          //   await clearCartItems();

          //   if (userCart.userId) {
          //     await deleteUserCart();
          //   }

          //   Alert.alert(
          //     'Success',
          //     'Your order has been placed',
          //     [
          //       {
          //         text: 'OK',
          //         onPress: () => {
          //           props.navigation.navigate('Table');
          //         },
          //       },
          //     ],
          //     { cancelable: false },
          //   );

          //   User.getRefreshCurrentAction();
          // }

          // if (result &&
          //   result.uniqueId &&
          //   !netInfo.isInternetReachable && netInfo.isConnected) {
          //   await printUserOrder({
          //     orderId: result.uniqueId,
          //   },
          //     false,
          //     [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
          //     false,
          //     false,
          //     false,
          //     netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
          //   );
          // }        

          if ((result && result.uniqueId) &&
            (isCounterOrdering || orderType === ORDER_TYPE.PICKUP)) {
            setTimeout(async () => {
              CommonStore.update((s) => {
                s.isLoading = false;
              });

              Alert.alert(
                'Success',
                'Your order has been placed',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      CommonStore.update(s => {
                        s.isCheckingOutTakeaway = true;

                        s.checkingOutTakeawayOrder = result;

                        s.timestamp = Date.now();

                        s.currPage = 'Table';
                      }, () => {
                        navigation.navigate('Table');
                      });

                      // props.navigation.navigate('Table');
                    },
                  },
                ],
                { cancelable: false },
              );

              User.getRefreshCurrentAction();

              await clearCartItems();

              if (userCart.userId) {
                await deleteUserCart();
              }
            }, 100);
          }

          global.isPlacingOrder = false;

          // var isNeededToPrintHere = false;

          // if (netInfo.isInternetReachable && netInfo.isConnected) {
          //   // if (result.orderType === ORDER_TYPE.DINEIN) {
          //   //   isNeededToPrintHere = true;
          //   // }
          // }
          // else {
          //   isNeededToPrintHere = true;
          // }

          // if (isNeededToPrintHere) {
          //   await printUserOrder(
          //     {
          //       orderData: result,
          //     },
          //     false,
          //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
          //     false,
          //     false,
          //     false,
          //     { isInternetReachable: true, isConnected: true },
          //   );

          //   await printUserOrder(
          //     {
          //       orderData: result,
          //     },
          //     false,
          //     [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
          //     false,
          //     false,
          //     false,
          //     { isInternetReachable: true, isConnected: true },
          //   );
          // }
        });

      if (!isCounterOrdering && orderType !== ORDER_TYPE.PICKUP) {
        setTimeout(async () => {
          CommonStore.update((s) => {
            s.isLoading = false;
          });

          await clearCartItems();

          if (userCart.userId) {
            await deleteUserCart();
          }

          // Alert.alert(
          //   'Success',
          //   'Your order has been placed',
          //   [
          //     {
          //       text: 'OK',
          //       onPress: () => {
          //         props.navigation.navigate('Table');
          //       },
          //     },
          //   ],
          //   { cancelable: false },
          // );
          props.navigation.navigate('Table');
          User.getRefreshCurrentAction();
        }, 100);
      }
    }
    else {
      if (orderType === ORDER_TYPE.DINEIN) {
        Alert.alert('Info', 'Please select a table before placing the order, or restart the app')
      }
      if (orderType === ORDER_TYPE.PICKUP) {
        Alert.alert('Info', 'Please unselect the table before placing the order, or restart the app')
      }

      CommonStore.update(s => {
        s.isLoading = false;
      });

      global.isPlacingOrder = false;
    }
  };

  const placeOrder = () => {
    var orderBodyType = '';
    switch (Cart.getOrderType()) {
      case 0:
        orderBodyType = 'Dine In';
        break;
      case 1:
        orderBodyType = 'Take Away';
        break;
      case 2:
        orderBodyType = 'Pick Up';
        break;
    }

    var body = {
      items: Cart.getCartItem(),
      tableId: Cart.getTableNumber(),
      outletId: Cart.getOutletId(),
      // type: Cart.getOrderType() == 0 ? "Dine In" : "Take Away",
      type: orderBodyType,
      paymentMethod: 'Online Banking',
      voucherId: '',
      voucherType: '',
      customTable: '',
      sessionId: 0,
      remarks: null,
      collectionTime: rev_date === undefined ? new Date() : new Date(rev_date),
    };
    // console.log('PLACE ORDER BODY', body);

    ApiClient.POST(API.createOrder, body).then((result) => {
      // console.log('placeOrder', result);
      if (result.success == true) {
        if (type == 1) {
          orderDelivery(result);
        } else if (type == 2) {
          orderPickUp(result);
        }

        Alert.alert(
          'Success',
          'Your order has been placed',
          [
            {
              text: 'OK',
              onPress: () => {
                //if navFrom Takeaway then jump to Home
                // if (navFrom == "TAKEAWAY") {
                //   props.navigation.jumpTo('Home')
                // }
                // else {
                //   props.navigation.popToTop()
                // }
                props.navigation.navigate('Table');
              },
            },
          ],
          { cancelable: false },
        );
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    });
  };

  const orderDelivery = (result) => {
    var deliverBody = {
      orderId: result.id,
      userId: User.getUserId(),
      addressId: Cart.getDeliveryAddress().id,
    };
    ApiClient.POST(API.createOrderDelivery, deliverBody).then((result) => {
      // console.log('order delivery', result);
      var body = {
        serviceType: 'MOTORCYCLE',
        specialRequests: [],
        stops: [
          {
            // Location information for pick-up point
            location: {
              lat: currOutlet.latlng.split(',')[0],
              lng: currOutlet.latlng.split(',')[1],
            },
            addresses: {
              ms_MY: {
                displayString: currOutlet.address,
                country: 'MY_KUL',
              },
            },
          },
          {
            // Location information for drop-off point (#1)
            location: {
              lat: Cart.getDeliveryAddress().lat,
              lng: Cart.getDeliveryAddress().lng,
            },
            addresses: {
              ms_MY: {
                displayString: Cart.getDeliveryAddress().address,
                country: 'MY_KUL',
              },
            },
          },
        ],
        // Pick-up point copntact details
        requesterContact: {
          name: 'Chris Wong', //get outlet person in charge?
          phone: '0376886555', //or get waiter?
        },
        deliveries: [
          {
            // Contact information at the drop-off point (#1)
            toStop: 1,
            toContact: {
              name: User.getName(),
              phone: User.getUserData().number,
            },
            remarks: Cart.getDeliveryAddress().note,
          },
        ],
      };
      if (UTCRevDate != undefined) {
        const scheduleAt = UTCRevDate;
        // console.log('scheduleAt', scheduleAt);
        body.scheduleAt = scheduleAt;
        // console.log('SCHEDULE BODY', body);
        getScheduleQuotation(body, result);
      } else {
        // console.log('NOW BODY ', body);
        placeDelivery(Cart.getDeliveryQuotation(), body, result);
      }
    });
  };

  const orderPickUp = (result) => {
    var pickUpBody = {
      orderId: result.id,
      userId: User.getUserId(),
      // addressId: Cart.getDeliveryAddress().id,
    };
    ApiClient.POST(API.createOrderPickUp, pickUpBody).then((result) => {
      // console.log('order pickup', result);
    });
  };

  const getScheduleQuotation = (body, order) => {
    ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
      // console.log('quotation result', result);
      placeDelivery(result, body, order);
    });
  };

  const placeDelivery = (quotation, body, order) => {
    // console.log('Placing delivery', quotation);
    body.quotedTotalFee = {
      amount: quotation.totalFee,
      currency: quotation.totalFeeCurrency,
    };
    // console.log('LALA ORDER BODY', body);
    ApiClient.POST(API.lalamovePlaceOrder, body).then((result) => {
      // console.log('lalamvoe place order', result);
      if (result) {
        const lalaOrderId = result.orderRef;
        ApiClient.GET(API.lalamoveGetOrderStatus + result.orderRef).then(
          (result) => {
            // console.log('lalamove order status', result);

            var updateBody = {
              orderId: order.orderId,
              lalaOrderId,
              orderStatus: result.status,
            };
            // console.log('UPDATE BODY', updateBody);
            ApiClient.PATCH(API.updateOrderDelivery, updateBody).then(
              (result) => {
                // console.log('update order delivery', result);
              },
            );
          },
        );
      }
    });
  };

  const calculateFinalTotal = () => {
    // console.log('cartItem', cartItem);
    const totalFloat = parseFloat(
      cartItem.reduce(
        (accumulator, current) =>
          accumulator + current.price * current.quantity,
        0,
      ),
    );

    const totalTax = parseFloat(
      (cartItem
        .reduce(
          (accumulator, current) =>
            accumulator + current.price * current.quantity,
          0,
        )
        .toFixed(2) /
        parseFloat(100)) *
      parseFloat(Cart.getTax()),
    );

    //+ '001' //.toFixed() bug, occour only if last significant digit is 5 and if fixing to only 1 less digit, i.e. 1.75.toFixed(1) => 1.7 instead of 1.8
    var taxStr = totalTax.toString().split('.');
    // console.log(taxStr);
    if (taxStr.length > 1) {
      taxStr[1] += '001';
    }
    const taxFloat = parseFloat(`${taxStr[0]}.${taxStr[1]}`);

    setState({
      totalFloat: parseFloat(totalFloat).toFixed(2),
      taxFloat: parseFloat(taxFloat).toFixed(2),
    });
  };

  const generateScheduleTime = () => {
    const now = new Date();
    var m = ((((now.getMinutes() + 7.5) / 15) | 0) * 15) % 60;
    var h = (((now.getMinutes() / 105 + 0.5) | 0) + now.getHours()) % 24;
    const nearestNow = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      h,
      m,
    );
    var startTime = moment(nearestNow, 'YYYY-MM-DD hh:mm');
    var endTime = moment(
      new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 1,
        now.getHours(),
        now.getMinutes(),
      ),
      'YYYY-MM-DD hh:mm a',
    );

    var result = [];
    var current = moment(startTime);

    var tempstr = current.format('llll').split(',');
    var daystr = tempstr[0];
    var datestr = tempstr[1].split(' ');
    var yeartimestr = tempstr[2].split(' ');
    var currentNearestTime =
      `${daystr
      } ${datestr[2]
      } ${datestr[1]
      },${yeartimestr[2]
      },${yeartimestr[1]
      },${yeartimestr[3]}`;
    result.push(currentNearestTime);

    current.add(15, 'minutes');
    while (current <= endTime) {
      var tempstr = current.format('llll').split(',');
      // console.log('tempstr', tempstr);
      var daystr = tempstr[0];
      var datestr = tempstr[1].split(' ');
      var yeartimestr = tempstr[2].split(' ');
      var finalstr =
        `${daystr
        } ${datestr[2]
        } ${datestr[1]
        },${yeartimestr[2]
        },${yeartimestr[1]
        },${yeartimestr[3]}`;
      result.push(finalstr);

      current.add(15, 'minutes');
    }
    // console.log('schedule result', result);
    setState({
      schedulteTimeList: result,
      schedulteTimeSelected: result[0],
      currentNearestTime,
    });
  };

  const renderSuccess = () => (
    <View style={{ backgroundColor: 'blue' }}>
      <Image source={require('../assets/image/dineinGrey.png')} />

    </View>
  );

  const renderSchedule = (item) => (
    <View
      style={{
        flexDirection: 'row',
        padding: 20,
        justifyContent: 'space-between',
        alignContent: 'center',
      }}>
      <View style={{ justifyContent: 'flex-start', marginHorizontal: 10 }}>
        <Text>
          {currentNearestTime == item.toString()
            ? 'TODAY'
            : item.toString().split(',')[0]}
        </Text>
      </View>
      <View style={{ justifyContent: 'flex-end', marginHorizontal: 10 }}>
        <Text>
          {currentNearestTime == item.toString()
            ? 'ASAP'
            : item.toString().split(',')[1]}
        </Text>
      </View>
    </View>
  );

  const proceedToMenuItemDetailsForUpdating = async (item) => {
    // const outletItemSnapshot = await firestore()
    //   .collection(Collections.OutletItem)
    //   .where('uniqueId', '==', item.itemId)
    //   .limit(1)
    //   .get();

    // if (!outletItemSnapshot.empty) {
    //   const outletItem = outletItemSnapshot.docs[0].data();

    //   CommonStore.update((s) => {
    //     s.selectedOutletItem = outletItem;

    //     s.onUpdatingCartItem = item;
    //   });

    //   props.navigation.navigate('MenuItemDetails', {
    //     refresh: refresh.bind(this),
    //     menuItem: outletItem,
    //     cartItem: item,
    //     outletData: currOutlet,
    //   });
    // }

    let foundItem = outletItems.find(findItem => findItem.uniqueId === item.itemId);

    if (foundItem) {
      const outletItem = foundItem;

      CommonStore.update((s) => {
        s.selectedOutletItem = outletItem;

        s.onUpdatingCartItem = item;
      });

      props.navigation.navigate('MenuItemDetails', {
        refresh: refresh.bind(this),
        menuItem: outletItem,
        cartItem: item,
        outletData: currOutlet,

        orderType,
        orderTypeSub,
      });
    }
  };

  const removeFromCartItems = async (item) => {
    var updateCartItemIndex = 0;

    for (var i = 0; i < cartItems.length; i++) {
      if (
        cartItems[i].itemId === item.itemId &&
        cartItems[i].cartItemDate === item.cartItemDate
      ) {
        updateCartItemIndex = i;
      }
    }

    const newCartItems = [
      ...cartItems.slice(0, updateCartItemIndex),
      ...cartItems.slice(updateCartItemIndex + 1),
    ];

    if (newCartItems.length > 0) {
      // await AsyncStorage.setItem(
      //   `${firebaseUid}.cartItems`,
      //   JSON.stringify(newCartItems),
      // );
    } else {
      // await AsyncStorage.removeItem(`${firebaseUid}.cartItems`);
    }

    CommonStore.update((s) => {
      s.cartItems = newCartItems;
    });

    if (newCartItems.length === 0)
      props.navigation.goBack();
  };

  const renderItem = (params) => {
    // var item = {
    //   ...params.item,
    // };

    // if (cartOutletItemsDict[item.itemId]) {
    //   item.image = cartOutletItemsDict[item.itemId].image;
    //   item.name = cartOutletItemsDict[item.itemId].name;
    //   item.itemName = cartOutletItemsDict[item.itemId].name;

    // }

    const { item } = params;

    // console.log('item');
    // console.log(item);

    var overrideCategoryPrice = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    /////////////////////////////////////////////////////////

    return <>
      <View
        style={{
          flexDirection: 'row',
          paddingVertical: 10,
          borderColor: Colors.descriptionColor,
          justifyContent: 'flex-start',
          alignContent: 'center',
          alignItems: 'center',
          display: 'flex',
          // borderBottomWidth: StyleSheet.hairlineWidth,
          marginBottom: 5,
          //backgroundColor: 'green',
        }}>
        <View
          style={[
            {
              marginRight: 15,
              backgroundColor: Colors.secondaryColor,
              //backgroundColor: 'red',
              borderRadius: 5,
              alignSelf: 'flex-start',
              // flex: 1,
            },
            item.image
              ? {
                // display: 'flex',
                // alignItems: 'center',
                // justifyContent: 'center',
              }
              : {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                paddingTop: 5,
                paddingBottom: 5,
              },
          ]}>
          {item.image ? (
            <AsyncImage
              source={{ uri: item.image }}
              item={item}
              style={{
                // width: 70,
                // height: 70,
                width: switchMerchant
                  ? windowWidth * 0.08
                  : windowWidth * 0.11,
                height: switchMerchant
                  ? windowWidth * 0.08
                  : windowWidth * 0.11,
                borderRadius: 5,
              }}
            />
          ) : (
            <View style={{
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: Colors.secondaryColor,
              width: switchMerchant
                ? windowWidth * 0.08
                : windowWidth * 0.11,
              height: switchMerchant
                ? windowWidth * 0.068
                : windowWidth * 0.11,
              borderRadius: 5,
            }}>
              <Ionicons name="fast-food-outline" size={switchMerchant ? 30 : 50} />
            </View>
          )}
        </View>

        <View
          style={{
            //backgroundColor: 'red',
            width: '50%',
          }}>
          <View
            style={{
              display: 'flex',
              // backgroundColor: 'red',
            }}>
            <Text
              style={{
                // fontWeight: "700",
                fontSize: switchMerchant ? 10 : 19,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.mainTxtColor,
              }}>
              {item.name}
            </Text>

            {/* {item.options ? item.options.map((item, index) => {
            return (
              (item.quantity > 0 ? <Text style={{ color: Colors.descriptionColor, marginTop: 5, fontWeight: '400', fontSize: 11, fontFamily: "NunitoSans-Regular" }}>
                {" "}
              + {item.quantity} ({item.itemName})
            </Text> : null)
            );
          }) : null} */}

            {item.addOns &&
              item.addOns.length > 0 &&
              item.addOns.map((addOn, index) => {
                const addOnChoices = addOn.choiceNames.join(', ');

                return (
                  <Text
                    key={index}
                    style={{
                      color: Colors.descriptionColor,
                      marginTop: 3,
                      // fontWeight: "700",
                      fontSize: switchMerchant ? 10 : 14,
                      marginBottom: 3,
                      fontFamily: 'NunitoSans-Regular',
                    }}>
                    {/* Size: {item.size == "small" ? "Small" : item.size == "medium" ? "Medium" : item.size == "big" ? "Big" : null} */}
                    {addOn.quantities > 0 ? (
                      <>
                        {`+ ${addOn.quantities.reduce(
                          (accum, value) => accum + value,
                          0,
                        )} (${addOnChoices}) (+RM${addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                          .toFixed(2)
                          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')})`}
                      </>
                    ) : (
                      <>
                        {`${addOn.name
                          } (${addOnChoices}) (+RM${addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                            .toFixed(2)
                            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')})`}
                      </>
                    )
                    }
                  </Text>
                );
              })}
          </View>

          {/* <Text style={{ 
          fontWeight: "700", 
          fontSize: 19, 
          fontFamily: 
          "NunitoSans-Regular" 
        }}>
          {test1 == 1 ? item.name : test2 == 1 ? item.name : ('x' + item.quantity + " " + item.name)}
        </Text> */}

          {/* {test1 == 1 ?
          <Text style={{ fontSize: 14, fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}> ({item.quantity} days extension)
        </Text>
          : test2 == 1 ? <Text style={{ fontSize: 14, fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}> x{item.quantity} mugs
          </Text> : null} */}

          {item.remarks ? (
            <Text
              style={{
                color: Colors.descriptionColor,
                marginTop: 0,
                fontWeight: '400',
                fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Italic',
              }}>
              Remarks: {item.remarks}
            </Text>
          ) : null}

          {/* {type == 0 ? <TouchableOpacity
            onPress={() => {
              optional(item.itemId);
            }}
          >
            <Text
              style={{
                color: Colors.primaryColor,
                marginTop: 3,
                fontWeight: "700",
                fontSize: 14,
                marginBottom: 3,
                fontFamily: "NunitoSans-Regular"
              }}
            >
              {item.fireOrder == 0 ? "Serve now" : item.fireOrder == 1 ? "Serve later" : null}
            </Text>
          </TouchableOpacity> : null} */}
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-end',
            width: '5%',
          }}>
          <Text
            style={{
              color: Colors.descriptionColor,
              fontSize: switchMerchant ? 10 : 16,
              fontFamily: 'NunitoSans-Regular',
            }}>
            x{item.quantity}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `${UNIT_TYPE_SHORT[item.unitType]}` : ''}
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-end',
            width: '20%',
          }}>
          <View
            style={{
              flexDirection: 'row',
              width: '70%',
              justifyContent: 'space-between',
            }}>
            <Text
              style={{
                color: Colors.descriptionColor,
                fontSize: switchMerchant ? 10 : 16,
                fontFamily: 'NunitoSans-Regular',
              }}>
              RM
            </Text>
            <Text
              style={{
                color: Colors.descriptionColor,
                fontSize: switchMerchant ? 10 : 16,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {
                (
                  (overrideItemPriceSkuDict[item.sku] !== undefined ||
                    overrideCategoryPrice !== undefined)
                    ?
                    overrideItemPriceSkuDict[item.sku] !== undefined ?
                      parseFloat(
                        overrideItemPriceSkuDict[item.sku].overridePrice
                      ).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                      :
                      parseFloat(
                        overrideCategoryPrice,
                      ).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                    :
                    parseFloat(item.price).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                )
              }
            </Text>
            {/* <Text
              style={{
                color: Colors.descriptionColor,
                fontSize: switchMerchant ? 10 : 16,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {parseFloat(item.price)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text> */}
          </View>
          {/* <TouchableOpacity
          onPress={() => {
            if (item.quantity <= 1) {
              Cart.deleteCartItem(item.itemId, item);
              getCartItem();
            }
            else {
              setState({
                visible: true,
                editingItemId: item.itemId,
                qty: item.quantity,
                value: item.quantity,
              });
            }
          }}
        >
          <Entypo name="cross" size={28} color="red" />
        </TouchableOpacity> */}
        </View>

        <View
          style={{
            width: '10%',
            // marginLeft: 30,
            alignItems: 'center',
          }}>
          {
            (!item.isFreeItem) ? (
              <>
                <TouchableOpacity
                  onPress={() => {
                    // console.log('ITEM@@@@@@@@@@@@@@@@@@@@@@@@@@@@', item);

                    requestAnimationFrame(() => {
                      proceedToMenuItemDetailsForUpdating(item);
                    });

                    // props.navigation.navigate("MenuItemDetailsUpdate", {
                    //   refresh: refresh.bind(this),
                    //   menuItem: item.menuItem,
                    //   cartItem: item,
                    //   outletData: outletData,
                    // });
                  }}
                  style={
                    {
                      // width: '10%',
                      // marginLeft: 30,
                      // backgroundColor: 'green',
                    }
                  }>
                  {switchMerchant ? (
                    <Icons name="edit" size={18} color={Colors.primaryColor} />
                  ) : (
                    <Icons name="edit" size={24} color={Colors.primaryColor} />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    requestAnimationFrame(() => {
                      removeFromCartItems(item);
                    });
                  }}
                  style={{
                    // width: '10%',
                    // marginLeft: 30,
                    // backgroundColor: 'green',
                    marginTop: 20,
                    right: 1,
                  }}>
                  {switchMerchant ? (
                    <FontAwesome name="trash-o" size={20} color={Colors.tabRed} />
                  ) : (
                    <FontAwesome name="trash-o" size={25} color={Colors.tabRed} />
                  )}
                </TouchableOpacity>
              </>
            )
              :
              (
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 16,
                    color: Colors.descriptionColor,
                    textAlign: 'center',
                  }}>
                  {'Bundle\nItem'}
                </Text>
              )
          }
        </View>
      </View>

      <View
        style={{
          height: 1.5,
          left: '-2%',
          width: '104%',
          backgroundColor: '#C2C1C0',
          opacity: 0.2,
          marginBottom: 4,

          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,
          elevation: 3,
        }} />
    </>;
  };

  const changeClick = () => {
    //delivery
    if (clicked == 1) {
      //setState({ clicked: 0 })
    } else {
      // setState({ clicked: 1, clicked1: 0, shouldShow: true })
      setClicked(1);
      setClicked1(0);
      // setShouldShow(true);

      Cart.setOrderType(1);

      CommonStore.update((s) => {
        s.orderType = ORDER_TYPE.DELIVERY;
      });
    }
  };

  const changeClick1 = () => {
    //pickup
    if (clicked1 == 1) {
      //setState({ clicked1: 0 })
    } else {
      // setState({ clicked1: 1, clicked: 0, shouldShow: false })
      setClicked(0);
      setClicked1(1);
      // setShouldShow(false);

      Cart.setOrderType(2);

      CommonStore.update((s) => {
        s.orderType = ORDER_TYPE.PICKUP;
      });
    }
  };

  const deleteUserCart = async () => {
    const body = {
      userCartId: userCart.uniqueId,
    };

    ApiClient.POST(API.deleteUserCart, body).then((result) => {
      if (result && result.status === 'success') {
        // console.log('ok');
      }
    });
  };

  // function end

  return (cartItemsProcessed.length === 0 || isCartLoading) ? (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>

    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}

        <View style={{ flex: 1 }}>
          <TouchableOpacity
            style={{ marginVertical: 15, flexDirection: 'row' }}
            onPress={() => {
              requestAnimationFrame(() => {
                props.navigation.goBack();
              });
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                alignContent: 'center',
              }}>
              <View>
                <Icons
                  name="chevron-left"
                  size={switchMerchant ? 20 : 30}
                  color={Colors.primaryColor}
                />
              </View>
              <Text
                style={{
                  color: Colors.primaryColor,
                  fontSize: switchMerchant ? 14 : 17,
                  textAlign: 'center',
                  fontFamily: 'NunitoSans-Bold',
                  borderWidth: 1,
                  borderColor: Colors.whiteColor,
                }}>
                Back
              </Text>
            </View>
          </TouchableOpacity>

          <View
            style={{
              // backgroundColor: 'red',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',

              height: '100%',
            }}>
            {/* {isLoading ? 
          <View>
            <ActivityIndicator color={Colors.primaryColor} size={"large"} />
          </View>
         :
          <Text
            style={{
              color: Colors.descriptionColor,
              textAlign: "center",
              // marginTop: 30,
              fontSize: 25,
              fontFamily: 'NunitoSans-Regular',
              top: '-10%',
            }}
          >
            No items in your cart now.
          </Text>
          } */}
            <View
              style={{
                marginBottom: Platform.OS === 'ios' ? '15%' : '15%',
              }}>
              <ActivityIndicator color={Colors.primaryColor} size={'large'} />
            </View>
          </View>
        </View>
      </View>
    </UserIdleWrapper>)
  ) : (
    // <View style={styles.container}>
    //   <ActionSheet
    //     ref={(o) => (ActionSheet = o)}
    //     title={'Select Your Payment Method'}
    //     options={['Online payment', 'Credit/Debit Card', 'cancel', 'Cash on delivery']}
    //     cancelButtonIndex={2}
    //     destructiveButtonIndex={2}
    //     onPress={(index) => {
    //       // console.log(index);
    //       if (index != 2) {
    //         setState({
    //           paymentMethod:
    //             index == 0 ? 'Online payment' : 'Cash on delivery',
    //         });
    //       }
    //     }}
    //   />
    //   <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}
        <ScrollView
          style={[
            { flex: 1 },
            switchMerchant
              ? { marginBottom: windowHeight * 0.01 }
              : {},
          ]}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => {
                // getCartItem();
                // getPopular();
              }}
            />
          }
          contentContainerStyle={{
            paddingBottom: switchMerchant ? (windowHeight * 0.05) : (windowHeight * 0.1),
          }}>
          {/* <View style={{ flexDirection: "row", marginBottom: 10, marginTop: 5 }}>
          <TouchableOpacity style={{
            width: "30%",
            height: 30,
            backgroundColor: clicked === 1 ? Colors.primaryColor : Colors.whiteColor,
            borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor
          }}
            onPress={() => { changeClick() }}
          >
            <Text style={{ color: clicked === 1 ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-Regular" }}>Delivery</Text>
          </TouchableOpacity>
          <TouchableOpacity style={{
            width: "30%",
            height: 30,
            backgroundColor: clicked1 === 1 ? Colors.primaryColor : Colors.whiteColor,
            borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor, marginLeft: 8
          }}
            onPress={() => { changeClick1() }}
          >
            <Text style={{ color: clicked1 === 1 ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-Regular" }}>Pick-up</Text>
          </TouchableOpacity>
        </View> */}

          {/* <View style={{
          flexDirection: "row",
          marginBottom: 15,
          marginTop: 5,
          paddingHorizontal: 24,
        }}>
          <TouchableOpacity style={{
            // width: "30%",
            // height: 30,
            width: 86,
            backgroundColor: clicked === 1 ? Colors.primaryColor : Colors.whiteColor,
            borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1,
            borderColor: clicked === 1 ? Colors.descriptionColor : "#adadad",
            paddingVertical: 6,
          }}
            onPress={() => { changeClick() }}
          >
            <Text style={{ color: clicked === 1 ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-SemiBold", fontSize: 13 }}>Delivery</Text>
          </TouchableOpacity>
          <TouchableOpacity style={{
            // width: "30%",
            // height: 30,
            width: 86,
            backgroundColor: clicked1 === 1 ? Colors.primaryColor : Colors.whiteColor,
            borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1,
            borderColor: clicked1 === 1 ? Colors.descriptionColor : "#adadad",
            marginLeft: 8,
            paddingVertical: 6,
          }}
            onPress={() => { changeClick1() }}
          >
            <Text style={{ color: clicked1 === 1 ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-SemiBold", fontSize: 13 }}>Pick-up</Text>
          </TouchableOpacity>
        </View> */}

          <View
            style={[
              {
                width: '100%',
                // height: 60,
                paddingVertical: 13,
                backgroundColor: '#ddebe5',
                justifyContent: 'center',
                paddingHorizontal: 25,
                marginTop: 0,

                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
              },
              switchMerchant ? {
                paddingVertical: 6,
              } : {},
            ]}>
            <View
              style={{
                position: 'absolute',
                top: switchMerchant ? windowHeight * -0.085 : windowHeight * 0.015,
              }}>
              <TouchableOpacity
                style={[
                  { flexDirection: 'row' },
                  switchMerchant
                    ? { marginTop: windowHeight * 0.119 }
                    : {},
                ]}
                onPress={() => {
                  requestAnimationFrame(() => {
                    props.navigation.goBack();
                  });
                }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View>
                    {switchMerchant ? (
                      <Icons
                        name="chevron-left"
                        size={20}
                        color={Colors.primaryColor}
                      />
                    ) : (
                      <Icons
                        name="chevron-left"
                        size={30}
                        color={Colors.primaryColor}
                      />
                    )}
                  </View>
                  <Text
                    style={{
                      color: Colors.primaryColor,
                      fontSize: switchMerchant ? 14 : 17,
                      //textAlign: 'center',
                      fontFamily: 'NunitoSans-Bold',
                      //marginLeft: -5,
                      marginTop: -1.5,
                    }}>
                    Back
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            <Text
              style={{
                color: Colors.primaryColor,
                //marginLeft: 4,
                fontSize: switchMerchant ? 15 : 19,
                fontFamily: 'NunitoSans-SemiBold',
                alignSelf: 'center',
              }}>
              Order Summary
            </Text>
          </View>


          <View
            style={{
              flexDirection: 'row',
              marginTop: 15,
            }}>
            <FlatList
              style={{ marginBottom: 10 }}
              data={cartItemsProcessed}
              extraData={cartItemsProcessed}
              renderItem={renderItem}
              keyExtractor={(item, index) => String(index)}
              contentContainerStyle={{
                paddingHorizontal: 30,
                paddingBottom: 5,
              }}
            />
          </View>
          {/* <ModalView
          style={{ flex: 1 }}
          visible={visible}
          transparent={true}
          animationType="slide">
          <View
            style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View style={styles.confirmBox}>
              <TouchableOpacity
                onPress={() => {
                  setState({ visible: false });
                  setState({ visible1: false });
                }}>
                <View
                  style={{
                    alignSelf: 'flex-end',
                    padding: 16,
                  }}>
                  <Close name="closecircle" size={28} />
                </View>
              </TouchableOpacity>
              <View>
                <Text
                  style={{
                    textAlign: 'center',
                    fontWeight: 'bold',
                    fontSize: 16,
                    marginBottom: 5,
                  }}>
                  Do you want to delete all?
              </Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  alignContent: 'center',
                  marginBottom: '6%',
                  height: '50%',
                }}>
                <View
                  style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      Cart.deleteCartItem(editingItemId);
                      getCartItem();
                      setState({ visible1: false });
                      setState({ visible: false });
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '30%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                      height: '75%',
                    }}>
                    <Text style={{ fontSize: 15, color: Colors.primaryColor }}>
                      Yes
                  </Text>
                  </TouchableOpacity>
                  <View style={{ marginLeft: '3%', marginRight: '3%' }}></View>
                  <TouchableOpacity
                    onPress={() => {
                      setState({ visible1: true });
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '30%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      borderRadius: 5,
                      height: '75%',
                    }}>
                    <Text style={{ fontSize: 15, color: Colors.primaryColor }}>
                      No
                  </Text>
                  </TouchableOpacity>
                  
                </View>
              </View>
            </View>
          </View>
        </ModalView> */}
          <DateTimePickerModal
            isVisible={takeawayDateModal}
            mode={'datetime'}
            onConfirm={(text) => {
              setScheduleDateTime(moment(text).valueOf());

              setTakeawayDateModal(false);
              // setTakeawayTimeModal(false);
            }}
            onCancel={() => {
              setTakeawayDateModal(false);
            }}
            minimumDate={Date.now()}
          />
          <DateTimePickerModal
            isVisible={takeawayTimeModal}
            mode={'time'}
            onConfirm={(text) => {
              setTakeawayLaterTime(moment(text).valueOf());

              setTakeawayDateModal(false);
              setTakeawayTimeModal(false);
            }}
            onCancel={() => {
              setTakeawayTimeModal(false);
            }}
          />
          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            style={{ flex: 1 }}
            visible={showSuccess}
            transparent>
            <View style={{ width: windowWidth * Styles.sideBarWidth, }} />
            <View style={{
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Image
                style={{
                  //marginLeft: windowWidth  * 0.16, 
                  marginTop: windowHeight * 0.08,
                  width: windowWidth,
                  height: windowHeight * 0.84
                }}
                source={require('../assets/image/Success.gif')}
                fadeDuration={400}
              //resizeMode='contain'
              // onLoad={() => {
              //   placeUserOrder();
              // }}
              />
            </View>
          </ModalView>
          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            style={{ flex: 1 }}
            visible={visible}
            transparent
            animationType="slide">
            <View
              style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <View style={styles.confirmBox}>
                <TouchableOpacity
                  onPress={() => {
                    setState({ visible1: false });
                    setState({ visible: false });
                  }}>
                  <View
                    style={{
                      alignSelf: 'flex-end',
                      padding: 13,
                    }}>
                    {/* <Close name="closecircle" size={25} /> */}
                    <AntDesign
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  </View>
                </TouchableOpacity>
                <View>
                  <Text
                    style={{
                      textAlign: 'center',
                      fontWeight: 'bold',
                      fontSize: switchMerchant ? 10 : 16,
                      marginBottom: 10,
                    }}>
                    Delete
                  </Text>
                </View>
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%',
                    alignContent: 'center',
                    marginBottom: '10%',
                    height: '50%',
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      marginBottom: '2%',
                    }}>
                    {/* <NumericInput
                      value={value}
                      onChange={(value) => setState({ value })}
                      minValue={1}
                      maxValue={qty}
                      totalWidth={200}
                      totalHeight={40}
                      iconSize={25}
                      step={1}
                      valueType="real"
                      rounded
                      textColor={Colors.primaryColor}
                      iconStyle={{ color: 'white' }}
                      rightButtonBackgroundColor={Colors.primaryColor}
                      leftButtonBackgroundColor={'grey'}
                    /> */}
                  </View>
                  <View
                    style={{
                      alignItems: 'center',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        onChangeQty(value, editingItemId);
                        getCartItem();
                        setState({ visible1: false });
                        setState({ visible: false });
                        setState({ value: '' });
                      }}
                      style={{
                        backgroundColor: Colors.primaryColor,
                        width: '30%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        borderRadius: 5,
                        height: '75%',
                        marginTop: 10,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 15,
                          color: Colors.whiteColor,
                        }}>
                        Update
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </ModalView>

          {/* <Text style={{
          fontWeight: "bold",
          fontSize: 20,
          marginTop: 10,
          marginBottom: 10,

          paddingHorizontal: 24
        }}>Other popular items</Text>

        <FlatList
          horizontal={true}
          data={popular}
          extraData={popular}
          renderItem={renderPopularItem}
          keyExtractor={(item, index) => String(index)}
          contentContainerStyle={{
            paddingHorizontal: 24,
          }}
        /> */}

          {/* <View style={{ height: 20 }}></View> */}

          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              //marginLeft: 40,
              marginTop: 0,
              //backgroundColor: 'blue',
              paddingHorizontal: 30,
            }}>
            <View style={{ width: '60%', marginRight: 15 }}>
              <Text
                style={[
                  styles.description,
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Subtotal
              </Text>
              <Text
                style={[
                  styles.description,
                  switchMerchant ? { fontSize: 10, height: 35, textAlignVertical: 'center' } : {},
                ]}>
                Promotion Code
              </Text>
              <Text
                style={[
                  styles.description,
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Discount
              </Text>
              {
                checkToApplyTaxOrNot(currOutlet, orderType, orderTypeSub)
                  ?
                  <Text
                    style={[
                      styles.description,
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    {`Tax (${(currOutlet.taxRate * 100).toFixed(0)}%)`}
                  </Text>
                  :
                  <></>
              }

              {
                (checkToApplyScOrNot(currOutlet, orderType, orderTypeSub))
                  ?
                  <Text
                    style={[
                      styles.description,
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    {`${currOutlet.scName ? currOutlet.scName : 'Service Charge'} (${(currOutlet.scRate * 100).toFixed(0)}%)`}
                  </Text>
                  :
                  <></>
              }

              {
                (currOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY)
                  ?
                  <Text
                    style={[
                      styles.description,
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    {`Delivery Packaging Fee`}
                  </Text>
                  :
                  <></>
              }

              {
                (currOutlet.pickupPackagingFee && orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL)
                  ?
                  <View>
                    <Text
                      style={[
                        styles.description,
                        switchMerchant ? { fontSize: 10 } : {},
                      ]}>
                      {`Takeaway Packaging Fee`}
                    </Text>
                  </View>
                  :
                  <></>
              }
              {(orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL) ?
                <Text
                  style={[
                    styles.description,
                    switchMerchant ? { fontSize: 10 } : {},
                  ]}>
                  {`Takeaway Time`}
                </Text> : <></>}

              {type == 1 ? (
                <View>
                  <Text
                    style={[
                      styles.description,
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    Delivery Fees
                  </Text>
                  <Text
                    style={[
                      styles.smallDescription,
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    (This fees is quoted by 3rd party logistic and it might be
                    different as it depends on the exact distance)
                  </Text>
                </View>
              ) : null}
              <Text
                style={[
                  styles.description,
                  switchMerchant ? { fontSize: 10 } : {},
                ]} />
            </View>
            <View style={{ width: windowWidth * 0.11 }} />

            <View style={{ width: '20%', alignItems: 'flex-end' }}>
              <View
                style={{
                  flexDirection: 'row',
                  width: '70%',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                  RM
                </Text>
                <Text
                  style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                  {totalPrice
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: '70%',
                  justifyContent: 'space-between',

                  zIndex: 500,
                }}>
                {
                  promoCodePromotionDropdownList.find(item => item.value === selectedPromoCodePromotionId)
                    ?
                    <DropDownPicker
                      globalTextStyle={{
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      containerStyle={{
                        height: switchMerchant ? 35 : 40,
                      }}
                      arrowColor={'black'}
                      arrowSize={switchMerchant ? 17 : 20}
                      arrowStyle={{ fontWeight: 'bold' }}
                      labelStyle={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      style={{
                        // width: 200,
                        width: windowWidth * 0.122,
                        paddingVertical: 0,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        //zIndex: index ++ ? 1 : 0,
                        //elevation: 1000
                      }}
                      placeholderStyle={{ color: Colors.fieldtTxtColor }}
                      placeholder={'Promotion'}
                      // items={[
                      //   {
                      //     label: 'Select Voucher',
                      //     value: 'SELECT_VOUCHER',
                      //   },
                      //   ...selectedVoucherUserList,
                      // ]}
                      items={promoCodePromotionDropdownList}
                      itemStyle={{
                        justifyContent: 'flex-start',
                        //marginLeft: 5,
                        paddingHorizontal:
                          windowWidth * 0.0079,
                        zIndex: 100,
                      }}
                      onChangeItem={(item) => {
                        // setSelectedVoucherUserItem(item.value);

                        setSelectedPromoCodePromotionId(item.value);

                        CommonStore.update(s => {
                          s.selectedPromoCodePromotion = availablePromoCodePromotions.find(promotion => promotion.uniqueId === item.value) || {};
                        });
                      }}
                      defaultValue={selectedPromoCodePromotionId}
                      dropDownMaxHeight={100}
                      dropDownStyle={{
                        // width: 200,
                        width: windowWidth * 0.122,
                        height: 150,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                        zIndex: 1000,
                      }}
                    />
                    :
                    <></>
                }
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: '70%',
                  justifyContent: 'space-between',

                  zIndex: -1000,
                }}>
                <Text
                  style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                  RM
                </Text>
                <Text
                  style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                  {(
                    totalDiscount +
                    // (usePointsToRedeem ? pointsToRedeemDiscount : 0) +
                    // (usePointsToRedeemLCC ? pointsToRedeemDiscountLCC : 0) +
                    discountPromotionsTotal
                  ).toFixed(2)}
                </Text>
              </View>
              {
                checkToApplyTaxOrNot(currOutlet, orderType, orderTypeSub)
                  ?
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '70%',
                      justifyContent: 'space-between',
                    }}>
                    <Text
                      style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                      RM
                    </Text>
                    <Text
                      style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                      {totalTax.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                  </View>
                  :
                  <></>
              }

              {
                (checkToApplyScOrNot(currOutlet, orderType, orderTypeSub))
                  ?
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '70%',
                      justifyContent: 'space-between',

                      zIndex: -1000,
                    }}>
                    <Text
                      style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                      RM
                    </Text>
                    <Text
                      style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                      {totalSc.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                  </View>
                  :
                  <></>
              }

              {
                (currOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY)
                  ?
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '70%',
                      justifyContent: 'space-between',

                      zIndex: -1000,
                    }}>
                    <Text
                      style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                      RM
                    </Text>
                    <Text
                      style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                      {currOutlet.deliveryPackagingFee.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                  </View>
                  :
                  <></>
              }

              {
                (currOutlet.pickupPackagingFee && orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL)
                  ?
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '70%',
                      justifyContent: 'space-between',

                      zIndex: -1000,
                    }}>
                    <Text
                      style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                      RM
                    </Text>
                    <Text
                      style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                      {currOutlet.pickupPackagingFee.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                  </View>
                  : <></>}
              {
                (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL)
                  ?
                  <View style={{
                    width: '70%',
                    justifyContent: 'space-between',

                    // zIndex: -1000,
                  }}>
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      zIndex: 1001,
                    }}>
                      <DropDownPicker
                        globalTextStyle={{
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        containerStyle={{
                          height: switchMerchant ? 35 : 40,
                        }}
                        arrowColor={'black'}
                        arrowSize={switchMerchant ? 17 : 20}
                        arrowStyle={{ fontWeight: 'bold' }}
                        labelStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        style={{
                          width: windowWidth * 0.122,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                        }}
                        placeholderStyle={{ color: Colors.fieldtTxtColor }}
                        placeholder={'Select'}
                        items={takeawayTimeOption}
                        itemStyle={{
                          justifyContent: 'flex-start',
                          paddingHorizontal:
                            windowWidth * 0.0079,
                          zIndex: 100,
                        }}
                        onChangeItem={(item) => {
                          setIsLater(item.value);
                        }}
                        defaultValue={isLater}
                        dropDownStyle={{
                          width: windowWidth * 0.122,
                          height: 70,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 1001,
                        }}
                      />
                    </View>
                    {isLater == 'LATER' ?
                      <View style={{
                        flexDirection: 'row',
                        width: '100%',
                        justifyContent: 'flex-end',
                        marginTop: 10,
                      }}>
                        <TouchableOpacity onPress={() => {
                          setTakeawayDateModal(true);
                        }}
                          style={[{
                            backgroundColor: Colors.fieldtBgColor,
                            // width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 115,
                            width: windowWidth * 0.122,
                            padding: 5,
                            // height: 40,
                            borderRadius: 5,
                            // marginVertical: 5,
                            marginTop: 5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                            //marginRight: 10,
                          }, switchMerchant ? {
                            fontSize: 10,
                            width: 93,
                            height: 35,
                          } : {}]}
                        >
                          <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 12 : 14 }, switchMerchant ? {
                            fontSize: 10,
                          } : {}]}>{`${scheduleDateTime ? `${moment(scheduleDateTime).format('DD MMM YYYY')}\n${moment(scheduleDateTime).format('hh:mm A')}` : 'Select DateTime'}`}</Text>
                        </TouchableOpacity>
                        {/* <TouchableOpacity onPress={() => {
                          setTakeawayTimeModal(true);
                        }}
                          style={[{
                            backgroundColor: Colors.fieldtBgColor,
                            // width: 115,
                            width: '45%',
                            height: 40,
                            borderRadius: 5,
                            padding: 1,
                            marginVertical: 5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            fontFamily: 'NunitoSans-Regular', fontSize: 14
                          }, switchMerchant ? {
                            height: 35
                          } : {}]}>
                          <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                            fontSize: 10,
                          } : {}]}>{moment(takeawayLaterTime).format('hh:mm A')}</Text>
                        </TouchableOpacity> */}
                      </View>
                      :
                      <></>
                    }
                  </View>
                  :
                  <></>
              }

              {type == 1 ? (
                <Text
                  style={[styles.price, switchMerchant ? { fontSize: 10 } : {}]}>
                  RM{' '}
                  {deliveryQuotation == null
                    ? '0.00'
                    : parseFloat(deliveryQuotation.totalFee)
                      .toFixed(2)
                      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text>
              ) : null}
              {/* <Text style={styles.price}></Text>
            <Text style={styles.price}></Text> */}
            </View>
          </View>

          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              //marginLeft: 40,

              paddingHorizontal: 30,
              //marginTop: -20,
              //marginBottom: 12,
            }}>
            <View style={{ width: '60%', marginRight: 15 }}>
              <Text style={[styles.total, switchMerchant ? { fontSize: 10, paddingVertical: 0, } : {}]}>
                TOTAL
              </Text>
            </View>
            <View style={{ width: windowWidth * 0.11 }} />
            <View style={{ width: '20%', alignItems: 'flex-end' }}>
              <View
                style={{
                  flexDirection: 'row',
                  width: '70%',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={[
                    styles.totalPrice,
                    {},
                    switchMerchant ? { fontSize: 10, paddingVertical: 0, } : {},
                  ]}>
                  RM
                </Text>
                <Text
                  style={[
                    styles.totalPrice,
                    {},
                    switchMerchant ? { fontSize: 10, paddingVertical: 0, } : {},
                  ]}>
                  {(Math.round(
                    (
                      totalPrice +
                      (checkToApplyTaxOrNot(currOutlet, orderType, orderTypeSub) ? totalTax : 0) +
                      (checkToApplyScOrNot(currOutlet, orderType, orderTypeSub) ? totalSc : 0) +
                      (currOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY ? currOutlet.deliveryPackagingFee : 0) +
                      (currOutlet.pickupPackagingFee && orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL ? currOutlet.pickupPackagingFee : 0)
                    )
                    * 20) / 20)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text>
              </View>
            </View>
          </View>

          {Cart.getVoucher() && (
            <View
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 20,
                justifyContent: 'space-around',
              }}>
              <View style={[styles.pic, {}]}>
                {Cart.getVoucher().outlet && Cart.getVoucher().outlet.cover && (
                  <Image
                    source={{ uri: Cart.getVoucher().outlet.cover }}
                    style={{
                      width: 90,
                      height: Platform.OS == 'ios' ? 90 : 90,
                      borderRadius: 10,
                    }}
                  />
                )}
                {!(
                  Cart.getVoucher().outlet && Cart.getVoucher().outlet.cover
                ) && (
                    <Image
                      source={require('../assets/image/extend.png')}
                      style={{
                        width: 90,
                        height: Platform.OS == 'ios' ? 90 : 90,
                        borderRadius: 10,
                      }}
                    />
                  )}
              </View>

              <Text
                style={[
                  styles.text,
                  {
                    width: '60%',
                  },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                {Cart.getVoucher().GiftCard.description}
              </Text>
            </View>
          )}

          {/* Use cash to checkout for now */}
          {/* <View style={{
          width: '80%',
          alignSelf: 'center',
          marginTop: 20,
        }}>
          <Text style={[styles.payment, {
            // paddingHorizontal: 24,
          }]}>Select your payment method</Text>

          <TouchableOpacity
            onPress={() => {
              setRouteFrom(1);
              props.navigation.navigate("PaymentMethod", { pageFrom: 'CartScreen' });
              // props.navigation.navigate("PaymentMethod", {
              //   screen: 'PaymentMethod',
              //   params: {
              //     pageFrom: 'CartScreen',
              //   }
              // });
            }}
            style={[styles.textInput, {
              // marginHorizontal: 24,
            }]}>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              // backgroundColor: 'red',
              height: '100%',
            }}>
              <Text style={{
                // paddingVertical: 14
                fontSize: 15,
                fontFamily: 'NunitoSans-Regular',
                color: Colors.fieldtTxtColor,
              }}>
                {'Credit/Debit Card'}
              </Text>
              <View style={{
                // marginLeft: '40%',
                justifyContent: 'center'
              }}>
                <Entypo
                  name="chevron-thin-down"
                  size={20}
                  color={Colors.primaryColor}
                />
              </View>
            </View>
          </TouchableOpacity>
        </View> */}

          {/* {Cart.getPayment() &&
          <View style={[styles.card, {
            backgroundColor: 'transparent',
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 10,
            justifyContent: 'flex-start',
          }]}>
            <View style={{
              marginRight: Platform.OS == 'ios' ? 120 : 25,
              left: -40,
            }}>
              <View style={[styles.pic, {
                // width: 0,
                // height: 0,
                backgroundColor: 'transparent',
                marginTop: 0,
                marginRight: 30,
              }]}>
                <Image
                  resizeMode={'contain'}
                  source={{ uri: Cart.getPayment().image }}
                  style={{ width: 200, height: 70, borderRadius: 10 }}
                />
              </View>
            </View>
            <View style={{
              // width: '60%',
            }}>
              <Text style={styles.title}>{Cart.getPayment().cardType}</Text>
              <Text style={styles.text}>{Cart.getPayment().cardNumber.replace(/./g, '*')}</Text>
            </View>
          </View>
        } */}

          {type == 1 ? (
            <View
              style={{
                width: '100%',
                // height: 60,
                paddingVertical: 16,
                backgroundColor: '#ddebe5',
                justifyContent: 'center',
                paddingHorizontal: 28,
                marginTop: 20,
              }}>
              <Text
                style={{
                  color: Colors.primaryColor,
                  marginLeft: 4,
                  fontSize: switchMerchant ? 10 : 17,
                  fontFamily: 'NunitoSans-SemiBold',
                }}>
                Deliver To
              </Text>
            </View>
          ) : null}

          {type == 1 ? (
            <View
              style={{
                // height: 100,
                justifyContent: 'center',
                // borderBottomWidth: StyleSheet.hairlineWidth,
                paddingHorizontal: 16,
                paddingVertical: 25,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  marginLeft: 10,
                  display: 'flex',
                  alignItems: 'center',
                }}>
                <View style={{ width: '15%' }}>
                  <Ionicons name="location-sharp" size={34} color={'red'} />
                </View>
                <View style={{ width: '60%' }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 14,
                      color: Colors.mainTxtColor,
                      fontFamily: 'NunitoSans-SemiBold',
                    }}>
                    {Cart.getDeliveryAddress() != null ||
                      Cart.getDeliveryAddress() != undefined
                      ? Cart.getDeliveryAddress().address
                      : 'Select an address'}
                  </Text>
                </View>
                <View style={{ width: '10%' }} />

                <TouchableOpacity
                  onPress={() => {
                    setRouteFrom(1);
                    props.navigation.navigate('Address', { testing: 1 });
                  }}
                  style={{
                    marginLeft: 15,
                  }}>
                  {switchMerchant ? (
                    <Icons name="edit" size={18} color={Colors.primaryColor} />
                  ) : (
                    <Icons name="edit" size={24} color={Colors.primaryColor} />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          ) : null}

          <View
            style={{
              height: 1.5,
              left: '5%',
              width: '90%',
              backgroundColor: '#C2C1C0',
              opacity: 0.2,
              marginBottom: 4,
              marginTop: 20,

              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 0.5,
              },
              shadowOpacity: 0.22,
              shadowRadius: 2.22,
              elevation: 3,
            }} />

          {type == 1 ? (
            <View
              style={{
                // height: 100,
                justifyContent: 'center',
                paddingHorizontal: 16,
                paddingVertical: 25,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  marginLeft: 16,
                  alignItems: 'center',
                }}>
                <TouchableOpacity
                  style={{
                    width: 22,
                    height: 22,
                    backgroundColor: Colors.whiteColor,
                    // marginTop: 10,
                    borderRadius: 20,
                    borderWidth: 1,
                    justifyContent: 'center',
                    borderColor: Colors.primaryColor,
                  }}
                  onPress={() => { }}>
                  <View
                    style={{
                      width: '70%',
                      height: '70%',
                      backgroundColor: Colors.primaryColor,
                      borderRadius: 20,
                      alignSelf: 'center',
                    }} />
                </TouchableOpacity>
                <View style={{ width: '5%' }} />
                <View style={{ width: '15%' }}>
                  {/* <MaterialIcons name="delivery-dining" size={40} color={Colors.primaryColor} /> */}
                  <Image
                    source={require('../assets/image/delivery.png')}
                    style={{
                      width: 34,
                      height: 34,
                      resizeMode: 'contain',
                    }} />
                </View>
                <View style={{ width: '55%' }}>
                  <View>
                    <Text
                      style={{
                        color: Colors.primaryColor,
                        // fontWeight: '700',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Delivery
                    </Text>
                    <Text
                      style={{
                        color: '#9c9c9c',
                        fontSize: switchMerchant ? 10 : 14,
                        colors: Colors.descriptionColor,
                      }}>
                      Deliver now (45mins)
                    </Text>
                  </View>
                </View>
                <View
                  style={{
                    width: '20%',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setState({ showDateTimePicker: true }),
                        generateScheduleTime();
                    }}>
                    <Text
                      style={{
                        color: Colors.primaryColor,
                        // fontWeight: "700",
                        // marginTop: 10
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                        textAlign: 'left',
                      }}>
                      {rev_date === undefined
                        ? 'Schedule'
                        : `${rev_date.split(',')[0]} ${rev_date.split(',')[1]}`}
                    </Text>
                  </TouchableOpacity>
                </View>
                {/* <DateTimePickerModal
              minimumDate={new Date()}
              selectMultiple={false}
              isVisible={showDateTimePicker}
              mode={pickerMode}
              display={pickerMode == "time" ? "spinner" : "default"} //for iOS to use minuteInterval
              maximumDate={new Date(Date.now() + (4320 * 60 * 1000))}
              onConfirm={(text) => {
                if (pickerMode == 'time') {
                  setState({ rev_time: new Date(text).getHours() + ":" + new Date(text).getMinutes() + ":00" })
                } else {
                  var date_ob = new Date(text);
                  let date = ("0" + date_ob.getDate()).slice(-2);
                  let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                  let year = date_ob.getFullYear();
                  setState({ rev_date: year + "-" + month + "-" + date, rev_time: new Date(text).getHours() + ":" + new Date(text).getMinutes() + ":00" })
                }
                setState({ showDateTimePicker: false })
              }}
              onCancel={() => {
                setState({ showDateTimePicker: false })
              }}
            /> */}

                <ModalView
                  supportedOrientations={['landscape', 'portrait']}
                  style={{ flex: 1 }}
                  visible={showDateTimePicker}
                  transparent
                  animationType="slide">
                  <View
                    style={{
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      flex: 1,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <View style={styles.scheduleBox}>
                      <TouchableOpacity
                        onPress={() => {
                          setState({ showDateTimePicker: false });
                        }}>
                        <View
                          style={{
                            alignSelf: 'flex-end',
                            padding: 16,
                          }}>
                          {/* <Close name="closecircle" size={25} /> */}
                          <AntDesign
                            name="closecircle"
                            size={25}
                            color={Colors.fieldtTxtColor}
                          />
                        </View>
                      </TouchableOpacity>
                      <View>
                        <Text
                          style={{
                            //textAlign: "center",
                            //fontWeight: "bold",
                            fontSize: switchMerchant ? 10 : 11,
                            marginBottom: 5,
                            marginLeft: '20%',
                            color: Colors.fieldtTxtColor,
                          }}>
                          Select delivery time
                        </Text>
                        <Text
                          style={{
                            //textAlign: "center",
                            fontWeight: 'bold',
                            fontSize: switchMerchant ? 10 : 16,
                            marginBottom: 5,
                            marginLeft: '20%',
                            color: Colors.primaryColor,
                          }}>
                          {schedulteTimeSelected.split(',')[0]}{' '}
                          {schedulteTimeSelected == 'TODAY,ASAP' ? ' ' : ', '}{' '}
                          {schedulteTimeSelected.split(',')[1]}
                        </Text>
                      </View>
                      <View
                        style={{
                          justifyContent: 'center',
                          alignItems: 'center',
                          width: '100%',
                          alignContent: 'center',
                          marginBottom: '6%',
                          height: '50%',
                        }}>
                        <View
                          style={{
                            alignItems: 'center',
                            marginBottom: '2%',
                            marginTop: '35%',
                          }}>
                          <SpinPicker
                            data={schedulteTimeList}
                            value={schedulteTimeList[0]}
                            onValueChange={(selectedItem) =>
                              setState({ schedulteTimeSelected: selectedItem })
                            }
                            keyExtractor={(number) => number.toString()}
                            //showArrows
                            //onInputValueChanged={console.log("hit")}
                            renderItem={renderSchedule}
                          />
                        </View>
                        <View
                          style={{
                            alignItems: 'center',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              // console.log(
                              //   'schedulteTimeSelected',
                              //   schedulteTimeSelected,
                              // );
                              setState(
                                {
                                  rev_date: schedulteTimeSelected,
                                  showDateTimePicker: false,
                                },
                                () => {
                                  // console.log(
                                  //   'rev_date: schedulteTimeSelected',
                                  //   rev_date,
                                  // );
                                  var splitDate = rev_date.split(',');
                                  var year = splitDate[2];
                                  var time = splitDate[1];
                                  var splitDay = splitDate[0].split(' ');
                                  var month = splitDay[2];
                                  var date = splitDay[1];
                                  const UTCRevDate = new Date(
                                    `${month
                                    } ${date
                                    } ${year
                                    } ${time
                                    } ${splitDate[3]}`,
                                  );
                                  // console.log('UTCRevDate', UTCRevDate);
                                  setState({ UTCRevDate });
                                },
                              );
                            }}
                            style={{
                              backgroundColor: Colors.primaryColor,
                              width: '80%',
                              justifyContent: 'center',
                              alignItems: 'center',
                              alignContent: 'center',
                              borderRadius: 10,
                              height: '70%',
                            }}>
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 24,
                                color: Colors.whiteColor,
                              }}>
                              SCHEDULE
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                </ModalView>
              </View>
            </View>
          ) : null}

          <TouchableOpacity
            disabled={isLoading}
            onPress={() => {
              // if (test1 == 1) {
              //   extend(Cart.getParkingId(), cartItem[0].quantity)
              // }
              // else {
              //   if (type == 1 && (Cart.getDeliveryAddress() == null || Cart.getDeliveryAddress() == undefined)) {
              //     Alert.alert(
              //       'Error',
              //       'Delivery address not selected.',
              //       [{ text: 'OK', onPress: () => { } }],
              //       { cancelable: false },
              //     );
              //   } else if (Cart.getOrderType() != 0) {
              //     var amount = (
              //       parseFloat(totalFloat) +
              //       parseFloat(taxFloat)
              //     ).toFixed(2)
              //     var amount = 5.00 //sandbox only allow maximum RM5
              //     // change to uuidv4
              //     var orderId = User.getUserId() + ':T' + Date.now().toString()
              //     // console.log("ORDER ID", orderId)
              //     //remember to change to this in production
              //     //molPayment(amount, 'orderId')
              //     molPayment(5, orderId)
              //     //placeOrder();
              //   }
              //   else {
              //     placeOrder();
              //   }
              // }

              // setShowSuccess(true);

              if (!global.isPlacingOrder) {
                global.isPlacingOrder = true;

                setTimeout(() => {
                  global.isPlacingOrder = false;
                }, 2000);

                requestAnimationFrame(() => {
                  placeUserOrder();
                });
              }
            }}>
            <View
              style={[
                {
                  backgroundColor: Colors.primaryColor,
                  padding: 20,
                  paddingVertical: 16,
                  borderRadius: 10,
                  alignItems: 'center',

                  marginHorizontal: 48,
                  // paddingTop: 32,
                  // marginBottom: 24,
                  width: '30%',
                  marginTop: 20,
                  alignSelf: 'center',
                },
                switchMerchant
                  ? {
                    width: windowWidth * 0.2,
                    height: windowHeight * 0.1,
                    bottom: windowHeight * 0.02,
                    padding: 0,
                  }
                  : {},
              ]}>
              <Text
                style={[
                  {
                    color: '#ffffff',
                    fontSize: switchMerchant ? 10 : 21,
                    // borderWidth: 1,
                    bottom: switchMerchant
                      ? windowHeight * 0.023
                      : 0,
                    fontFamily: 'NunitoSans-Regular',
                  },
                  switchMerchant
                    ? {
                      height: '500%',
                      top: -4,
                    }
                    : {},
                ]}>
                {isLoading ? 'LOADING...' : 'PLACE ORDER'}
              </Text>
            </View>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    // padding: 16,
    flexDirection: 'row',
  },
  description: {
    paddingVertical: 5,
    fontSize: 16,
    color: Colors.descriptionColor,
    fontFamily: 'NunitoSans-Regular',
    fontWeight: '400',
  },
  smallDescription: {
    paddingVertical: 5,
    fontSize: 14,
    color: '#9c9c9c',
    fontFamily: 'NunitoSans-Regular',
    fontWeight: '400',
  },
  payment: {
    color: Colors.descriptionColor,
    paddingVertical: 5,
    fontSize: 18,
    marginTop: 20,
  },
  total: {
    paddingVertical: 5,
    fontSize: 18,
    fontWeight: '700',
    marginTop: 5,
    fontFamily: 'NunitoSans-Regular',
  },
  price: {
    paddingVertical: 5,
    fontSize: 16,
    alignSelf: 'flex-end',
    fontFamily: 'NunitoSans-Regular',
    fontWeight: '400',
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 10,
    marginBottom: 10,
  },
  textInput1: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.secondaryColor,
    borderRadius: 5,
    marginTop: 20,
    justifyContent: 'center',
  },
  totalPrice: {
    color: Colors.primaryColor,
    paddingVertical: 5,
    fontSize: 18,
    fontWeight: '700',
    marginTop: 5,
  },
  confirmBox: {
    width: '60%',
    height: '30%',
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: '100%',
    width: '100%',
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
});
export default CartScreen;
