import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  Linking,
  Picker,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { TextInput, FlatList } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Styles from '../constant/Styles';
import Switch from 'react-native-switch-pro';
import {
  isTablet, listenToCurrOutletIdReservationChanges, parseValidIntegerText
} from '../util/common';
import { UserStore } from '../store/userStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { parseValidPriceText } from '../util/common';
import CheckBox from 'react-native-check-box';
import { color } from 'react-native-reanimated';
import Close from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import moment, { isDate } from 'moment';
import Barcode from 'react-native-barcode-builder';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { EXPAND_TAB_TYPE, LIVE_CHANNEL_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal from '../util/apiLocalReplacers';
import { qrUrl } from '../constant/env';
import Entypo from 'react-native-vector-icons/Entypo';
import firestore from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Clipboard from "@react-native-clipboard/clipboard";
import AsyncImage from "../components/asyncImage";

const PAGE_STATE = {
  CHANNEL_LIST: 'CHANNEL_LIST',
  STREAM_LIST: 'STREAM_LIST',
  LIVE_PRODUCTS_DETAILS: 'LIVE_PRODUCTS_DETAILS',
};

let videoIntervalTimer = null;

global.liveStreamTimerId = null;
global.goNextPageToken = '';

global.parsedMessageIdDict = {};

const LiveProductScreen = (props) => {
  const { navigation } = props;

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  // const [merchantId, setMerchantId] = useState([]);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const [isMounted, setIsMounted] = useState(true);
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  ////////////////////

  //Added 2024-04-17

  const [integrationId, setIntegrationId] = useState("");
  const [selectedProvince, setSelectedProvince] = useState("");
  const [lightning, setlightning] = useState(false);

  const DummyProductData = [
    { name: 'Product 1', price: '10', inventory: '100', code: 'ABC123', image: require('../assets/image/easyParcelLogo.png') },
    { name: 'Product 2', price: '20', inventory: '200', code: 'DEF456', image: require('../assets/image/easyParcelLogo.png') },
    { name: 'Product 3', price: '30', inventory: '300', code: 'GHI789', image: require('../assets/image/easyParcelLogo.png') },
  ];
  const dummyChannelData = [
    { name: 'Youtube', numOfStreams: '0' },
    // { name: 'facebook', numOfStreams: '0' },
  ];
  const dummyStreamData = [
    { name: 'channel A', duration: '30:00', viewers: 10, },
    { name: 'channel B', duration: '20:00', viewers: 3, },
    { name: 'channel C', duration: '1:30:00', viewers: 50, },
  ]

  ////////////////////////////////////////////////////////

  // 2024-04-25 - live product list changes

  const liveChannelList = [
    {
      name: 'YouTube Live',
      numOfStreams: 0,
      type: LIVE_CHANNEL_TYPE.YOUTUBE_LIVE,
    },
  ];

  const [selectedLiveChannel, setSelectedLiveChannel] = useState({});
  const [selectedStream, setSelectedStream] = useState({});

  const [currLiveProductList, setCurrLiveProductList] = useState([]);
  const [currStocks, setCurrStocks] = useState({});
  // const [currCodes, setCurrCodes] = useState([]);

  const [currStreamName, setCurrStreamName] = useState('');
  const [currStreamId, setCurrStreamId] = useState('');

  ////////////////////////////////////////////////////////

  const [streamList, setStreamList] = useState([]);

  const [selectedProduct, setSelectedProduct] = useState(DummyProductData[0]);
  const [data, setData] = useState(DummyProductData);

  const product = DummyProductData[0];

  // Accessing properties using dot notation
  const { name } = product;
  const { price } = product;
  const { inventory } = product;
  const { code } = product;
  const { image } = product;

  ////////////////////
  ///////////////////
  ///////////////////

  // 2024-04-18 - live product list changes

  const liveProductList = OutletStore.useState(s => s.liveProductList);
  const liveProductListArray = OutletStore.useState(s => s.liveProductListArray);

  const [currPageState, setCurrPageState] = useState(PAGE_STATE.CHANNEL_LIST);

  const [selectedGoVideoId, setSelectedGoVideoId] = useState('');

  const [streamName, setStreamName] = useState('');
  const [streamID, setStreamID] = useState('');

  useEffect(() => {
    let streamListTemp = [];

    if (selectedLiveChannel && selectedLiveChannel.type) {
      for (let i = 0; i < liveProductListArray.length; i++) {
        if (liveProductListArray[i].type === selectedLiveChannel.type) {
          streamListTemp.push(liveProductListArray[i]);
        }
      }

      if (selectedStream && selectedStream.uniqueId) {
        const currStream = streamListTemp.find(stream => stream.uniqueId === selectedStream.uniqueId);
        if (currStream) {
          setSelectedStream(currStream);
        }
      }
    }

    setStreamList(streamListTemp);
  }, [
    selectedLiveChannel,
    liveProductListArray,
  ]);

  useEffect(() => {
    if (selectedStream && selectedStream.items) {
      setCurrLiveProductList(selectedStream.items.map(item => {
        return {
          ...item,
          p: item.p.toFixed(2),
        };
      }));

      let stocksArr = Object.entries(selectedStream.stocks).map(([key, value]) => {
        return {
          key,
          value: value.toFixed(0),
        };
      });

      let stocksStr = stocksArr.reduce((accum, stock) => {
        return {
          ...accum,
          [stock.key]: stock.value,
        };
      }, {});

      setCurrStocks(stocksStr);

      setCurrStreamName(selectedStream.streamName ? selectedStream.streamName : '');
      setCurrStreamId(selectedStream.streamId ? selectedStream.streamId : '');
    }
  }, [
    selectedStream,
  ]);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);

      return () => {
        setIsMounted(false);


        // 2024-04-26 - end stream monitoring if exited/not active from this page

        setCurrPageState(PAGE_STATE.CHANNEL_LIST);

        endLiveStream();
      };
    }, [])
  );

  // useEffect(() => {
  //   if (videoIntervalTimer) {
  //     clearInterval(videoIntervalTimer);
  //   }

  //   if (currPageState === PAGE_STATE.STREAM_LIST) {
  //     let body = {
  //       accessToken: currOutlet.goAccessToken,
  //       refreshToken: currOutlet.goRefreshToken,
  //       expiryDate: currOutlet.goExpiryDate,

  //       outletId: currOutlet.uniqueId,
  //     };

  //     ApiClient.POST(API.goGetVideoList, body, false).then((result) => {
  //       console.log(result)
  //       console.log('result');

  //       setStreamList(result.data.map(video => ({
  //         name: video.title,
  //         duration: video.duration.toFixed(0),
  //         viewers: 0,
  //         videoId: video.videoId,
  //       })));
  //     }).catch(err => {
  //       console.log(err);
  //     });
  //   }
  //   else if (currPageState === PAGE_STATE.LIVE_PRODUCTS_DETAILS) {
  //     videoIntervalTimer = setInterval(async () => {
  //       console.log('poll to get comments');

  //       // if (selectedGoVideoId) {
  //       //   let body = {
  //       //     accessToken: currOutlet.goAccessToken,
  //       //     refreshToken: currOutlet.goRefreshToken,
  //       //     expiryDate: currOutlet.goExpiryDate,

  //       //     outletId: currOutlet.uniqueId,

  //       //     videoId: selectedGoVideoId,
  //       //   };

  //       //   ApiClient.POST(API.goGetVideoComments, body, false).then((result) => {
  //       //     console.log(result)
  //       //     console.log('result');

  //       //     for (let i = 0; i < result.data.length; i++) {
  //       //       let comment = result.data[i];

  //       //       console.log('comment');
  //       //       console.log(comment);

  //       //       let commentSplit = comment.comment.split(' ');

  //       //       if (liveProductList.codes.includes(commentSplit[0])) {
  //       //         // can proceed to net stage

  //       //         if (commentSplit[1][0] === '+') {
  //       //           let commentQuantity = commentSplit[1].split(1);

  //       //           let commentQuantityParsed = parseInt(commentQuantity);

  //       //           if (typeof commentQuantityParsed === 'number') {
  //       //             body = {
  //       //               accessToken: currOutlet.goAccessToken,
  //       //               refreshToken: currOutlet.goRefreshToken,
  //       //               expiryDate: currOutlet.goExpiryDate,

  //       //               outletId: currOutlet.uniqueId,
  //       //               merchantName,
  //       //               outletName: currOutlet.name,

  //       //               goUserEmail: comment.authorEmail,
  //       //               goCommentDt: moment(comment.publishedAt).valueOf(),

  //       //               codes: commentSplit[0],
  //       //               qty: commentQuantityParsed,

  //       //               // videoId: selectedGoVideoId,
  //       //             };

  //       //             ApiClient.POST(API.goCreatePreloadCartItems, body, false).then((result) => {
  //       //               console.log(result)
  //       //               console.log('result');
  //       //             }).catch(err => {
  //       //               console.log(err);
  //       //             });
  //       //           }
  //       //         }
  //       //       }
  //       //     }
  //       //   }).catch(err => {
  //       //     console.log(err);
  //       //   });
  //       // }


  //     }, 30000);

  //     const body = {
  //       codes: ['speci'],
  //       qty: 1,

  //       userPhone: '60125810210',
  //       userName: 'Herks',

  //       outletId: currOutlet.uniqueId,
  //       merchantId: currOutlet.merchantId,
  //     };

  //     ApiClient.POST(API.createPreloadCartItems, body)
  //       .then((result) => {
  //         if (result && result.status === 'success') {
  //           Alert.alert(
  //             'Success',
  //             `Live payment items link created successfully.\n\n${result.data}`,
  //           );

  //           console.log(result.data);
  //         } else {
  //           Alert.alert('Error', `Please try again later.\n\n${result.message}`);
  //         }

  //         CommonStore.update((s) => {
  //           s.isLoading = false;
  //         });
  //       })
  //       .catch((err) => {
  //         console.error(err);

  //         Alert.alert('Error', 'Please try again later.');

  //         CommonStore.update((s) => {
  //           s.isLoading = false;
  //         });
  //       });
  //   }
  // }, [
  //   currPageState,
  //   selectedGoVideoId
  // ]);

  //////////////////////////////////////////////////////////////////////////////////////////

  const [variationItemsDropdownList, setVariationItemsDropdownList] = useState(
    [],
  );

  const [outletItems, setOutletItems] = useState([]);

  const allOutletsItemAddOnDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnDict,
  );

  const allOutletsItemAddOnChoiceDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnChoiceDict,
  );

  const outletItemsUnsorted = OutletStore.useState(s => s.outletItems);

  const outletCategories = OutletStore.useState(s => s.outletCategories);

  const outletCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict,
  );

  useEffect(() => {
    var outletItemsTemp = outletItemsUnsorted.filter(item => outletCategoriesDict[item.categoryId] ? true : false)

    outletItemsTemp.sort((a, b) => a.name.localeCompare(b.name));

    setOutletItems(outletItemsTemp);
  }, [outletItemsUnsorted, outletCategoriesDict]);

  useEffect(() => {

    setVariationItemsDropdownList(outletItems.map(item => ({ label: item.name, value: item.uniqueId, sku: item.sku, price: item.price, image: item.image })));

    // setVariationItemsProducts(outletItems.map(item => ({ label: item.name, value: item.uniqueId })));
    // setVariationItemsCategories(allOutletsCategoriesUnique.map(item => ({ label: item.name, value: item.uniqueId })));
  }, [
    outletItems,
  ]);

  //////////////////////////////////////////////////////////////////////////////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('EasyParcelScreen');

            CommonStore.update((s) => {
              s.currPage = 'EasyParcelScreen';
              s.currPageStack = [...currPageStack, 'EasyParcelScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
            });
          }
        }}
        style={{
          width: windowWidth * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            marginRight: Platform.OS === 'ios' ? "27%" : 0,
            bottom: switchMerchant ? '2%' : 0,
            width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
          },
          windowWidth >= 768 && switchMerchant
            ? { right: windowWidth * 0.1 }
            : {},
          windowWidth <= 768
            ? { right: 20 }
            : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Live Product
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  ///////////////////

  const changeProduct = (item, itemIdNew) => {
    const outletItem = outletItems.find(findItem => findItem.uniqueId === itemIdNew);

    const foundCategory = outletCategories.find(category => {
      if (category.uniqueId === outletItem.categoryId) {
        return true;
      }
    });

    let selectedChoicesIdDict = {};
    let selectedChoicesPrice = 0;

    let variantStr = '';
    let addonStr = '';
    const foundVariantAddonList = allOutletsItemAddOnDict[outletItem.uniqueId];

    if (foundVariantAddonList) {
      for (let j = 0; j < foundVariantAddonList.length; j++) {
        let selectedChoiceNum = 0;

        const variantAddon = foundVariantAddonList[j];

        let isVariant = false;
        if (variantAddon.minSelect !== undefined && variantAddon.maxSelect !== undefined) {
          isVariant = true;
        }

        if (isVariant) {
          const variantAddonChoiceList = allOutletsItemAddOnChoiceDict[variantAddon.uniqueId];

          if (variantAddonChoiceList && variantAddonChoiceList.length > 0) {
            for (let k = 0; k < variantAddonChoiceList.length; k++) {
              const variantAddonChoice = variantAddonChoiceList[k];

              if (selectedChoiceNum < variantAddon.minSelect) {
                selectedChoicesIdDict[variantAddonChoice.uniqueId] = true;
                selectedChoicesPrice += variantAddonChoice.price;

                selectedChoiceNum++;
              }
            }
          }
        }
      }
    }

    ///////////////////////

    let liveProductNew = {
      // id: uuidv4(),
      id: item.id,
      itemId: outletItem.uniqueId,
      cId: foundCategory.uniqueId,
      cName: foundCategory.name,
      sku: outletItem.sku,
      image: outletItem.image,
      name: outletItem.name,
      code: outletItem.name.toLowerCase().replaceAll(' ', '').slice(0, 5),
      p: outletItem.price - 1,
      pO: outletItem.price,

      pVA: outletItem.price - 1 + selectedChoicesPrice, // with variant + addon price
      pOVA: outletItem.price + selectedChoicesPrice, // with variant + addon price (for original item price)

      va: selectedChoicesPrice,

      choices: selectedChoicesIdDict,

      os: false, // on sales
    };

    setCurrLiveProductList(currLiveProductList.map(product => {
      if (product.id === item.id) {
        return {
          ...liveProductNew,
          p: liveProductNew.p.toFixed(2),
        };
      }
      else {
        return product;
      }
    }));
  };

  const addNewProduct = () => {
    // const newItem = DummyProductData[0];
    // setData([newItem, ...data]);

    const outletItem = outletItems[0];

    const foundCategory = outletCategories.find(category => {
      if (category.uniqueId === outletItem.categoryId) {
        return true;
      }
    });

    let selectedChoicesIdDict = {};
    let selectedChoicesPrice = 0;

    let variantStr = '';
    let addonStr = '';
    const foundVariantAddonList = allOutletsItemAddOnDict[outletItem.uniqueId];

    if (foundVariantAddonList) {
      for (let j = 0; j < foundVariantAddonList.length; j++) {
        let selectedChoiceNum = 0;

        const variantAddon = foundVariantAddonList[j];

        let isVariant = false;
        if (variantAddon.minSelect !== undefined && variantAddon.maxSelect !== undefined) {
          isVariant = true;
        }

        if (isVariant) {
          const variantAddonChoiceList = allOutletsItemAddOnChoiceDict[variantAddon.uniqueId];

          if (variantAddonChoiceList && variantAddonChoiceList.length > 0) {
            for (let k = 0; k < variantAddonChoiceList.length; k++) {
              const variantAddonChoice = variantAddonChoiceList[k];

              if (selectedChoiceNum < variantAddon.minSelect) {
                selectedChoicesIdDict[variantAddonChoice.uniqueId] = true;
                selectedChoicesPrice += variantAddonChoice.price;

                selectedChoiceNum++;
              }
            }
          }
        }
      }
    }

    ///////////////////////

    let liveProductNew = {
      id: uuidv4(),
      // id: item.id,
      itemId: outletItem.uniqueId,
      cId: foundCategory.uniqueId,
      cName: foundCategory.name,
      sku: outletItem.sku,
      image: outletItem.image,
      name: outletItem.name,
      code: outletItem.name.toLowerCase().replaceAll(' ', '').slice(0, 5),
      p: outletItem.price - 1,
      pO: outletItem.price,

      pVA: outletItem.price - 1 + selectedChoicesPrice, // with variant + addon price
      pOVA: outletItem.price + selectedChoicesPrice, // with variant + addon price (for original item price)

      va: selectedChoicesPrice,

      choices: selectedChoicesIdDict,

      os: false, // on sales
    };

    setCurrLiveProductList([
      ...currLiveProductList,
      {
        ...liveProductNew,
        p: liveProductNew.p.toFixed(2),
      },
    ]);

    setCurrStocks({
      ...currStocks,
      [liveProductNew.id]: '10',
    });
  };

  const renderItem = ({ item }) => {
    return (
      <>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 5, marginBottom: 5, }}>
          <View style={{ width: '8.5%' }}>
            {item.image && item.image !== '' ? (
              <AsyncImage
                source={{ uri: item.image }}
                style={{
                  width: switchMerchant ? 30 : 40,
                  height: switchMerchant ? 30 : 40,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  borderRadius: 5,
                }}
              />
            ) : (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: switchMerchant ? 30 : 40,
                  height: switchMerchant ? 30 : 40,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  borderRadius: 5,
                }}>
                <Ionicons
                  name="fast-food-outline"
                  size={switchMerchant ? 15 : 25}
                />
              </View>
            )}
          </View>
          <View style={{ width: '25%', /* backgroundColor: Colors.fieldtBgColor, */ marginRight: 10, borderRadius: 10, justifyContent: 'center' }}>
            {/* <Picker
              style={{ flex: 1 }} // Let the Picker take all available space
              selectedValue={selectedProduct}
              onValueChange={(itemValue, itemIndex) =>
                setSelectedProduct(DummyProductData[itemIndex])
              }
            >
              {DummyProductData.map((product, index) => (
                <Picker.Item key={index} label={product.name} value={product} />
              ))}
            </Picker> */}

            {/* <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Regular', paddingLeft: 5, }}>
              {item.name}
            </Text> */}

            {
              variationItemsDropdownList.length > 0
                &&
                (
                  variationItemsDropdownList.find(option => option.value === item.itemId)
                  // selectedVariationItems.filter(itemId => {
                  //   return variationItemsDropdownList.find(item2 => item2.value === itemId) ? true : false;
                  // }).length === selectedVariationItems.length
                  // ||
                  // selectedVariationItems.length === 0
                )
                ?
                <View
                  style={{
                    width: '95%',
                    height: switchMerchant ? 20 : 35,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 10,
                    justifyContent: 'center',
                    // paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                    //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                    // paddingTop: '-60%',
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    // marginRight: '1%',
                  }}>
                  <RNPickerSelect
                    placeholder={{}}
                    useNativeAndroidPickerStyle={false}
                    style={{
                      inputIOS: {
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                      },
                      inputAndroid: {
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        justifyContent: 'center',
                        textAlign: 'center',
                        height: 45,
                        color: 'black',
                      },
                      inputAndroidContainer: { width: '100%' },
                      //backgroundColor: 'red',
                      height: 35,

                      chevronContainer: {
                        display: 'none',
                      },
                      chevronDown: {
                        display: 'none',
                      },
                      chevronUp: {
                        display: 'none',
                      },
                    }}
                    items={variationItemsDropdownList}
                    value={item.itemId}
                    onValueChange={(value) => {
                      changeProduct(item, value);
                    }}
                  />
                </View>
                :
                <></>
            }
          </View>
          <View style={{ width: '16.5%', marginRight: 5, }}>
            <TextInput
              style={[styles.input, {
                flex: 1,
                backgroundColor: Colors.fieldtBgColor,
                marginRight: 10,
                borderRadius: 10,
                // paddingLeft: 5,
                textAlign: 'center',
              }]}
              underlineColorAndroid={Colors.fieldtBgColor}
              // value={selectedProduct.price}
              value={item.p}
              onChangeText={text => {
                setCurrLiveProductList(currLiveProductList.map(product => {
                  if (product.id === item.id) {
                    return {
                      ...product,
                      p: parseValidPriceText(text),
                    };
                  }
                  else {
                    return product;
                  }
                }));
              }}
            />
          </View>

          <View style={{ width: '16%' }}>
            <TextInput
              style={[styles.input, {
                flex: 1,
                backgroundColor: Colors.fieldtBgColor,
                marginRight: 10,
                borderRadius: 10,
                // paddingLeft: 5,
                textAlign: "center",
              }]}
              // value={selectedProduct.inventory}
              value={currStocks[item.id]}
              onChangeText={text => {
                setCurrStocks({
                  ...currStocks,
                  [item.id]: parseValidIntegerText(text),
                })
                // setCurrLiveProductList(currLiveProductList.map(product => {
                //   if (product.id === item.id) {
                //     return {
                //       ...product,
                //       p: parseValidIntegerText(text),
                //     };
                //   }
                //   else {
                //     return product;
                //   }
                // }));
              }}
              underlineColorAndroid={Colors.fieldtBgColor}
            />
          </View>

          <View style={{ width: '15%' }}>
            <TextInput
              style={[styles.input, {
                flex: 1,
                backgroundColor: Colors.fieldtBgColor,
                marginRight: 10,
                borderRadius: 10,
                // paddingLeft: 5,
                textAlign: 'center',
              }]}
              value={item.code}
              onChangeText={text => {
                setCurrLiveProductList(currLiveProductList.map(product => {
                  if (product.id === item.id) {
                    return {
                      ...product,
                      code: text,
                    };
                  }
                  else {
                    return product;
                  }
                }));
              }}
              underlineColorAndroid={Colors.fieldtBgColor}
            />
          </View>

          {/* {item.os ? (
            <TouchableOpacity
              onPress={() => {

              }}>
              <MaterialCommunityIcons
                name="lightning-bolt"
                size={switchMerchant ? 25 : 35}
              />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={{
                backgroundColor: 'red',
                borderRadius: 5,
                paddingHorizontal: 10,
                height: switchMerchant ? 35 : 40,
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onPress={() => {

              }}
            >
              <Text style={{
                color: Colors.whiteColor,
                fontSize: switchMerchant ? 10 : 16,
                fontFamily: 'NunitoSans-Bold',
                textAlign: 'center',
              }}>
                STOP
              </Text>
            </TouchableOpacity>
          )} */}
        </View>
      </>
    )
  };

  const renderChannel = ({ item }) => {
    console.log('render channel', item);

    const numOfStreams = liveProductListArray.filter(lpl => lpl.type === item.type).length;

    return (
      <View style={{ marginTop: 5, }}>
        <TouchableOpacity
          style={{
            // backgroundColor: Colors.whiteColor,
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 5,
            // borderBottomWidth: StyleSheet.hairlineWidth,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,

            alignItems: 'center',
          }}
          onPress={() => {
            setSelectedLiveChannel(item);

            setCurrPageState(PAGE_STATE.STREAM_LIST);
          }}
        >
          <View style={{ width: '30%', }}>
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                height: 70,
                width: 70,
                alignSelf: 'center',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 5,
              }}>
              <FontAwesome name='youtube' size={55} color={'black'} />
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              width: '30%',
              alignItems: 'center',
              justifyContent: 'flex-start',
              height: 40,
            }}>

            <Text
              numberOfLines={1}
              style={{
                fontSize: switchMerchant ? 10 : 14,
                color: Colors.blackColor,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {item.name ? item.name : 'N/A'}
            </Text>

          </View>
          <View
            style={{
              flexDirection: 'row',
              width: '30%',
              alignItems: 'center',
              justifyContent: 'flex-start',
              height: 40,
            }}>
            <Text
              numberOfLines={1}
              style={{
                fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {(numOfStreams).toFixed(0)}
            </Text>
          </View>
        </TouchableOpacity >
      </View >
    )
  };

  const renderStream = ({ item }) => {
    return (
      <TouchableOpacity style={{

      }}
        onPress={() => {
          // setSelectedGoVideoId(item.videoId);

          setSelectedStream(item);

          setCurrPageState(PAGE_STATE.LIVE_PRODUCTS_DETAILS);
        }}
      >
        <View
          style={{
            backgroundColor: Colors.whiteColor,
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 5,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
            alignItems: 'center',
          }}>

          <View
            style={{
              flexDirection: 'row',
              width: '30%',
              alignItems: 'center',
              justifyContent: 'flex-start',
              marginLeft: 10,
            }}>

            <Text
              numberOfLines={1}
              style={{
                fontSize: switchMerchant ? 10 : 14,
                color: Colors.blackColor,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {item.streamName ? item.streamName : 'N/A'}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              width: '30%',
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}>
            <Text
              numberOfLines={1}
              style={{
                fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {`RM ${(item.salesAmt ? item.salesAmt : 0).toFixed(2)}`}
            </Text>
          </View>

          <View
            style={{
              flexDirection: 'row',
              width: '30%',
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}>
            <Text
              numberOfLines={1}
              style={{
                fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {`${(item.salesQty ? item.salesQty : 0).toFixed(0)}`}
            </Text>
          </View>

          {/* <View
            style={{
              flexDirection: 'row',
              width: '30%',
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}>
            <Text
              numberOfLines={1}
              style={{
                fontSize: switchMerchant ? 10 : 14,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {(item.viewers ? item.viewers : 0).toFixed(0)}
            </Text>
          </View> */}
        </View>
      </TouchableOpacity>
    )
  };

  const startLiveStream = () => {
    if (selectedStream && selectedStream.uniqueId) {
      if (selectedLiveChannel.type === LIVE_CHANNEL_TYPE.YOUTUBE_LIVE) {
        if (
          currOutlet.goAccessToken &&
          currOutlet.goRefreshToken &&
          currOutlet.goExpiryDate
        ) {
          CommonStore.update((s) => {
            s.isLoading = true;
          });

          const body = {
            outletId: currOutlet.uniqueId,

            accessToken: currOutlet.goAccessToken,
            refreshToken: currOutlet.goRefreshToken,
            expiryDate: currOutlet.goExpiryDate,
          };

          ApiClient.POST(API.goGetLiveStream, body)
            .then(async (result) => {
              if (result && result.status === 'success') {
                if (result.data && result.data.liveChatId) {
                  // can see if got other stream sharing the same chat id or not

                  const findStream = liveProductListArray.find(stream =>
                    stream.streamId === result.data.liveChatId &&
                    stream.streamType === LIVE_CHANNEL_TYPE.YOUTUBE_LIVE &&
                    stream.uniqueId !== selectedStream.uniqueId
                  );

                  if (findStream) {
                    Alert.alert(
                      'Info',
                      `The current live stream session is binded to ${findStream.streamName ? findStream.streamName : 'N/A'} already.`,
                    );
                  }
                  else {
                    if (selectedStream.streamId === '') {
                      firestore().collection(Collections.LiveProductList).doc(selectedStream.uniqueId).update({
                        streamId: result.data.liveChatId,

                        updatedAt: Date.now(),
                      });
                    }

                    //////////////////////////////////////////////////////////////
                    //////////////////////////////////////////////////////////////

                    // 2024-04-26 - here can call the api to monitor the stream details

                    if (global.liveStreamTimerId) {
                      clearInterval(global.liveStreamTimerId);
                    }

                    global.liveStreamTimerId = setInterval(() => {
                      CommonStore.update((s) => {
                        s.isLoading = true;
                      });

                      const bodyLiveChatMessages = {
                        liveChatId: result.data.liveChatId,
                        nextPageToken: global.goNextPageToken,

                        outletId: currOutlet.uniqueId,

                        accessToken: currOutlet.goAccessToken,
                        refreshToken: currOutlet.goRefreshToken,
                        expiryDate: currOutlet.goExpiryDate,
                      };

                      ApiClient.POST(API.goGetLiveChatMessages, bodyLiveChatMessages)
                        .then(async (result) => {
                          if (result && result.status === 'success') {
                            if (result.data && result.data.messages) {
                              const { messages, nextPageToken } = result.data;

                              global.goNextPageToken = nextPageToken;

                              parseMessages(messages);
                            }
                          } else {
                            Alert.alert('Error', 'Please try again later.');
                          }

                          CommonStore.update((s) => {
                            s.isLoading = false;
                          });
                        })
                        .catch((err) => {
                          console.error(err);

                          Alert.alert('Error', 'Please try again later.');

                          CommonStore.update((s) => {
                            s.isLoading = false;
                          });
                        });
                    }, 5000);

                    //////////////////////////////////////////////////////////////
                    //////////////////////////////////////////////////////////////

                    Alert.alert(
                      'Success',
                      `The stream will be monitored now.`,
                    );
                  }
                }
                else {
                  Alert.alert(
                    'Info',
                    'No live stream found.',
                  );
                }
              } else {
                Alert.alert('Error', 'Please try again later.');
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            })
            .catch((err) => {
              console.error(err);

              Alert.alert('Error', 'Please try again later.');

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            });
        }
        else {
          Alert.alert('Please authenticate your YouTube account first, by go to BackOffice > Settings > Integration page for the configuration.');
        }
      }
    }
    else {
      Alert.alert('Info', 'Please save the current stream details first before proceed.');
    }
  };

  const endLiveStream = () => {
    if (global.liveStreamTimerId) {
      clearInterval(global.liveStreamTimerId);
    }
  };

  const parseMessages = async (messages) => {
    if (selectedLiveChannel.type === LIVE_CHANNEL_TYPE.YOUTUBE_LIVE) {
      let stocksContext = {
        ...selectedStream.stocks,
      };

      for (let i = 0; i < messages.length; i++) {
        const currMessage = messages[i];

        // can only proceed if haven't parsed before
        if (
          !global.parsedMessageIdDict[currMessage.id]
          // || true
        ) {
          ////////////////////////////////////////////////////

          // 2024-04-26 - check user message

          if (currMessage.text) {
            const comment = currMessage.text;

            console.log('comment');
            console.log(comment);

            let commentSplit = comment.split(' ');

            if (selectedStream.codes.includes(commentSplit[0])) {
              // can proceed to net stage

              if (commentSplit[1][0] === '+') {
                let commentQuantity = commentSplit[1].split(1);

                let commentQuantityParsed = parseInt(commentQuantity);

                if (typeof commentQuantityParsed === 'number') {
                  const findLiveProduct = selectedStream.items.find(findItem => findItem.code === commentSplit[0]);

                  if (findLiveProduct && stocksContext[findLiveProduct.id] >= commentQuantityParsed) {
                    // means stock enough

                    stocksContext[findLiveProduct.id] -= commentQuantityParsed;

                    // call api to create the preload item

                    const body = {
                      accessToken: currOutlet.goAccessToken,
                      refreshToken: currOutlet.goRefreshToken,
                      expiryDate: currOutlet.goExpiryDate,

                      outletId: currOutlet.uniqueId,
                      merchantName,
                      outletName: currOutlet.name,

                      // goUserEmail: result.data.userEmail,
                      goUserEmail: currMessage.userId,
                      goCommentDt: moment(currMessage.dt).valueOf(),

                      // codes: commentSplit[0],
                      codes: [
                        commentSplit[0], // put in array format
                      ],
                      qty: commentQuantityParsed,

                      channelType: selectedLiveChannel.type,

                      streamId: selectedStream.streamId,
                      messageId: currMessage.id,

                      lsPle: currOutlet.lsPle !== undefined ? currOutlet.lsPle : true,

                      // videoId: selectedGoVideoId,
                    };

                    ApiClient.POST(API.goCreatePreloadCartItems, body, false).then((result) => {
                      console.log(result)
                      console.log('result');

                      console.log('done create preload cart items, and sent email');

                      let contentStr = `${result.data}\n\nMessage: ${currMessage.text}\nDate/Time: ${moment(currMessage.dt).format('YYYY-MM-DD, HH:mm A')}`;

                      Clipboard.setString(contentStr);

                      if (result && result.status === 'success') {
                        Alert.alert('Info', contentStr);
                      }
                    }).catch(err => {
                      console.log(err);
                    });

                    // go to get the email for this user

                    // const bodyEmail = {
                    //   accessToken: currOutlet.goAccessToken,
                    //   refreshToken: currOutlet.goRefreshToken,
                    //   expiryDate: currOutlet.goExpiryDate,

                    //   outletId: currOutlet.uniqueId,

                    //   userId: currMessage.userId,
                    // };

                    // ApiClient.POST(API.goGetUserEmailByUserId, bodyEmail, false).then((result) => {
                    //   console.log(result)
                    //   console.log('result');

                    //   if (result && result.status === 'success') {
                    //     if (result.data && result.data.userEmail) {
                    //       console.log(result.data.userEmail);


                    //     }
                    //   }
                    // }).catch(err => {
                    //   console.log(err);
                    // });
                  }
                }
              }
            }
          }

          ////////////////////////////////////////////////////

          // to avoid parse the same message again

          global.parsedMessageIdDict[currMessage.id] = true;
        }
      }
    }
  };

  // function end

  return (

    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={10}
            expandSettings
          />
        </View> */}

        {
          currPageState === PAGE_STATE.CHANNEL_LIST
            ?
            <ScrollView
              showsVerticalScrollIndicator={false}
              // scrollEnabled={switchMerchant}
              style={{ backgroundColor: Colors.highlightColor }}
              contentContainerStyle={{
                paddingBottom: windowHeight * 0.1,
                backgroundColor: Colors.highlightColor,
                //alignItems: 'flex-end',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 10,
                  margin: 20,
                  width: switchMerchant
                    ? windowWidth * 0.8
                    : windowWidth * 0.87,
                  alignSelf: 'center',
                  alignItems: 'center',
                }}>
                <View style={{ justifyContent: 'flex-start' }}>
                  <Text
                    style={[
                      { fontFamily: 'NunitoSans-Bold', fontSize: 26 },
                      switchMerchant
                        ? {
                          fontSize: 20,
                        }
                        : {},
                    ]}>
                    Channel List
                  </Text>
                </View>
                <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  {/* <TouchableOpacity
                    style={[
                      {
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: switchMerchant ? 20 : 15,
                      },
                      switchMerchant
                        ? {
                          height: 35,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <AntDesign
                      name="pluscircle"
                      size={switchMerchant ? 10 : 20}
                      style={{ color: Colors.whiteColor }}
                    />
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {/* ADD CHANNEL*
                  CHANNEL
                </Text>
              </TouchableOpacity> */}
                  <View
                    style={[
                      {
                        width: 250,
                        height: 40,
                        backgroundColor: 'white',
                        borderRadius: 5,
                        flexDirection: 'row',
                        alignContent: 'center',
                        alignItems: 'center',
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                      },
                      switchMerchant
                        ? {
                          width: 180,
                          height: 35,
                        }
                        : {},
                    ]}>
                    <Icon
                      name="search"
                      size={switchMerchant ? 10 : 18}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />

                    <View style={{ flex: 4 }}>
                      <TextInput
                        underlineColorAndroid={Colors.whiteColor}
                        style={[
                          {
                            width: 220,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                            paddingLeft: 5,
                            height: 45,
                          },
                          switchMerchant
                            ? {
                              width: 150,
                              height: 35,
                              fontSize: 10,
                            }
                            : {},
                        ]}
                        clearButtonMode="while-editing"
                        placeholder=" Search"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => { }}
                      // value={search}
                      />
                    </View>
                  </View>
                </View>
              </View>

              <View style={{
                backgroundColor: Colors.whiteColor,
                width: switchMerchant
                  ? windowWidth * 0.8
                  : windowWidth * 0.87,
                // height: windowHeight * 0.78,
                height: windowHeight * 0.68,
                marginTop: 0,
                marginBottom: 10,
                marginHorizontal: 20,
                alignSelf: 'center',
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }}>
                <View
                  style={{}}>
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      flexDirection: 'row',
                      paddingVertical: 20,
                      paddingHorizontal: 5,
                      borderBottomWidth: StyleSheet.hairlineWidth,
                      borderTopLeftRadius: 8,
                      borderTopRightRadius: 8,

                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        color: Colors.blackColor,
                        width: '30%',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        textAlign: 'left',
                      }} />
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '30%',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}>

                      <Text
                        numberOfLines={1}
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          color: Colors.blackColor,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Channel
                      </Text>

                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '30%',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}>
                      <Text
                        numberOfLines={1}
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Total number of streams
                      </Text>
                    </View>
                  </View>

                  {/* Channel List*/}
                  <View
                    style={{
                      flexDirection: 'row',
                      flex: 0.25,
                      borderRightColor: 'lightgrey',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                    }}>
                    <View style={{ flexDirection: 'column' }}>
                      <Text
                        style={{
                          fontSize: 14,
                          color: Colors.fieldtTxtColor,
                          fontFamily: 'NunitoSans-Bold',
                        }} />
                    </View>
                  </View>
                </View>

                <FlatList
                  nestedScrollEnabled
                  showsVerticalScrollIndicator={false}
                  data={liveChannelList}
                  renderItem={renderChannel}
                  keyExtractor={(item, index) => String(index)}
                  style={{}}
                />
              </View>
            </ScrollView>
            :
            <></>
        }

        {
          currPageState === PAGE_STATE.STREAM_LIST
            ?
            <ScrollView
              showsVerticalScrollIndicator={false}
              // scrollEnabled={switchMerchant}
              style={{ backgroundColor: Colors.highlightColor }}
              contentContainerStyle={{
                paddingBottom: windowHeight * 0.1,
                backgroundColor: Colors.highlightColor,
                //alignItems: 'flex-end',
              }}>
              <TouchableOpacity
                style={{
                  height: windowHeight * 0.05,
                  flexDirection: 'row',
                  alignContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                  marginLeft: 25,
                }}
                onPress={() => {
                  setCurrPageState(PAGE_STATE.CHANNEL_LIST)
                }}>
                <Icon
                  name="chevron-left"
                  size={switchMerchant ? 20 : 30}
                  color={Colors.primaryColor}
                />
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 14 : 17,
                    color: Colors.primaryColor,
                    marginBottom: Platform.OS === 'ios' ? 0 : 1,
                  }}>
                  Back
                </Text>
              </TouchableOpacity>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 10,
                  margin: 20,
                  width: switchMerchant
                    ? windowWidth * 0.8
                    : windowWidth * 0.87,
                  alignSelf: 'center',
                  alignItems: 'center',
                }}>
                <View style={{ justifyContent: 'flex-start' }}>
                  <Text
                    style={[
                      { fontFamily: 'NunitoSans-Bold', fontSize: 26 },
                      switchMerchant
                        ? {
                          fontSize: 20,
                        }
                        : {},
                    ]}>
                    Stream List
                  </Text>
                </View>
                <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                  <TouchableOpacity
                    style={[
                      {
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: switchMerchant ? 20 : 15,
                      },
                      switchMerchant
                        ? {
                          height: 35,
                        }
                        : {},
                    ]}
                    onPress={() => {
                      setSelectedStream({
                        items: [],
                        stocks: {},
                      });

                      setCurrPageState(PAGE_STATE.LIVE_PRODUCTS_DETAILS);
                    }}>
                    <AntDesign
                      name="pluscircle"
                      size={switchMerchant ? 10 : 20}
                      style={{ color: Colors.whiteColor }}
                    />
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {/* ADD STREAM*/}
                      STREAM
                    </Text>
                  </TouchableOpacity>
                  <View
                    style={[
                      {
                        width: 250,
                        height: 40,
                        backgroundColor: 'white',
                        borderRadius: 5,
                        flexDirection: 'row',
                        alignContent: 'center',
                        alignItems: 'center',
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                      },
                      switchMerchant
                        ? {
                          width: 180,
                          height: 35,
                        }
                        : {},
                    ]}>
                    <Icon
                      name="search"
                      size={switchMerchant ? 10 : 18}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />

                    <View style={{ flex: 4 }}>
                      <TextInput
                        underlineColorAndroid={Colors.whiteColor}
                        style={[
                          {
                            width: 220,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                            paddingLeft: 5,
                            height: 45,
                          },
                          switchMerchant
                            ? {
                              width: 150,
                              height: 35,
                              fontSize: 10,
                            }
                            : {},
                        ]}
                        clearButtonMode="while-editing"
                        placeholder=" Search"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => {

                        }}
                      // value={search}
                      />
                    </View>
                  </View>
                </View>
              </View>

              <View style={{
                backgroundColor: Colors.whiteColor,
                width: switchMerchant
                  ? windowWidth * 0.8
                  : windowWidth * 0.87,
                // height: windowHeight * 0.78,
                height: windowHeight * 0.68,
                marginTop: 0,
                marginBottom: 10,
                marginHorizontal: 20,
                alignSelf: 'center',
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }}>

                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-end' }} />

                <View style={{}} />
                <View
                  style={{}}>
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      flexDirection: 'row',
                      paddingVertical: 20,
                      paddingHorizontal: 5,
                      borderBottomWidth: StyleSheet.hairlineWidth,
                      borderTopLeftRadius: 8,
                      borderTopRightRadius: 8,

                      alignItems: 'center',
                    }}>
                    {/* <Text
                      style={{
                        color: Colors.blackColor,
                        width: '20%',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        textAlign: 'left',
                      }} /> */}
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '30%',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        marginLeft: 10,
                      }}>

                      <Text
                        numberOfLines={1}
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          color: Colors.blackColor,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Name/Streams List
                      </Text>

                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '30%',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}>
                      <Text
                        numberOfLines={1}
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Sales (RM)
                      </Text>
                    </View>

                    <View
                      style={{
                        flexDirection: 'row',
                        width: '30%',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}>
                      <Text
                        numberOfLines={1}
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Order Qty
                      </Text>
                    </View>
                    {/* <View
                      style={{
                        flexDirection: 'row',
                        width: '30%',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}>
                      <Text
                        numberOfLines={1}
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Viewers
                      </Text>
                    </View> */}
                  </View>

                  {/* Channel List*/}
                  <View
                    style={{
                      flexDirection: 'row',
                      flex: 0.25,
                      borderRightColor: 'lightgrey',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                    }}>
                    <View style={{ flexDirection: 'column' }}>
                      <Text
                        style={{
                          fontSize: 14,
                          color: Colors.fieldtTxtColor,
                          fontFamily: 'NunitoSans-Bold',
                        }} />
                    </View>
                  </View>
                </View>

                <FlatList
                  nestedScrollEnabled
                  showsVerticalScrollIndicator={false}
                  data={streamList}
                  renderItem={renderStream}
                  keyExtractor={(item, index) => String(index)}
                  style={{}}
                />

              </View>
            </ScrollView>
            :
            <></>
        }

        {
          currPageState === PAGE_STATE.LIVE_PRODUCTS_DETAILS
            ?
            <View style={{ flexDirection: 'row', backgroundColor: Colors.highlightColor, }}>
              <View style={{ width: '35%', }}>
                <TouchableOpacity
                  style={{
                    height: windowHeight * 0.05,
                    flexDirection: 'row',
                    alignContent: 'center',
                    alignItems: 'center',
                    marginTop: 10,
                    marginLeft: 25,
                  }}
                  onPress={() => {
                    setCurrPageState(PAGE_STATE.STREAM_LIST)
                  }}>
                  <Icon
                    name="chevron-left"
                    size={switchMerchant ? 20 : 30}
                    color={Colors.primaryColor}
                  />
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: switchMerchant ? 14 : 17,
                      color: Colors.primaryColor,
                      marginBottom: Platform.OS === 'ios' ? 0 : 1,
                    }}>
                    Back
                  </Text>
                </TouchableOpacity>

                {/* <View style={{
                  padding: 20,
                  backgroundColor: Colors.secondaryColor,
                  borderRadius: 5,
                  width: '85%',
                  height: '35%',
                  alignSelf: 'center',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 20,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                }}>
                  <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', color: 'white' }}>
                    Live Stream
                  </Text>
                </View> */}

                <View style={{}}>
                  <View style={{
                    padding: 20,
                    marginHorizontal: 10,
                    width: '85%',
                    marginHorizontal: 10,
                    borderRadius: 5,
                    alignSelf: 'center',
                    justifyContent: 'center',
                    marginTop: 20,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    backgroundColor: Colors.primaryColor
                  }}>
                    <View style={{ flexDirection: 'row', }}>
                      <View style={{ flexDirection: 'column', width: '50%', alignItems: 'center' }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 20, color: 'white' }}>
                          {(selectedStream && selectedStream.salesAmt ? selectedStream.salesAmt : 0).toFixed(2)} MYR
                        </Text>
                        <Text style={{ fontFamily: 'NunitoSans-SemiBold', color: 'white' }}>
                          Sales
                        </Text>
                      </View>

                      <View style={{ flexDirection: 'column', width: '50%', alignItems: 'center' }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 20, color: 'white' }}>
                          {(selectedStream && selectedStream.salesQty ? selectedStream.salesQty : 0).toFixed(0)}
                        </Text>
                        <Text style={{ fontFamily: 'NunitoSans-SemiBold', color: 'white' }}>
                          Orders
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </View>

              <View style={{ width: '65%', }}>
                <View
                  // scrollEnabled={switchMerchant}
                  style={{ backgroundColor: Colors.highlightColor }}

                //alignItems: 'flex-end',
                >
                  <View style={{
                    padding: 20,
                    // width: '100%',
                    backgroundColor: 'white',
                    //width: windowWidth * 0.85,
                    borderRadius: 5,
                    width: '86%',
                    height: windowHeight * 0.84,
                    marginLeft: 0,
                    //alignSelf: 'center',
                    marginTop: 20,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    // elevation: 1,
                    elevation: 3,
                  }}>
                    <View style={{}}>
                      <View style={{ marginTop: 15, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-end', }}>
                        <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                          Live Product
                        </Text>
                        <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
                          <TouchableOpacity
                            style={{
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginRight: 10,
                            }}
                            onPress={() => {
                              startLiveStream();
                            }}>
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                              <Text style={{
                                color: Colors.whiteColor,
                                // marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                                START
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            style={{
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginRight: 10,
                            }}
                            onPress={() => {
                              addNewProduct();
                            }}>
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                              <Text style={{
                                color: Colors.whiteColor,
                                // marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                                ADD
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            style={{
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onPress={() => {
                              let itemsNew = currLiveProductList.map(product => {
                                let sellPrice = parseFloat(product.p);

                                return {
                                  ...product,
                                  p: sellPrice,
                                  pVA: sellPrice + product.va,
                                };
                              })

                              let stocksArr = Object.entries(currStocks).map(([key, value]) => {
                                return {
                                  key,
                                  value: parseInt(value),
                                };
                              });

                              let stocksNew = stocksArr.reduce((accum, stock) => {
                                return {
                                  ...accum,
                                  [stock.key]: stock.value,
                                };
                              }, {});

                              const codesNew = itemsNew.map(item => item.code);

                              if (new Set(codesNew).size !== codesNew.length) {
                                // means got duplication

                                Alert.alert('Info', `Please make sure the 'codes' section is unique for ech row.`);

                              }
                              else if (selectedStream && selectedStream.uniqueId) {
                                // update

                                CommonStore.update((s) => {
                                  s.isLoading = true;
                                });

                                const body = {
                                  items: itemsNew,
                                  stocks: stocksNew,
                                  codes: codesNew,

                                  streamName: currStreamName,
                                  streamId: currStreamId,
                                  streamType: selectedLiveChannel.type,

                                  merchantId: currOutlet.merchantId,
                                  outletId: currOutlet.uniqueId,
                                  merchantName,
                                  outletName: currOutlet.name,

                                  duration: '',

                                  subdomain: currOutlet.subdomain ? currOutlet.subdomain : '',

                                  uniqueId: selectedStream.uniqueId,
                                };

                                ApiClient.POST(API.updateLiveProductList, body)
                                  .then((result) => {
                                    if (result && result.status === 'success') {
                                      Alert.alert(
                                        'Success',
                                        'Stream details saved successfully.',
                                      );
                                    } else {
                                      Alert.alert('Error', 'Please try again later.');
                                    }

                                    CommonStore.update((s) => {
                                      s.isLoading = false;
                                    });
                                  })
                                  .catch((err) => {
                                    console.error(err);

                                    Alert.alert('Error', 'Please try again later.');

                                    CommonStore.update((s) => {
                                      s.isLoading = false;
                                    });
                                  });
                              }
                              else {
                                // create

                                CommonStore.update((s) => {
                                  s.isLoading = true;
                                });

                                const body = {
                                  items: itemsNew,
                                  stocks: stocksNew,
                                  codes: codesNew,

                                  streamName: currStreamName,
                                  streamId: currStreamId,
                                  streamType: selectedLiveChannel.type,

                                  merchantId: currOutlet.merchantId,
                                  outletId: currOutlet.uniqueId,
                                  merchantName,
                                  outletName: currOutlet.name,

                                  duration: '',

                                  subdomain: currOutlet.subdomain ? currOutlet.subdomain : '',
                                };

                                ApiClient.POST(API.createLiveProductList, body)
                                  .then((result) => {
                                    if (result && result.status === 'success') {
                                      Alert.alert(
                                        'Success',
                                        'Stream details saved successfully.',
                                      );
                                    } else {
                                      Alert.alert('Error', 'Please try again later.');
                                    }

                                    CommonStore.update((s) => {
                                      s.isLoading = false;
                                    });
                                  })
                                  .catch((err) => {
                                    console.error(err);

                                    Alert.alert('Error', 'Please try again later.');

                                    CommonStore.update((s) => {
                                      s.isLoading = false;
                                    });
                                  });
                              }
                            }}>
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                              <Text style={{
                                color: Colors.whiteColor,
                                // marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                                SAVE
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>

                      <View style={{ marginTop: 10, }}>
                        <View style={{ flexDirection: 'row', alignItems: "center" }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 14,
                            color: Colors.blackColor,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            Stream Name
                          </Text>
                          <TextInput
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              marginRight: 10,
                              borderRadius: 10,
                              paddingLeft: 10,
                              height: 45,
                              width: 250,
                              marginLeft: 10,
                            }}
                            underlineColorAndroid={Colors.fieldtBgColor}
                            value={currStreamName}
                            placeholder="Stream Name"
                            onChangeText={text => {
                              setCurrStreamName(text)
                            }}
                          />
                        </View>
                        <View style={{ flexDirection: "row", alignItems: 'center', marginTop: 10, }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 14,
                            color: Colors.blackColor,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            Stream ID
                          </Text>
                          <TextInput
                            editable={false}
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              marginRight: 10,
                              borderRadius: 10,
                              paddingLeft: 10,
                              height: 45,
                              width: 250,
                              marginLeft: 10,
                            }}
                            underlineColorAndroid={Colors.fieldtBgColor}
                            value={currStreamId}
                            placeholder="Stream ID"
                          // onChangeText={text => {
                          //   setCurrStreamId(text)
                          // }}
                          />
                        </View>
                      </View>

                      <View style={{ marginTop: 10 }} />
                      <View
                        style={{}}>
                        <View
                          style={{
                            backgroundColor: Colors.whiteColor,
                            flexDirection: 'row',
                            paddingVertical: 20,
                            paddingHorizontal: 5,
                            borderBottomWidth: StyleSheet.hairlineWidth,
                            borderTopLeftRadius: 8,
                            borderTopRightRadius: 8,

                            alignItems: 'center',
                          }}>
                          <Text
                            style={{
                              color: Colors.blackColor,
                              width: '8.5%',
                              alignSelf: 'center',
                              fontFamily: 'NunitoSans-Bold',
                              textAlign: 'left',
                            }} />
                          <View
                            style={{
                              flexDirection: 'row',
                              width: '25%',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                              marginRight: 10,
                            }}>
                            <TouchableOpacity
                              onPress={() => {

                              }}>
                              <View style={{ flexDirection: 'row' }}>
                                <View style={{ flexDirection: 'column' }}>
                                  <Text
                                    numberOfLines={1}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: Colors.blackColor,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Product
                                  </Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              width: '16.5%',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                              marginRight: 5,
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                              }}>
                              <View style={{ flexDirection: 'row' }}>
                                <View style={{ flexDirection: 'column' }}>
                                  <Text
                                    numberOfLines={1}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    {'Price (RM)'}
                                  </Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              width: '16%',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                              }}>
                              <View style={{ flexDirection: 'row' }}>
                                <View style={{ flexDirection: 'column' }}>
                                  <Text
                                    numberOfLines={1}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Inventory
                                  </Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              width: '15%',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                            }}>
                            <TouchableOpacity
                              onPress={() => {

                              }}>
                              <View style={{ flexDirection: 'row' }}>
                                <View style={{ flexDirection: 'column' }}>
                                  <Text
                                    numberOfLines={1}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 14,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Code
                                  </Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        </View>

                        {/* Product List*/}
                        <FlatList
                          style={{ height: windowHeight * 0.52 }}
                          data={currLiveProductList}
                          renderItem={renderItem}
                        />
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
            : <></>
        }
      </View >
    </UserIdleWrapper >
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInputTakeawayCharges: {
    backgroundColor: Colors.fieldtBgColor,
    width: 200,
    height: 40,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
    //marginLeft: 190,
  },

  textInput: {
    //fontFamily: 'NunitoSans-Regular',
    //width: 300,
    marginTop: 10,
    color: 'black',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
    paddingLeft: 10,
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 60,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: '#E5E5E5',
    borderWidth: 1,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: Platform.OS == 'ios' ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10,
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center',
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  viewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0,
    width: '100%',
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
    marginBottom: 15,
    width: '100%',
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 7,
    width: '83%',
    alignSelf: 'flex-end',
  },
  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
});
export default LiveProductScreen;
