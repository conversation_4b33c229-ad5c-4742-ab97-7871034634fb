import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback, useRef, } from 'react';
import {
  StyleSheet, Image, // View,
  Alert, TouchableOpacity, Dimensions, TextInput, Modal, Platform, ActivityIndicator, useWindowDimensions, Animated
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView as ScrollViewGH, ScrollView, FlatList } from 'react-native-gesture-handler';
import Feather from 'react-native-vector-icons/Feather';
import Colors from '../constant/Colors';
import Close from 'react-native-vector-icons/AntDesign';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ImagePicker from 'react-native-image-picker';
import API from '../constant/API';
import { isEnabled } from 'react-native/Libraries/Performance/Systrace';
import DropDownPicker from 'react-native-dropdown-picker';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import Switch from 'react-native-switch-pro';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Ionicon from 'react-native-vector-icons/Ionicons';
import {
  getTransformForScreenInsideNavigation,
  isTablet, performResize
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import AsyncImage from '../components/asyncImage';
import { CommonStore } from '../store/commonStore';
import AIcon from 'react-native-vector-icons/AntDesign';
import AntDesign from 'react-native-vector-icons/AntDesign';
import RNPickerSelect from 'react-native-picker-select';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { color } from 'react-native-reanimated';
import APILocal from '../util/apiLocalReplacers';
import MoMenuItemDetailsScreen from './MoMenuItemDetailsScreen';
import MoOutletMenuScreen from './MoOutletMenuScreen';
import MoCartScreen from './MoCartScreen';
import { EXPAND_TAB_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';

const View = require(
  'react-native/Libraries/Components/View/ViewNativeComponent'
).default;

const MenuOrderingScreen = React.memo((props) => {
  const { navigation, route } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      // setTimeout(() => {
      //     performResize(
      //         {
      //             windowPhysicalPixels: {
      //                 height: 2160,
      //                 width: 1620,
      //                 scale: 2,
      //             },
      //         },
      //         'iPad 9th Generation',
      //         false,
      //         false,
      //         true,
      //         global.windowWidthOriginal,
      //         global.windowHeightOriginal,
      //         global.fontScaleOriginal,
      //     );
      // }, 50);

      // if (global.simulateTabletMode) {
      //     performResize(
      //         {
      //             windowPhysicalPixels: {
      //                 // height: 2160,
      //                 // width: 1620,
      //                 // scale: 2,
      //                 height: global.windowWidthOriginal,
      //                 width: global.windowHeightOriginal,
      //                 scale: global.fontScaleOriginal
      //             },
      //         },
      //         'iPad 9th Generation',
      //         false,
      //         false,
      //         true,
      //         global.windowWidthOriginal,
      //         global.windowHeightOriginal,
      //         global.fontScaleOriginal,
      //     );
      // }

      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const simulateTabletMode = CommonStore.useState(s => s.simulateTabletMode);
  const windowWidthSimulate = CommonStore.useState(s => s.windowWidthSimulate);
  const windowHeightSimulate = CommonStore.useState(s => s.windowHeightSimulate);
  const fontScaleSimulate = CommonStore.useState(s => s.fontScaleSimulate);

  // const { width: windowWidthRaw, height: windowHeightRaw } = useWindowDimensions();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  // const [windowWidth, setWindowWidth] = useState(0);
  // const [windowHeight, setWindowHeight] = useState(0);

  // useEffect(() => {
  //     if (simulateTabletMode) {
  //         setWindowWidth(windowWidthSimulate);
  //         setWindowHeight(windowHeightSimulate);
  //     }
  //     else {
  //         setWindowWidth(windowWidthRaw);
  //         setWindowHeight(windowHeightRaw);
  //     }
  // }, [simulateTabletMode, windowWidthSimulate, windowWidthRaw]);

  const [routeParamsCart, setRouteParamsCart] = useState({
    test: {},
    outletData: {},
    navFrom: '',
  });
  const [routeParamsOutletMenu, setRouteParamsOutletMenu] = useState({
    navFrom: '',
    test: {},
    outletDat: {},
    orderType: '',
  });
  const [routeParamsMenuItemDetails, setRouteParamsMenuItemDetails] = useState({
    refresh: () => { },
    menuItem: {},
    outletData: {},
    orderType: '',
  });

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');

  ///////////////////////////////////////////////////////////////////

  const outletCategories = OutletStore.useState((s) => s.outletCategories);
  const outletItems = OutletStore.useState((s) => s.outletItems);

  //////////////////////////////////////////////////////////////

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const selectedProductEdit = CommonStore.useState((s) => s.selectedProductEdit,);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const outletSelectDropdownView = CommonStore.useState((s) => s.outletSelectDropdownView,);

  //////////////////////////////////////////////////////////////

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const isOnMenu = CommonStore.useState((s) => s.isOnMenu);
  const isOnCategory = CommonStore.useState((s) => s.isOnCategory == true);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [numColumns, setNumColumns] = useState(7);
  const [listKey, setListKey] = useState('default');

  const sidebarClose = () => {
    setNumColumns(7);
    setListKey(`columns-${7}`);
    setIsSidebarOpen(false);
    Animated.timing(sidebarXValue, {
      toValue: -200,
      duration: 200,
      useNativeDriver: true,
    }).start();

    Animated.timing(contentXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };
  const sidebarOpen = () => {

    setNumColumns(6);
    setListKey(`columns-${6}`);
    setIsSidebarOpen(true);
    Animated.timing(sidebarXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
    Animated.timing(contentXValue, {
      toValue: windowWidth * 0.08,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const sidebarXValue = useRef(new Animated.Value(-200)).current;
  const contentXValue = useRef(new Animated.Value(0)).current;
  console.log('sidebarXValue:', sidebarXValue);
  console.log('contentXValue:', contentXValue);

  useEffect(() => {
    setNumColumns(6);
    setListKey(`columns-${6}`);
    setIsSidebarOpen(true);
    Animated.timing(sidebarXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
    Animated.timing(contentXValue, {
      toValue: windowWidth * 0.08,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, []);

  const goToCart = () => {
    // console.log('Cart.getCartItem()');
    // console.log(Cart.getCartItem());

    if (Cart.getCartItem().length > 0) {
      props.navigation.navigate({
        name: 'MoCart',
        params: {
          test, outletData,
          menuItem,
          clicked,
          clicked1,
          clicked2,
        },
        merge: true,
      });
    } else {
      Alert.alert("Info", "No items in your cart at moment", [
        { text: "OK", onPress: () => { } }
      ],
        { cancelable: false })
    }
  }

  // if (!isTablet()) {
  //   navigation.setOptions({
  //     // headerLeft: () => null,
  //     headerBackVisible: false,
  //     headerLeft: () => (
  //       <TouchableOpacity
  //         onPress={() => {
  //           if (isAlphaUser || true) {
  //             navigation.navigate('MenuOrderingScreen');

  //             CommonStore.update((s) => {
  //               s.currPage = 'MenuOrderingScreen';
  //               s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
  //             });
  //           }
  //           else {
  //             navigation.navigate('Table');

  //             CommonStore.update((s) => {
  //               s.currPage = 'Table';
  //               s.currPageStack = [...currPageStack, 'Table'];
  //             });
  //           }
  //           if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
  //             CommonStore.update((s) => {
  //               s.expandTab = EXPAND_TAB_TYPE.OPERATION;
  //             });
  //           }
  //         }}
  //         style={[styles.headerLeftStyle, {
  //           width: windowWidth * 0.17,
  //           marginLeft: -windowWidth * 0.15,
  //           // backgroundColor: 'red',
  //         }]}>
  //         {console.log(`header $ width: ${windowWidth}`)}
  //         <Image
  //           style={[{
  //             width: 124,
  //             height: 26,
  //           }, switchMerchant ? {
  //             transform: [
  //               { scaleX: 0.7 },
  //               { scaleY: 0.7 }
  //             ],
  //           } : {}]}
  //           resizeMode="contain"
  //           source={require('../assets/image/logo.png')}
  //         />
  //       </TouchableOpacity>
  //     ),
  //     headerTitle: () => (
  //       <View
  //         style={[
  //           {
  //             justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
  //             alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
  //             marginRight: Platform.OS === 'ios' ? "27%" : 0,
  //             bottom: switchMerchant ? '2%' : 0,
  //             width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
  //           },
  //           windowWidth >= 768 && switchMerchant
  //             ? { right: windowWidth * 0.1 }
  //             : {},
  //           windowWidth <= 768
  //             ? { right: 20 }
  //             : {},

  //         ]}>
  //         <Text
  //           style={{
  //             fontSize: switchMerchant ? 20 : 24,
  //             // lineHeight: 25,
  //             textAlign: 'left',
  //             alignItems: 'flex-start',
  //             justifyContent: 'flex-start',
  //             fontFamily: 'NunitoSans-Bold',
  //             color: Colors.whiteColor,
  //             opacity: 1,
  //             paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
  //           }}>
  //           {isOnMenu ? 'Menu' : 'Menu Item Details'}
  //         </Text>
  //       </View>
  //     ),
  //     headerRight: () => (
  //       <View
  //         style={{
  //           flexDirection: 'row',
  //           alignItems: 'center',
  //           justifyContent: 'space-between',
  //         }}>
  //         {outletSelectDropdownView()}
  //         <View
  //           style={{
  //             backgroundColor: 'white',
  //             width: 0.5,
  //             height: windowHeight * 0.025,
  //             opacity: 0.8,
  //             marginHorizontal: 15,
  //             bottom: -1,
  //           }} />
  //         <TouchableOpacity
  //           onPress={() => {
  //             if (global.currUserRole === 'admin') {
  //               navigation.navigate('Setting');
  //             }
  //           }}
  //           style={{ flexDirection: 'row', alignItems: 'center' }}>
  //           <Text
  //             style={[{
  //               fontFamily: 'NunitoSans-SemiBold',
  //               fontSize: switchMerchant ? 10 : 16,
  //               color: Colors.secondaryColor,
  //               marginRight: 15,
  //             }, switchMerchant ? { width: windowWidth / 8 } : {}]}
  //             numberOfLines={switchMerchant ? 1 : 1}
  //           >
  //             {userName}
  //           </Text>
  //           <View
  //             style={{
  //               marginRight: 30,
  //               width: windowHeight * 0.05,
  //               height: windowHeight * 0.05,
  //               borderRadius: windowHeight * 0.05 * 0.5,
  //               alignItems: 'center',
  //               justifyContent: 'center',
  //               backgroundColor: 'white',
  //             }}>
  //             <Image
  //               style={{
  //                 width: windowHeight * 0.035,
  //                 height: windowHeight * 0.035,
  //                 alignSelf: 'center',
  //               }}
  //               source={require('../assets/image/profile-pic.jpg')}
  //             />
  //           </View>
  //         </TouchableOpacity>
  //       </View>
  //     ),
  //   });
  // }
  // else {

  // }

  return (
    <UserIdleWrapper disabled={!isMounted} screenName={'MenuOrderingScreen'}>
      <View style={[styles.container, !isTablet() ? { transform: [{ scaleX: 1 }, { scaleY: 1 }], } : {
        ...getTransformForScreenInsideNavigation(),
      },]}>

        {/* <Animated.View
          style={[
            styles.sidebar,
            {
              transform: [{ translateX: sidebarXValue }],
              flex: 0.8,
              //zIndex: 100,

              position: 'absolute',
              top: 0,
              left: 0,
              zIndex: 2,
              height: '100%',

              ...isSidebarOpen === false && {
                opacity: 0,
              }
              //backgroundColor: 'blue',
            }
          ]}
        >

          <View style={[styles.sidebar, !isTablet() ? { width: windowWidth * 0.08, } : { width: Dimensions.get('window').width * Styles.sideBarWidth }, {
            width: windowWidth * 0.08,
          }]}>
            <SideBar
              navigation={props.navigation}
              selectedTab={2}
              expandProduct
            />
          </View>

        </Animated.View> */}

        {console.log('render: MenuOrderingScreen.js')}

        <Animated.View style={[styles.content, {
          width: windowWidth,
        }]}>
          <View
            style={[{
              flexDirection: 'row',
              height: windowHeight,
              justifyContent: 'space-between',
              width: windowWidth,
            }, switchMerchant ? {
              // width: windowWidth * (1 - Styles.sideBarWidth) * 0.95,
              height: windowHeight
              // paddingRight: 10,
            } : {}]}>
            <Animated.View
              style={[{
                // flex: 4,
                width: isSidebarOpen ? windowWidth * 0.66 : windowWidth * 0.74,
                // width: '100%',
                // width: windowWidth * (1 - Styles.sideBarWidth),
                // width: windowWidth * 0.8,
                borderRightWidth: StyleSheet.hairlineWidth,

                // transform: [{ translateX: contentXValue }],

                //justifyContent: 'center',
                //alignItems: 'center',
                //backgroundColor: 'blue'
                ...(!isOnMenu && {
                  width: windowWidth * 0.92,
                })
              }, switchMerchant ? {
                borderRightWidth: 1,
              } : {}]}>
              <View
                style={{
                  flexDirection: 'row',
                }}>

                {isSidebarOpen ?

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%', marginTop: -10, paddingHorizontal: 10, }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                      <TouchableOpacity
                        style={{
                          marginTop: 10,
                          // width: windowWidth * 0.05,
                          width: 0,
                          opacity: 0,
                        }}
                      // onPress={sidebarClose}
                      >
                        <View
                          style={{
                            flexDirection: 'row',
                            // marginLeft: 10,
                            marginVertical: 10,
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 5,
                            //width: 160,
                            height: switchMerchant ? 35 : 40,
                            // width: windowWidth * 0.05,
                            width: 0,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                          }}>
                          <MaterialIcons name='keyboard-capslock' color={Colors.primaryColor} size={30} style={{
                            alignSelf: 'center', transform: [{ rotate: '270deg' }],
                            marginLeft: -3
                          }} />
                        </View>
                      </TouchableOpacity>

                      {!isOnCategory ? (
                        <TouchableOpacity
                          style={{
                            flexDirection: 'row',
                            marginLeft: windowWidth * 0.002,
                            top: 5,
                            //paddingBottom: '5%',
                            //backgroundColor: 'blue',
                            // width: '33%',

                          }}
                          onPress={() => {
                            requestAnimationFrame(() => {
                              CommonStore.update((s) => {
                                s.isOnMenu = true;
                                s.isOnCategory = true;
                                setSearch('');
                                s.moSearch = '';
                              });
                            });
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'center',
                              alignContent: 'center',
                              // marginBottom: 25,
                            }}>
                            <View>
                              {switchMerchant ? (
                                <Feather
                                  name="chevron-left"
                                  size={20}
                                  color={Colors.primaryColor}
                                  style={{ paddingLeft: '1%' }}
                                />
                              ) : (
                                <Feather
                                  name="chevron-left"
                                  size={30}
                                  color={Colors.primaryColor}
                                  style={{}}
                                />
                              )}
                            </View>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 14 : 17,
                                textAlign: 'center',
                                fontFamily: 'NunitoSans-Bold',
                                marginBottom: Platform.OS === 'ios' ? 0 : 2,
                                //top: -2,
                                //marginLeft: -3,
                              }}>
                              Product Catalog
                            </Text>
                          </View>
                        </TouchableOpacity>
                      ) : (
                        <></>
                      )}
                    </View>

                    <View
                      style={{
                        width: (switchMerchant && windowWidth < 775 && windowHeight < 412) ? 150 : 200,
                        height: switchMerchant ? 35 : 40,
                        backgroundColor: 'white',
                        borderRadius: 5,
                        marginTop: 20,
                        flexDirection: 'row',
                        alignContent: 'center',
                        alignItems: 'center',

                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,

                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        // marginLeft: Platform.OS == 'ios' ? '25%' : '28%',
                        //marginRight: switchMerchant ? 0 : 0,
                      }}>
                      <Feather
                        name="search"
                        size={switchMerchant ? 13 : 18}
                        color={Colors.primaryColor}
                        style={{ marginLeft: 15, paddingRight: '5%' }}
                      />
                      <TextInput
                        editable={!loading}
                        underlineColorAndroid={Colors.whiteColor}
                        style={{
                          width: 150,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Regular',
                          height: 45,
                        }}
                        clearButtonMode="while-editing"

                        //here edit
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => {
                          if (text) {
                            setSearch(text);
                            CommonStore.update((s) => {
                              s.isOnCategory = false;
                              s.moSearch = text;
                            });
                          } else {
                            CommonStore.update((s) => {
                              s.isOnCategory = true;
                              s.moSearch = '';
                            });
                            setSearch('');
                          }
                          // setIsOnSearch(true)
                        }}
                        value={search}
                      />
                    </View>
                  </View>

                  :

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '99%', marginTop: -10 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                      {/* <TouchableOpacity
                        style={{ marginTop: 10, width: windowWidth * 0.15, }}
                        onPress={sidebarOpen}
                      >
                        <View style={{
                          flexDirection: 'row',
                          marginLeft: 10,
                          marginVertical: 10,
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: Colors.primaryColor,
                          borderRadius: 5,
                          //width: 160,
                          height: switchMerchant ? 35 : 40,
                          width: windowWidth * 0.15,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,

                        }}>
                          <MaterialIcons name='keyboard-capslock' color={Colors.whiteColor} size={30} style={{ alignSelf: 'flex-start', transform: [{ rotate: '90deg' }], marginTop: 5 }} />
                          <Text style={{
                            color: Colors.whiteColor,
                            marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}
                          >
                            MORE PAGES
                          </Text>
                        </View>
                      </TouchableOpacity> */}

                      {!isOnCategory ? (
                        <TouchableOpacity
                          style={{
                            flexDirection: 'row',
                            marginLeft: windowWidth * 0.002,
                            top: 5,
                            // marginLeft: windowWidth * 0.07,
                            //paddingBottom: '5%',
                            //backgroundColor: 'blue',
                            // width: '26%',
                          }}
                          onPress={() => {
                            requestAnimationFrame(() => {
                              CommonStore.update((s) => {
                                s.isOnMenu = true;
                                s.isOnCategory = true;
                                s.moSearch = '';
                                setSearch('');
                              });
                            });
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'center',
                              alignContent: 'center',
                              // marginBottom: 25,
                            }}>
                            <View>
                              {switchMerchant ? (
                                <Feather
                                  name="chevron-left"
                                  size={20}
                                  color={Colors.primaryColor}
                                  style={{ paddingLeft: '1%' }}
                                />
                              ) : (
                                <Feather
                                  name="chevron-left"
                                  size={30}
                                  color={Colors.primaryColor}
                                  style={{}}
                                />
                              )}
                            </View>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                fontSize: switchMerchant ? 14 : 17,
                                textAlign: 'center',
                                fontFamily: 'NunitoSans-Bold',
                                marginBottom: Platform.OS === 'ios' ? 0 : 2,
                                //top: -2,
                                //marginLeft: -3,
                              }}>
                              Product Catalog
                            </Text>
                          </View>
                        </TouchableOpacity>
                      ) : (
                        <></>
                      )}
                    </View>

                    <View
                      style={{
                        width: (switchMerchant && windowWidth < 775 && windowHeight < 412) ? 150 : 200,
                        height: switchMerchant ? 35 : 40,
                        backgroundColor: 'white',
                        borderRadius: 5,
                        marginTop: 20,
                        flexDirection: 'row',
                        alignContent: 'center',
                        alignItems: 'center',

                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,

                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        // marginLeft: Platform.OS == 'ios' ? '25%' : '28%',
                        //marginRight: switchMerchant ? 0 : 0,
                      }}>
                      <Feather
                        name="search"
                        size={switchMerchant ? 13 : 18}
                        color={Colors.primaryColor}
                        style={{ marginLeft: 10, paddingRight: '5%' }}
                      />
                      <TextInput
                        editable={!loading}
                        underlineColorAndroid={Colors.whiteColor}
                        style={{
                          width: 150,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Regular',
                          height: 45,
                        }}
                        clearButtonMode="while-editing"
                        //searches
                        //here edit
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => {
                          if (text) {
                            setSearch(text);
                            CommonStore.update((s) => {
                              s.isOnCategory = false;
                              s.moSearch = text;
                            });
                          } else {
                            CommonStore.update((s) => {
                              s.isOnCategory = true;
                              s.moSearch = '';
                            });
                            setSearch('');
                          }
                          // setIsOnSearch(true)
                        }}
                        value={search}
                      />
                    </View>
                  </View>
                }



              </View>
              {/* menuList */}
              {
                isOnMenu ?
                  // <Text>Menu Display Here</Text>
                  <MoOutletMenuScreen numColumns={numColumns} key={listKey} navigation={props.navigation
                  } route={{
                    ...props.route,
                    params: routeParamsOutletMenu,
                  }}
                  />

                  :
                  // <Text>Modal</Text>
                  <MoMenuItemDetailsScreen sidebarOpen={isSidebarOpen} navigation={navigation} route={{
                    ...props.route,
                    params: routeParamsMenuItemDetails,
                  }} />


              }
            </Animated.View >
            {/* right */}
            {isOnMenu ?
              < View style={{
                // flex: 1.5,
                // width: '26%',
                width: isSidebarOpen ? windowWidth * 0.36 : windowWidth * 0.26,
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                {/* <Text>Cart Part</Text> */}
                < MoCartScreen navigation={props.navigation} route={{
                  ...props.route,
                  params: routeParamsCart,
                }} />
              </View >
              : null}
          </View >


        </Animated.View >

      </View >
    </UserIdleWrapper >
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: Colors.highlightColor,
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  list_PhoneScreen: {
    backgroundColor: Colors.whiteColor,
    // width: Dimensions.get('window').width * 0.79,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth) * 0.945,
    height: Dimensions.get('window').height * 0.72,
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    flex: 1,
    minHeight: '100%',
    backgroundColor: 'red',
    // shadowColor: '#000',
    // shadowOffset: {
    //     width: 0,
    //     height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {


  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default MenuOrderingScreen;
