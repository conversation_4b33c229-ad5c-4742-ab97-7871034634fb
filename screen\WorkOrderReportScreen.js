import { Text } from "react-native-fast-text";
import React, { Component, useEffect, useReducer, useState, useCallback, useRef } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Alert,
    TouchableOpacity,
    TextInput,
    Dimensions,
    FlatList,
    Modal,
    PermissionsAndroid,
    Platform,
    useWindowDimensions,
    ActivityIndicator,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import Icon2 from 'react-native-vector-icons/EvilIcons';
import Icon3 from 'react-native-vector-icons/Foundation';
import Icon4 from 'react-native-vector-icons/FontAwesome5';
import DropDownPicker from 'react-native-dropdown-picker';
// import { ceil } from 'react-native-reanimated';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from "react-native-modal-datetime-picker";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import moment from 'moment';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles'
import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import RNFetchBlob from 'rn-fetch-blob';
import {
    isTablet,
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
    sortWOList,
    consumeOutletSupplyItemRecursive,
    recordOutletSupplyItemTransaction,
    calculateQuantityUsageAndQuantityWastage,
} from '../util/common';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import {
    STOCK_TRANSFER_STATUS,
    STOCK_TRANSFER_STATUS_PARSED,
    EMAIL_REPORT_TYPE,
    PURCHASE_ORDER_STATUS,
    EXPAND_TAB_TYPE,
    WOLIST_SORT_FIELD_TYPE,
    REPORT_SORT_FIELD_TYPE,
} from '../constant/common';
import { convertArrayToCSV, generateEmailReport } from '../util/common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import XLSX from 'xlsx';
import { Row } from 'react-native-table-component';
import { v4 as uuidv4 } from 'uuid';
import GCalendar from '../assets/svg/GCalendar'
import GCalendarGrey from '../assets/svg/GCalendarGrey'
import { OutletStore } from '../store/outletStore';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
// import ViewShot from 'react-native-view-shot';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
// import stockTransferOrderAmountsHtml from '../templates/stock_transfer_order_amounts.html';
import { STOCK_TRANSFER_ORDER_AMOUNTS_HTML } from '../templates/stock_transfer_order_amounts';
import { KEYWORDS } from '../templates/keywords';
import APILocal from '../util/apiLocalReplacers';
import { logEventAnalytics } from '../util/common';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import Entypo from 'react-native-vector-icons/Entypo';
import firestore from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";

const RNFS = require('@dr.pogodin/react-native-fs');

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);

const WorkOrderReportScreen = props => {
    const {
        navigation,
    } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const [keyboardHeight] = useKeyboard();

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const viewShotRef = useRef();


    const [currWOListSort, setCurrWOListSort] = useState('');
    const [currWOItemSort, setCurrWOItemSort] = useState('');
    const [stockTransfer, setStockTransfer] = useState(true);
    const [addPurchase, setAddPurchase] = useState(false);
    const [editPurchase, setEditPurchase] = useState(false);
    const [addStockTransfer, setAddStockTransfer] = useState(false);
    const [stockList, setStockList] = useState([]);
    const [stockTransferList, setStockTransferList] = useState([]);
    const [stockTakeList, setStockTakeList] = useState([]);
    const [orderList, setOrderList] = useState([]);
    const [itemsToOrder, setItemsToOrder] = useState([{}, {}, {}],);
    const [itemsToOrder2, setItemsToOrder2] = useState([{}, {}, {}],);
    const [addStockTransferList, setAddStockTransferList] = useState([{}, {}, {}],);
    const [addCountedStockTakeList, setAddCountedStockTakeList] = useState([{}, {}, {}],);
    const [addUnCountedStockTakeList, setAddUnCountedStockTakeList] = useState([{}, {}, {}],);
    const [productList, setProductList] = useState([]);
    const [isSelected, setIsSelected] = useState(false);
    const [isSelected2, setIsSelected2] = useState(false);
    const [isSelected3, setIsSelected3] = useState(true);
    const [isSelected4, setIsSelected4] = useState(false);
    const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
    const [date, setDate] = useState(Date.now());
    const [date1, setDate1] = useState(Date.now());
    const [createdDate, setCreatedDate] = useState(Date.now());
    const [visible, setVisible] = useState(false);
    const [Email, setEmail] = useState('');
    const [modal, setModal] = useState(false);
    // const [// outletId, set// outletId] = useState(1);
    const [outletId, setOutletId] = useState(User.getOutletId());
    const [search, setSearch] = useState('');
    const [search2, setSearch2] = useState('');
    const [search3, setSearch3] = useState('');
    const [ideal, setIdeal] = useState('');
    const [minimum, setMinimum] = useState('');
    const [itemId, setItemId] = useState('');
    const [choose, setChoose] = useState(null);

    const [loading, setLoading] = useState(false);

    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
    const [rev_date, setRev_date] = useState(moment().subtract(6, 'days').startOf('day'),);
    const [rev_date1, setRev_date1] = useState(moment().endOf(Date.now()).endOf('day'),);
    //////////////////////////////////////////////////////////////////////

    const [poId, setPoId] = useState('');
    const [editMode, setEditMode] = useState(false);
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [exportEmail, setExportEmail] = useState('');
    const [exportModal, setExportModal] = useState(false);
    const [importModal, setImportModal] = useState(false);

    const [doModal, setDoModal] = useState(false);
    const [doItem, setDoItem] = useState(null);

    const [isLoadingExcel, setIsLoadingExcel] = useState(false);
    const [isLoadingCsv, setIsLoadingCsv] = useState(false);
    const [isLoadingLocalExcel, setIsLoadingLocalExcel] = useState(false);
    const [isLoadingLocalCsv, setIsLoadingLocalCsv] = useState(false);

    const [poStatus, setPoStatus] = useState(STOCK_TRANSFER_STATUS.CREATED);

    const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
    const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

    const [selectedSourceOutletIdPrev, setSelectedSourceOutletIdPrev] = useState('');
    const [selectedSourceOutletId, setSelectedSourceOutletId] = useState('');

    const [outletSupplyItemDropdownList, setOutletSupplyItemDropdownList] = useState([]);

    const [poItems, setPoItems] = useState([
        {
            outletSupplyItemId: '',
            name: '',
            sku: '',
            unit: '',
            skuMerchant: '',
            quantity: 0,
            transferQuantity: 0,
            balance: 0,
            price: 0,
            totalPrice: 0,

            supplyItem: null,
        }
    ]);

    const [subtotal, setSubtotal] = useState(0);
    const [taxTotal, setTaxTotal] = useState(0);
    const [discountTotal, setDiscountTotal] = useState(0);
    const [finalTotal, setFinalTotal] = useState(0);

    const [outletSupplyItems, setOutletSupplyItems] = useState([]);

    const [selectedIndex, setSelectedIndex] = useState(null);

    // const [supplyItems, setSupplyItems] = useState([]);

    const outletItems = OutletStore.useState(s => s.outletItems);
    const allOutletsItems = OutletStore.useState(s => s.allOutletsItems);

    const supplyItems = CommonStore.useState(s => s.supplyItems);
    const supplyItemsSkuDict = CommonStore.useState(s => s.supplyItemsSkuDict);

    const allOutletsSupplyItemsSkuDict = CommonStore.useState(s => s.allOutletsSupplyItemsSkuDict);
    const allOutletsSupplyItems = CommonStore.useState(s => s.allOutletsSupplyItems);
    const allOutletsSupplyItemsDict = CommonStore.useState(s => s.allOutletsSupplyItemsDict);

    const outletSupplyItemsDict = CommonStore.useState(s => s.outletSupplyItemsDict);
    const outletSupplyItemsSkuDict = CommonStore.useState(s => s.outletSupplyItemsSkuDict);

    const allOutlets = MerchantStore.useState(s => s.allOutlets);
    const merchantId = UserStore.useState(s => s.merchantId);
    const stockTransfersProduct = CommonStore.useState(s => s.stockTransfersProduct);

    const userName = UserStore.useState(s => s.name);
    const userId = UserStore.useState(s => s.firebaseUid);
    const merchantName = MerchantStore.useState(s => s.name);

    const currOutlet = MerchantStore.useState(s => s.currOutlet);
    const currOutletId = MerchantStore.useState(s => s.currOutletId);

    const isLoading = CommonStore.useState((s) => s.isLoading);

    const dropDownRef = React.useRef();
    const dropDownRef1 = React.useRef();

    const selectedStockTransferEdit = CommonStore.useState(s => s.selectedStockTransferEdit);
    const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const woList = CommonStore.useState(s => s.woList);
    const [selectedWorkOrder, setSelectedWorkOrder] = useState([]);
    const [showDetails, setShowDetails] = useState(false);

    useEffect(() => {
        if (currOutletId !== '' &&
            allOutlets.length > 0 &&
            stockTransfersProduct.length > 0) {
            var stockTransferProductTemp = [];
            for (var i = 0; i < stockTransfersProduct.length; i++) {
                if (moment(rev_date).isSameOrBefore(stockTransfersProduct[i].createdAt) &&
                    moment(rev_date1).isAfter(stockTransfersProduct[i].createdAt)
                ) {
                    stockTransferProductTemp.push(stockTransfersProduct[i]);
                }
            }
            stockTransferProductTemp.sort((a, b) => b.orderDate - a.orderDate)
            setStockTransferList(stockTransferProductTemp);
        }
    }, [currOutletId, rev_date, rev_date1, stockTransfersProduct])

    useEffect(() => {
        if (selectedStockTransferEdit) {
            // insert info

            setEditMode(false);

            setPoId(selectedStockTransferEdit.stId);
            setPoStatus(selectedStockTransferEdit.status);
            setSelectedSourceOutletId(selectedStockTransferEdit.sourceOutletId);
            setSelectedTargetOutletId(selectedStockTransferEdit.targetOutletId);
            setDate(selectedStockTransferEdit.estimatedArrivalDate);
            setCreatedDate(selectedStockTransferEdit.createdAt);


            if (selectedStockTransferEdit.stItems) {
                setPoItems(selectedStockTransferEdit.stItems);

                // delay the state update
                setTimeout(() => {
                    setPoItems(selectedStockTransferEdit.stItems);
                }, 100);
            }
        }
        else {
            // designed to always mounted, thus need clear manually...

            setEditMode(false);

            if (stockTransfersProduct.length > 0) {
                // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
                setPoId(`ST${(stockTransfersProduct.length + 1).toString().padStart(4, '0')}`);
            }
            setPoStatus(STOCK_TRANSFER_STATUS.CREATED);
            setSelectedSourceOutletId(allOutlets[0].uniqueId);
            setSelectedTargetOutletId(allOutlets[0].uniqueId);
            setDate(Date.now());

            if (outletItems.length > 0) {
                setPoItems([
                    {
                        outletSupplyItemId: outletItems[0].uniqueId,
                        name: outletItems[0].name,
                        sku: outletItems[0].sku,
                        unit: '',
                        skuMerchant: outletItems[0].skuMerchant,
                        quantity: outletItems[0].stockCount || 0,
                        transferQuantity: 0,
                        balance: 0,
                        price: outletItems[0].price,
                        totalPrice: 0,

                        supplyItem: outletItems[0],
                    }
                ]);
            }

            // if (outletSupplyItems.length > 0 && Object.keys(allOutletsSupplyItemsDict).length > 0) {
            // if (outletSupplyItems.length > 0) {
            //   setPoItems([
            //     {
            //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
            //       name: outletSupplyItems[0].name,
            //       sku: outletSupplyItems[0].sku,
            //       quantity: outletSupplyItems[0].quantity,
            //       transferQuantity: 0,
            //       price: outletSupplyItems[0].price,
            //       totalPrice: 0,
            //     }
            //   ]);
            // }
            // else {
            //   setPoItems([
            //     {
            //       outletSupplyItemId: '',
            //       name: '',
            //       sku: '',
            //       quantity: 0,
            //       transferQuantity: 0,
            //       price: 0,
            //       totalPrice: 0,
            //     }
            //   ]);
            // }
        }
    }, [selectedStockTransferEdit, addStockTransfer]);

    useEffect(() => {
        if (selectedStockTransferEdit === null && stockTransfersProduct.length > 0) {
            // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
            setPoId(`ST${(stockTransfersProduct.length + 1).toString().padStart(4, '0')}`);
        }
    }, [stockTransfersProduct]);

    useEffect(() => {
        if (outletItems.length > 0) {
            // setPoItems([
            //   {
            //     outletSupplyItemId: outletSupplyItems[0].uniqueId,
            //     name: outletSupplyItems[0].name,
            //     sku: outletSupplyItems[0].sku,
            //     skuMerchant: outletSupplyItems[0].skuMerchant,
            //     quantity: outletSupplyItems[0].quantity,
            //     transferQuantity: outletSupplyItems[0].transferQuantity,
            //     balance: outletSupplyItems[0].balance,
            //     price: outletSupplyItems[0].price,
            //     totalPrice: 0,

            //     supplyItem: supplyItems[0],
            //   }
            // ]);

            // delay the state updating
            // setTimeout(() => {
            //   setPoItems([
            //     {
            //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
            //       name: outletSupplyItems[0].name,
            //       sku: outletSupplyItems[0].sku,
            //       skuMerchant: outletSupplyItems[0].skuMerchant,
            //       quantity: outletSupplyItems[0].quantity,
            //       transferQuantity: outletSupplyItems[0].transferQuantity,
            //       balance: outletSupplyItems[0].balance,
            //       price: outletSupplyItems[0].price,
            //       totalPrice: 0,

            //       supplyItem: supplyItems[0],
            //     }
            //   ]);
            // }, 500);
        }
        else {
            setPoItems([
                {
                    outletSupplyItemId: '',
                    name: '',
                    sku: '',
                    unit: '',
                    skuMerchant: '',
                    quantity: 0,
                    transferQuantity: 0,
                    price: 0,
                    totalPrice: 0,

                    supplyItem: null,
                }
            ]);
        }
    }, [selectedSourceOutletId]);

    useEffect(() => {
        setOutletSupplyItems(allOutletsSupplyItems.filter(outletSupplyItem => {
            if (outletSupplyItem.outletId === selectedSourceOutletId && outletSupplyItem.quantity > 0) {
                return true;
            }
        }));
    }, [allOutletsSupplyItems, selectedSourceOutletId]);

    useEffect(() => {
        const outletDropdownListTemp = allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId }))

        setTargetOutletDropdownList(outletDropdownListTemp);

        if (selectedTargetOutletId === '' && allOutlets.length > 0) {
            setSelectedTargetOutletId(allOutlets[0].uniqueId);
            setSelectedSourceOutletId(allOutlets[0].uniqueId);
        }
    }, [allOutlets]);

    useEffect(() => {
        setOutletSupplyItemDropdownList(outletItems.map(outletSupplyItem => {
            // if (selectedSupplierId === supplyItem.supplierId) {
            //   return { label: supplyItem.name, value: supplyItem.uniqueId };
            // }      

            return { label: outletSupplyItem.name, value: outletSupplyItem.uniqueId };
        }));

        if (outletItems.length > 0 &&
            poItems.length === 1 &&
            poItems[0].outletSupplyItemId === '') {
            setPoItems([
                {
                    outletSupplyItemId: outletItems[0].uniqueId,
                    name: outletItems[0].name,
                    sku: outletItems[0].sku,
                    unit: '',
                    skuMerchant: outletItems[0].skuMerchant,
                    quantity: outletItems[0].stockCount || 0,
                    transferQuantity: 0,
                    balance: 0,
                    price: outletItems[0].price,
                    totalPrice: 0,

                    supplyItem: outletItems[0],
                }
            ]);
        }
        else if (
            poItems[0].outletSupplyItemId !== ''
            // &&
            // Object.keys(allOutletsSupplyItemsDict).length > 0
        ) {
            if (selectedSourceOutletIdPrev.length > 0 &&
                selectedSourceOutletIdPrev !== selectedSourceOutletId) {
                // reset current outlet supply items

                setPoItems([
                    {
                        outletSupplyItemId: outletItems[0].uniqueId,
                        name: outletItems[0].name,
                        unit: '',
                        sku: outletItems[0].sku,
                        skuMerchant: outletItems[0].skuMerchant,
                        quantity: outletItems[0].stockCount || 0,
                        transferQuantity: 0,
                        balance: 0,
                        price: outletItems[0].price,
                        totalPrice: 0,

                        supplyItem: outletItems[0],
                    }
                ]);

                // disabled first, outletSupplyItems might slow to retrieve
                // setSelectedSourceOutletIdPrev(selectedSourceOutletId);
            }
            else {
                var poItemsTemp = [
                    ...poItems,
                ];

                for (var i = 0; i < poItemsTemp.length; i++) {
                    poItemsTemp[i] = {
                        ...poItemsTemp[i],
                        // quantity: allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].quantity : 0, // check if the supply item sku for this outlet existed | might changed in real time
                        // price: allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].price : 0, // might changed in real time
                        quantity: allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId) ? (allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId).stockCount || 0) : 0,
                        price: allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId) ? (allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId).price || 0) : 0,
                    };
                }

                setPoItems(poItemsTemp);
            }
        }
    }, [
        // outletSupplyItems,
        // allOutletsSupplyItemsDict,
        // supplyItems,
        outletItems,
        allOutletsItems,
        selectedSourceOutletIdPrev
    ]);

    useEffect(() => {
        // console.log('balance');
        // console.log(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
        setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
    }, [poItems]);

    useEffect(() => {
        // console.log('subtotal');
        // console.log(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
        setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
    }, [poItems]);

    // useEffect(() => {
    //   // console.log('taxTotal');
    //   // console.log(subtotal * selectedSupplier.taxRate);
    //   setTaxTotal(subtotal * selectedSupplier.taxRate);
    // }, [subtotal]);

    useEffect(() => {
        // console.log('finalTotal');
        // console.log((subtotal - discountTotal) + taxTotal);
        setFinalTotal((subtotal - discountTotal) + taxTotal);
    }, [subtotal, discountTotal, taxTotal]);

    useEffect(() => {
        requestStoragePermission();

        setPoId(nanoid());
    }, []);


    // useEffect(() => {
    //   if (poItems.find(poItem => poItem.outletSupplyItemId === poItem.outletSupplyItemId)){
    //     Alert.alert(
    //       'Error',
    //       'Same Supply Item.',
    //       [
    //         {
    //           text: "OK", onPress: () => {
    //           }
    //         }
    //       ],
    //     );
    //   return;
    //   }
    // }, [poItems]);

    //////////////////////////////////////////////////////////////////////

    const setState = () => { };

    // navigation.dangerouslyGetParent().setOptions({
    //   tabBarVisible: false
    // });

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }

                    logEventAnalytics({
                        eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_LOGO,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_LOGO,
                    })
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        bottom: switchMerchant ? '2%' : 0,
                        width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
                    },
                    windowWidth >= 768 && switchMerchant
                        ? { right: windowWidth * 0.1 }
                        : {},
                    windowWidth <= 768
                        ? { right: 20 }
                        : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Work Order List
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }}></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }

                        logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_PROFILE,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_PROFILE,
                        })
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    // componentDidMount = () => {
    //   // setInterval(() => {
    //   //   getStockOrder()
    //   //   getStockTransfer()
    //   //   getLowStock()
    //   // }, 1000);
    //   getStockOrder()
    //   getStockTransfer()
    //   getLowStock()
    // }

    // async componentWillMount = () => {
    //   await requestStoragePermission()
    // }

    const requestStoragePermission = async () => {
        try {
            const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
                {
                    title: "Lumiere Storage Permission",
                    message:
                        "Lumiere App needs access to your storage ",
                    buttonNegative: "Cancel",
                    buttonPositive: "OK"
                }
            );
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                // console.log("Storage permission granted");
            } else {
                // console.log("Storage permission denied");
            }
        } catch (err) {
            console.warn(err);
        }
    }

    const importSelectFile = async () => {
        try {
            const res = await DocumentPicker.pickSingle({
                type: [DocumentPicker.types.xlsx],
            });

            // console.log(res)

        } catch (err) {

            if (DocumentPicker.isCancel(err)) {

            } else {
                throw err;
            }
        }
    }

    const convertDataToExcelFormat = () => {
        var excelData = [];

        for (var i = 0; i < stockTransferList.length; i++) {
            for (var j = 0; j < stockTransferList[i].stItems.length; j++) {
                var excelRow = {
                    'Transfer Product': stockTransferList[i].stItems[j].name,
                    'In Stock': stockTransferList[i].stItems[j].quantity ? stockTransferList[i].stItems[j].quantity.toFixed(2) : '0',
                    'Transfer Quantity': stockTransferList[i].stItems[j].transferQuantity ? stockTransferList[i].stItems[j].transferQuantity : '0',
                    'Balance': stockTransferList[i].stItems[j].balance ? stockTransferList[i].stItems[j].balance.toFixed(2) : '0',
                    'Stock Transfer ID': stockTransferList[i].stId,
                    'Created Date': moment(stockTransferList[i].orderDate).format('DD/MM/YYYY'),
                    'From': stockTransferList[i].sourceOutletName,
                    'To': stockTransferList[i].targetOutletName,
                    'Status': stockTransferList[i].status,

                };

                excelData.push(excelRow);
            }
        }



        // console.log('excelData');
        // console.log(excelData);

        return excelData;
    };

    const handleExportExcel = () => {
        const excelData = convertDataToExcelFormat();

        var ws = XLSX.utils.json_to_sheet(excelData);
        var wb = XLSX.utils.book_new();

        XLSX.utils.book_append_sheet(wb, ws, "KooDoo Transfer Report");
        const wbout = XLSX.write(wb, { type: 'binary', bookType: "xlsx" });
        RNFS.writeFile(`${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.xlsx`, wbout, 'ascii').then((success) => {
            Alert.alert(
                'Success',
                `Exported to ${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.xlsx`,
                [
                    {
                        text: 'OK',
                        onPress: () => {
                            CommonStore.update((s) => {
                                s.isLoading = false;
                            });
                            setIsLoadingLocalExcel(false);
                            setExportModal(false);

                            logEventAnalytics({
                                eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT,
                            })
                        },
                    },
                ],
                { cancelable: false },
            );
            console.log('Success');
        }).catch((e) => {
            console.log('Error', e);
        });
    };

    const handleExportCsv = () => {

        const excelData = convertDataToExcelFormat();

        var ws = XLSX.utils.json_to_sheet(excelData);
        var wb = XLSX.utils.book_new();

        XLSX.utils.book_append_sheet(wb, ws, "KooDoo Transfer Report");
        const wbout = XLSX.write(wb, { type: 'binary', bookType: "csv" });
        RNFS.writeFile(`${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.csv`, wbout, 'ascii').then((success) => {
            Alert.alert(
                'Success',
                `Exported to ${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.csv`,
                [
                    {
                        text: 'OK',
                        onPress: () => {
                            CommonStore.update((s) => {
                                s.isLoading = false;
                            });
                            setIsLoadingLocalCsv(false);
                            setExportModal(false);

                            logEventAnalytics({
                                eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT,
                            })
                        },
                    },
                ],
                { cancelable: false },
            );
            console.log('Success');
        }).catch((e) => {
            console.log('Error', e);
        });

    };

    // function here

    const exportHtmlToPdf = async (item) => {
        var sourceOutlet = allOutlets.find(outlet => outlet.uniqueId === item.sourceOutletId);
        var targetOutlet = allOutlets.find(outlet => outlet.uniqueId === item.targetOutletId);
        var sourceOutletEmail = (sourceOutlet && sourceOutlet.email) ? sourceOutlet.email : 'N/A';
        var sourceOutletPhone = (sourceOutlet && sourceOutlet.phone) ? sourceOutlet.phone : 'N/A';
        var targetOutletEmail = (targetOutlet && targetOutlet.email) ? targetOutlet.email : 'N/A';
        var targetOutletPhone = (targetOutlet && targetOutlet.phone) ? targetOutlet.phone : 'N/A';

        var totalPriceForAll = 0;
        var totalTransferQtyForAll = 0;

        var itemStrList = [];
        for (var i = 0; i < item.stItems.length; i++) {
            // totalPriceForAll += item.stItems[i].totalPrice ? item.stItems[i].totalPrice : 0;
            totalTransferQtyForAll += item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity : 0;

            // itemStrList.push(
            //   `<tr>
            //   <td>
            //     <b>${item.stItems[i].name || '-'}</b>
            //     <br />
            //     ${item.stItems[i].skuMerchant || '-'}
            //   </td>
            //   <td>
            //   ${item.stItems[i].quantity.toFixed(0)}
            //   </td>
            //   <td>
            //   ${item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity.toFixed(0) : '0'}
            //   </td>
            //   <td>
            //   ${item.stItems[i].totalPrice ? item.stItems[i].totalPrice.toFixed(2) : '0.00'}
            //   </td>
            // </tr>`
            // );

            itemStrList.push(
                `<tr>
        <td>
          <b>${item.stItems[i].name || '-'}</b>
          <br />
          ${item.stItems[i].skuMerchant || '-'}
        </td>
        <td>
        ${item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity.toFixed(0) : '0'}
        </td>
      </tr>`
            );
        }

        let replacedHtml = STOCK_TRANSFER_ORDER_AMOUNTS_HTML
            .replaceAll(KEYWORDS.MERCHANT_NAME, merchantName)
            .replaceAll(KEYWORDS.TITLE, 'Delivery Order')
            .replaceAll(KEYWORDS.HEADER_DATE, moment(item.createdAt).format('YYYY-MM-DD'))
            .replaceAll(KEYWORDS.HEADER_ID, item.stId)
            .replaceAll(KEYWORDS.OUTLET_NAME_FROM, item.sourceOutletName)
            .replaceAll(KEYWORDS.OUTLET_EMAIL_FROM, sourceOutletEmail)
            .replaceAll(KEYWORDS.OUTLET_PHONE_FROM, sourceOutletPhone)
            .replaceAll(KEYWORDS.OUTLET_NAME_TO, item.targetOutletName)
            .replaceAll(KEYWORDS.OUTLET_EMAIL_TO, targetOutletEmail)
            .replaceAll(KEYWORDS.OUTLET_PHONE_TO, targetOutletPhone)
            .replaceAll(KEYWORDS.REMARKS, item.remarks ? item.remarks : 'N/A')
            .replaceAll(KEYWORDS.ROW_ITEMS, itemStrList.join(''))
            .replaceAll(KEYWORDS.FOOTER_TOTAL, totalTransferQtyForAll.toFixed(0));

        // return replacedHtml;

        let options = {
            // html: STOCK_TRANSFER_ORDER_AMOUNTS_HTML,
            html: replacedHtml,
            fileName: `stock-transfer-do-${item && item.stId ? moment(item.createdAt).format('YYYY-MM-DD') + '-' + item.stId : item.stId}-${moment().valueOf()}`,
            // directory: Platform.OS === 'ios'
            //   ? RNFS.DocumentDirectoryPath
            //   : RNFS.DownloadDirectoryPath,
            directory: Platform.OS === 'ios'
                ? 'Documents'
                : 'Download',
        };

        let file = await RNHTMLtoPDF.convert(options);

        if (file && file.filePath) {
            Alert.alert(
                'Success',
                `Exported to ${file.filePath}`,
            );
        }
    };

    const updateStatusToRejected = async (uniqueId, currentStatus) => {
        try {
            const newStatus = currentStatus !== 'REJECTED' ? 'REJECTED' : 'PENDING';
            await firestore().collection(Collections.WOList).doc(uniqueId).update({
                status: newStatus,
                updatedAt: Date.now(),
            });
            Alert.alert('Success', `The work order has been ${newStatus === 'REJECTED' ? 'rejected' : 'restored to pending'}.`);
        } catch (error) {
            console.error('Error updating work order status:', error);
            Alert.alert('Error', 'An error occurred while updating the work order status.');
        }
    };

    const updateStatusToComplete = async (item) => {
        try {
            const newStatus = item.status === 'PENDING' ? 'COMPLETED' : 'PENDING';
            await firestore().collection(Collections.WOList).doc(item.uniqueId).update({
                status: newStatus,
                updatedAt: Date.now(),
            });

            const userOrderStatus = item.status === 'PENDING' ? 'WOD' : 'WOIP';
            const updatePromises = item.woItems.map(woItem => {
                let promise = null;

                if (item.type === 'PO_STOCK') {
                    promise = new Promise((resolve) => resolve());
                }
                else {
                    promise = firestore().collection(Collections.UserOrder).doc(woItem.userOrderId).update({
                        woStatus: userOrderStatus, // Update woStatus accordingly
                        updatedAt: Date.now(),
                    });
                }

                return promise;
            });

            // Wait for all updates to complete
            await Promise.all(updatePromises);
            Alert.alert('Success', `The work order has been ${newStatus === 'COMPLETED' ? 'marked as completed.' : 'restored to pending'}.`,
                [
                    {
                        cancelable: false
                    },
                    {
                        text: "OK",
                        onPress: () => {
                            setAddStockTransfer(false);
                            setSelectedWorkOrder([]);
                        }
                    }
                ]);

        } catch (error) {
            console.error('Error updating work order status:', error);
            Alert.alert('Error', 'An error occurred while marking the work order as completed.');
        }
    };

    const filterOrders = (param) => {
        if (param.value === 1) {
            CommonStore.update((s) => {
                s.woList = s.woListTemp || s.woList;
            });
            return;
        }

        if (param.value === 2) {
            CommonStore.update((s) => {
                s.woList = s.woListTemp.filter((wo) => wo.status === 'PENDING');
            });
        }

        if (param.value === 3) {
            CommonStore.update((s) => {
                s.woList = s.woListTemp.filter((wo) => wo.status === 'COMPLETED');
            });
        }

        if (param.value === 4) {
            CommonStore.update((s) => {
                s.woList = s.woListTemp.filter((wo) => wo.status === 'REJECTED');
            });
        }
    };

    const renderWO = ({ item, index }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    setSelectedWorkOrder(item);
                    setShowDetails(true);
                }}
                style={{
                    flexDirection: 'row',
                    paddingVertical: 10,
                    paddingHorizontal: 6,
                    alignItems: 'center',
                    borderBottomWidth: 0.5,
                }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{ width: '20%', }}>
                        <Text style={{
                            fontSize: switchMerchant ? 10 : 13, fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 5,
                        }}>
                            {item.woId}
                        </Text>
                    </View>
                    <View style={{ width: '15%', }}>
                        <Text style={{
                            fontSize: switchMerchant ? 10 : 13, fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 5,
                        }}>
                            {moment(item.orderDate).format('DD MMM YYYY')}
                        </Text>
                    </View>
                    <View style={{ width: '30%', }}>
                        <Text style={{
                            fontSize: switchMerchant ? 10 : 13, fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 5,
                        }}>
                            {item.targetOutletName.includes(item.sourceOutletName)
                                ? 'Same Store'
                                : `${item.sourceOutletName} to ${item.targetOutletName.join(', ')}`}
                        </Text>
                    </View>
                    {/* <View style={{ width: '16%', padding: 2 }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 13 }}>{item.remark}</Text>
        </View> */}
                    <View style={{ width: '15%', }}>
                        <Text style={{
                            fontSize: switchMerchant ? 10 : 13, fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 5,
                        }}>
                            {item.status}
                        </Text>
                    </View>
                    {/* <View style={{ width: '20%', flexDirection: 'row', }}>
                        <TouchableOpacity onPress={() => {
                            setAddStockTransfer(true);
                            setSelectedWorkOrder(item);
                            console.log(item)
                        }}
                            style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: Colors.fieldtBgColor,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 5,
                                //width: 160,
                                paddingHorizontal: switchMerchant ? 5 : 10,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                                marginRight: 10,
                            }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 13, color: Colors.primaryColor, }}>
                                View
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => {
                                const actionText = item.status === 'REJECTED' ? 'restore' : 'reject';
                                Alert.alert(
                                    `Confirm ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`,
                                    `Are you sure you want to ${actionText} this work order?`,
                                    [
                                        {
                                            text: "Cancel",
                                            style: "cancel"
                                        },
                                        {
                                            text: "OK",
                                            onPress: () => {
                                                // Call the function to update the status of the selected work order
                                                updateStatusToRejected(item.uniqueId, item.status);
                                            }
                                        }
                                    ]
                                );
                            }}
                            style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: Colors.fieldtBgColor,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 5,
                                //width: 160,
                                paddingHorizontal: switchMerchant ? 5 : 10,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                                marginLeft: 10,
                            }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 13, color: item.status === 'REJECTED' ? Colors.primaryColor : "red" }}>
                                {item.status === 'REJECTED' ? 'Undo' : 'Reject'}
                            </Text>
                        </TouchableOpacity>
                    </View> */}
                </View>
            </TouchableOpacity>
        )
    }

    const renderWorkOrderItemList = ({ item, index }) => {
        console.log(item);
        console.log('stop');
        return (
            <View>

                <View
                    key={index}
                    style={{
                        backgroundColor: '#ffffff',
                        flexDirection: 'row',
                        paddingVertical: 20,
                        paddingHorizontal: 10,
                        //paddingBottom: 100,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        borderBottomColor: '#c4c4c4',
                        alignItems: 'center',
                    }}>


                    <View style={{ width: '20%' }}>
                        <View style={{
                            width: '100%',
                            height: 35,
                            backgroundColor: 'white',
                            //alignItems: 'center',
                            justifyContent: 'center',
                            paddingLeft: 0,
                        }}>
                            <Text style={{ width: '100%', color: "black", marginLeft: 0, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                {item.osiName || '-'}
                            </Text>
                        </View>
                    </View>


                    <Text style={{ width: '14%', color: "black", marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                        {item.osiSkuM || '-'}
                    </Text>

                    <Text style={{ width: '9%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                        {item.osiUnit || '-'}
                    </Text>

                    <Text style={{ width: '9%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                        {item.osiQuantity.toFixed(2)}
                    </Text>

                    <Text style={{ width: '11%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                        {Math.abs(item.totalUsage).toFixed(2)}
                    </Text>

                    <Text style={{ width: '13%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                        {(item.osiQuantity + Math.abs(item.totalUsage)).toFixed(2)}
                    </Text>

                    <Text style={{ width: '10%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                        {item.osiPrice.toFixed(2)}
                    </Text>

                    <Text style={{ width: '11%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                        {item.totalOsiP ? item.totalOsiP.toFixed(2) : '0.00'}
                    </Text>

                    {/* <TouchableOpacity
          style={{ marginRight: 20, left: Platform.OS === 'ios' ? 0 : '-10%' }}
          onPress={() => {
            setPoItems([
              ...poItems.slice(0, index),
              ...poItems.slice(index + 1),
            ]);
          }}>
          <Icon name="trash-2" size={switchMerchant ? 15 : 20} color="#eb3446" />
        </TouchableOpacity> */}

                </View>
            </View>
        )
    };

    return (
        <UserIdleWrapper disabled={!isMounted}>
            <View style={[styles.container, !isTablet() ? {
                transform: [
                    { scaleX: 1 },
                    { scaleY: 1 },
                ],
            } : {},
            {
                ...getTransformForScreenInsideNavigation(),
            }]}>
                <View style={[styles.sidebar, !isTablet() ? {
                    width: Dimensions.get('screen').width * 0.08,
                } : {}, switchMerchant ? {
                    // width: '10%'
                } : {},
                {
                    width: windowWidth * 0.08,
                }]}>
                    <SideBar navigation={props.navigation} selectedTab={3} expandInventory={true} />
                </View>
                <ScrollView
                    scrollEnabled={switchMerchant}
                    horizontal={true}
                // contentContainerStyle={{backgroundColor: 'red'}}
                // style={{ backgroundColor: 'red'}}
                >
                    <View style={[styles.content, {
                        padding: 16,
                        width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
                    }]}>

                        <Modal
                            supportedOrientations={['portrait', 'landscape']}
                            style={{
                                // flex: 1
                            }}
                            visible={doModal}
                            transparent={true}
                            animationType={'fade'}
                        >
                            <View style={{
                                flex: 1,
                                backgroundColor: Colors.modalBgColor,
                                alignItems: 'center',
                                justifyContent: 'center',

                                top:
                                    Platform.OS === 'android'
                                        ? 0
                                        : keyboardHeight > 0
                                            ? -keyboardHeight * 0.45
                                            : 0,
                            }}>
                                <View style={{
                                    height: Dimensions.get('screen').width * 0.3,
                                    width: Dimensions.get('screen').width * 0.4,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    padding: Dimensions.get('screen').width * 0.03,
                                    alignItems: 'center',
                                    justifyContent: 'center',

                                    // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                                    ...getTransformForModalInsideNavigation(),
                                }}>
                                    <TouchableOpacity
                                        disabled={isLoading}
                                        style={{
                                            position: 'absolute',
                                            right: Dimensions.get('screen').width * 0.02,
                                            top: Dimensions.get('screen').width * 0.02,

                                            elevation: 1000,
                                            zIndex: 1000,
                                        }}
                                        onPress={() => {
                                            setDoModal(false);
                                        }}>
                                        <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
                                    </TouchableOpacity>
                                    <View style={{
                                        alignItems: 'center',
                                        top: '20%',
                                        position: 'absolute',
                                    }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            textAlign: 'center',
                                            fontSize: switchMerchant ? 18 : 20,
                                        }}>
                                            Download Delivery Order
                                        </Text>
                                    </View>
                                    <View style={{ top: switchMerchant ? '14%' : '10%', }}>
                                        <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                                        <TextInput
                                            underlineColorAndroid={Colors.fieldtBgColor}
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: switchMerchant ? 240 : 370,
                                                height: switchMerchant ? 35 : 50,
                                                borderRadius: 5,
                                                padding: 5,
                                                marginVertical: 5,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                                paddingLeft: 10,
                                                fontSize: switchMerchant ? 10 : 16,
                                            }}
                                            autoCapitalize='none'
                                            placeholderStyle={{ padding: 5 }}
                                            placeholder="Enter your email"
                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                            onChangeText={(text) => {
                                                setExportEmail(text);
                                            }}
                                            value={exportEmail}
                                        />
                                        <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 15 }}>Send As:</Text>


                                        <View style={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            //top: '10%',
                                            flexDirection: 'row',
                                            marginTop: 10,
                                        }}>
                                            <TouchableOpacity
                                                disabled={isLoading}
                                                style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: '#0F1A3C',
                                                    borderRadius: 5,
                                                    width: switchMerchant ? 100 : 120,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,
                                                    marginRight: 15,
                                                }}
                                                onPress={async () => {
                                                    if (exportEmail.length > 0) {
                                                        if (doItem && doItem.uniqueId) {
                                                            CommonStore.update(s => {
                                                                s.isLoading = true;
                                                            });

                                                            var body = {
                                                                emailToSent: exportEmail,
                                                                htmlContent: await exportHtmlToPdf(doItem),
                                                                emailTitle: `KooDoo Stock Transfer DO - ${doItem && doItem.stId ? moment(doItem.createdAt).format('YYYY-MM-DD') + ' - ' + doItem.stId : doItem.stId}.pdf`,
                                                                fileName: `stock-transfer-do-${doItem && doItem.stId ? moment(doItem.createdAt).format('YYYY-MM-DD') + '-' + doItem.stId : doItem.stId}.pdf`,
                                                            };

                                                            ApiClient.POST(API.sendDeliveryOrder, body).then((result) => {
                                                                if (result && result.status === 'success') {
                                                                    Alert.alert('Success', 'Delivery order will be sent to the email address shortly');

                                                                    CommonStore.update(s => {
                                                                        s.isLoading = false;
                                                                    });

                                                                    setDoModal(false);
                                                                }
                                                                else {
                                                                    Alert.alert(
                                                                        'Error',
                                                                        'Failed to send delivery order',
                                                                        [
                                                                            { text: "OK", onPress: () => { } }
                                                                        ],
                                                                        { cancelable: false },
                                                                    );

                                                                    CommonStore.update(s => {
                                                                        s.isLoading = false;
                                                                    });

                                                                    setDoModal(false);
                                                                }
                                                            });
                                                        }
                                                        else {
                                                            Alert.alert('Info', 'Invalid stock transfer to send.');
                                                        }
                                                    }
                                                    else {
                                                        Alert.alert('Info', 'Invalid email address');
                                                    }
                                                }}>
                                                {
                                                    isLoadingExcel
                                                        ?
                                                        <ActivityIndicator
                                                            size={'small'}
                                                            color={Colors.whiteColor}
                                                        />
                                                        :
                                                        <Text style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                            SEND</Text>

                                                }
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </Modal>

                        <Modal
                            supportedOrientations={['portrait', 'landscape']}
                            style={{
                                // flex: 1
                            }}
                            visible={exportModal}
                            transparent={true}
                            animationType={'fade'}
                        >
                            <View style={{
                                flex: 1,
                                backgroundColor: Colors.modalBgColor,
                                alignItems: 'center',
                                justifyContent: 'center',

                                top:
                                    Platform.OS === 'android'
                                        ? 0
                                        : keyboardHeight > 0
                                            ? -keyboardHeight * 0.45
                                            : 0,
                            }}>
                                <View style={{
                                    height: Dimensions.get('screen').width * 0.32,
                                    width: Dimensions.get('screen').width * 0.4,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    padding: Dimensions.get('screen').width * 0.03,
                                    alignItems: 'center',
                                    justifyContent: 'center',

                                    // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                                    ...getTransformForModalInsideNavigation(),
                                }}>
                                    <TouchableOpacity
                                        disabled={isLoading}
                                        style={{
                                            position: 'absolute',
                                            right: Dimensions.get('screen').width * 0.02,
                                            top: Dimensions.get('screen').width * 0.02,

                                            elevation: 1000,
                                            zIndex: 1000,
                                        }}
                                        onPress={() => {
                                            setExportModal(false);

                                            logEventAnalytics({
                                                eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE,
                                                eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE,
                                            })
                                        }}>
                                        <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
                                    </TouchableOpacity>
                                    <View style={{
                                        alignItems: 'center',
                                        top: 20,
                                        position: 'absolute',
                                    }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            textAlign: 'center',
                                            fontSize: switchMerchant ? 18 : 20,
                                        }}>
                                            Download Report
                                        </Text>
                                    </View>
                                    <View style={{ top: switchMerchant ? '14%' : '10%', }}>
                                        <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                                        <TextInput
                                            underlineColorAndroid={Colors.fieldtBgColor}
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: switchMerchant ? 240 : 370,
                                                height: switchMerchant ? 35 : 50,
                                                borderRadius: 5,
                                                padding: 5,
                                                marginVertical: 5,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                                paddingLeft: 10,
                                                fontSize: switchMerchant ? 10 : 16,
                                            }}
                                            autoCapitalize='none'
                                            placeholderStyle={{ padding: 5 }}
                                            placeholder="Enter your email"
                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                            onChangeText={(text) => {
                                                setExportEmail(text);

                                                logEventAnalytics({
                                                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                                                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                                                })
                                            }}
                                            value={exportEmail}
                                        />
                                        <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 15 }}>Send As:</Text>


                                        <View style={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            //top: '10%',
                                            flexDirection: 'row',
                                            marginTop: 10,
                                        }}>
                                            <TouchableOpacity
                                                disabled={isLoading}
                                                style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: '#0F1A3C',
                                                    borderRadius: 5,
                                                    width: switchMerchant ? 100 : 120,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,
                                                    marginRight: 15,
                                                }}
                                                onPress={() => {
                                                    if (exportEmail.length > 0) {
                                                        CommonStore.update(s => {
                                                            s.isLoading = true;
                                                        });
                                                        setIsLoadingExcel(true);
                                                        const excelData = convertDataToExcelFormat();

                                                        if (excelData && excelData.length > 0) {
                                                            generateEmailReport(
                                                                EMAIL_REPORT_TYPE.EXCEL,
                                                                excelData,
                                                                'KooDoo Stock Transfer Report',
                                                                'KooDoo Stock Transfer Report.xlsx',
                                                                `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                                                exportEmail,
                                                                'KooDoo Stock Transfer Report',
                                                                'KooDoo Stock Transfer Report',
                                                                () => {
                                                                    CommonStore.update(s => {
                                                                        s.isLoading = false;
                                                                    });
                                                                    setIsLoadingExcel(false);

                                                                    Alert.alert('Success', 'Report will be sent to the email address shortly');

                                                                    setExportModal(false);
                                                                },
                                                            );
                                                        }
                                                        else {
                                                            Alert.alert('Info', 'Empty data to export.');

                                                            CommonStore.update(s => {
                                                                s.isLoading = false;
                                                            });
                                                            setIsLoadingExcel(false);
                                                        }
                                                    }
                                                    else {
                                                        Alert.alert('Info', 'Invalid email address');
                                                    }

                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL,
                                                    })
                                                }}>
                                                {
                                                    isLoadingExcel
                                                        ?
                                                        <ActivityIndicator
                                                            size={'small'}
                                                            color={Colors.whiteColor}
                                                        />
                                                        :
                                                        <Text style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                            EXCEL</Text>

                                                }
                                            </TouchableOpacity>

                                            <TouchableOpacity
                                                disabled={isLoading}
                                                style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: '#0F1A3C',
                                                    borderRadius: 5,
                                                    width: switchMerchant ? 100 : 120,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,
                                                }}
                                                onPress={() => {
                                                    if (exportEmail.length > 0) {
                                                        CommonStore.update(s => {
                                                            s.isLoading = true;
                                                        });
                                                        setIsLoadingCsv(true);
                                                        const csvData = convertArrayToCSV(stockTransferList);

                                                        generateEmailReport(
                                                            EMAIL_REPORT_TYPE.CSV,
                                                            csvData,
                                                            'KooDoo Stock Transfer Report',
                                                            'KooDoo Stock Transfer Report.csv',
                                                            `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                                            exportEmail,
                                                            'KooDoo Stock Transfer Report',
                                                            'KooDoo Stock Transfer Report',
                                                            () => {
                                                                CommonStore.update(s => {
                                                                    s.isLoading = false;
                                                                });
                                                                setIsLoadingCsv(false);
                                                                Alert.alert('Success', 'Report will be sent to the email address shortly');

                                                                setExportModal(false);
                                                            },
                                                        );
                                                    }
                                                    else {
                                                        Alert.alert('Info', 'Invalid email address');
                                                    }

                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV,
                                                    })
                                                }}>
                                                {
                                                    isLoadingCsv
                                                        ?
                                                        <ActivityIndicator
                                                            size={'small'}
                                                            color={Colors.whiteColor}
                                                        />
                                                        :
                                                        <Text style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                            CSV
                                                        </Text>
                                                }
                                            </TouchableOpacity>
                                        </View>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginTop: 15,
                                            }}>
                                            Download As:
                                        </Text>
                                        <View
                                            style={{
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                marginTop: 10,
                                            }}>
                                            <TouchableOpacity
                                                disabled={isLoading}
                                                style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: '#0F1A3C',
                                                    borderRadius: 5,
                                                    width: switchMerchant ? 100 : 120,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,
                                                    marginRight: 15,
                                                }}
                                                onPress={() => {
                                                    CommonStore.update((s) => {
                                                        s.isLoading = true;
                                                    });
                                                    setIsLoadingLocalExcel(true);
                                                    handleExportExcel();

                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL,
                                                    })
                                                }}>
                                                {isLoadingLocalExcel ? (
                                                    <ActivityIndicator
                                                        size={'small'}
                                                        color={Colors.whiteColor}
                                                    />
                                                ) : (
                                                    <Text
                                                        style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        EXCEL
                                                    </Text>
                                                )}
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                disabled={isLoading}
                                                style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: '#0F1A3C',
                                                    borderRadius: 5,
                                                    width: switchMerchant ? 100 : 120,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,
                                                }}
                                                onPress={() => {
                                                    CommonStore.update((s) => {
                                                        s.isLoading = true;
                                                    });
                                                    setIsLoadingLocalCsv(true);
                                                    handleExportCsv();

                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV,
                                                    })
                                                }}>
                                                {isLoadingLocalCsv ? (
                                                    <ActivityIndicator
                                                        size={'small'}
                                                        color={Colors.whiteColor}
                                                    />
                                                ) : (
                                                    <Text
                                                        style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        CSV
                                                    </Text>
                                                )}
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </Modal>

                        <Modal
                            style={{
                                // flex: 1
                            }}
                            supportedOrientations={['portrait', 'landscape']}
                            visible={importModal}
                            transparent={true}
                            animationType={'fade'}
                        >
                            <View style={styles.modalContainer}>
                                <View style={[styles.modalViewImport, {
                                    top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                                    ...getTransformForModalInsideNavigation(),
                                }]}>
                                    <TouchableOpacity
                                        style={styles.closeButton}
                                        onPress={() => {
                                            // setState({ changeTable: false });
                                            setImportModal(false);
                                        }}>
                                        <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                                    </TouchableOpacity>
                                    <View style={{ padding: 10, margin: 30 }}>
                                        <View style={[styles.modalTitle1, { justifyContent: 'center', alignItems: 'center' }]}>
                                            <Text style={[styles.modalTitleText1, { fontSize: 16, fontWeight: '500' }]}>
                                                Imported List
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <View
                                                style={{
                                                    backgroundColor: Colors.primaryColor,
                                                    width: 150,
                                                    height: 40,
                                                    marginVertical: 15,
                                                    borderRadius: 5,
                                                    alignSelf: 'center',
                                                }}>
                                                <TouchableOpacity onPress={() => { importSelectFile() }}>
                                                    <Text
                                                        style={{
                                                            color: Colors.whiteColor,
                                                            alignSelf: 'center',
                                                            marginVertical: 10,
                                                        }}>
                                                        IMPORT
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View
                                                    style={{
                                                        backgroundColor: Colors.whiteColor,
                                                        width: 150,
                                                        height: 40,
                                                        marginVertical: 15,
                                                        borderRadius: 5,
                                                        alignSelf: 'center',
                                                    }}>
                                                    <TouchableOpacity onPress={() => { setImportTemplate(false) }}>
                                                        <Text
                                                            style={{
                                                                color: Colors.primaryColor,
                                                                alignSelf: 'center',
                                                                marginVertical: 10,
                                                            }}>
                                                            CANCEL
                                                        </Text>
                                                    </TouchableOpacity>
                                                </View>
                                                <View
                                                    style={{
                                                        backgroundColor: Colors.primaryColor,
                                                        width: 150,
                                                        height: 40,
                                                        marginVertical: 15,
                                                        borderRadius: 5,
                                                        alignSelf: 'center',
                                                    }}>
                                                    <TouchableOpacity onPress={() => { }}>
                                                        <Text
                                                            style={{
                                                                color: Colors.whiteColor,
                                                                alignSelf: 'center',
                                                                marginVertical: 10,
                                                            }}>
                                                            SAVE
                                                        </Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </Modal>

                        <DateTimePickerModal
                            isVisible={showDateTimePicker}
                            mode={'date'}
                            onConfirm={(text) => {
                                setRev_date(moment(text).startOf('day'));
                                setShowDateTimePicker(false);
                            }}
                            onCancel={() => {
                                setShowDateTimePicker(false);
                            }}
                            maximumDate={moment(rev_date1).toDate()}
                            date={moment(rev_date).toDate()}
                        />

                        <DateTimePickerModal
                            isVisible={showDateTimePicker1}
                            mode={'date'}
                            onConfirm={(text) => {
                                setRev_date1(moment(text).endOf('day'));
                                setShowDateTimePicker1(false);
                            }}
                            onCancel={() => {
                                setShowDateTimePicker1(false);
                            }}
                            minimumDate={moment(rev_date).toDate()}
                            date={moment(rev_date1).toDate()}
                        />

                        <View
                            style={{
                                flexDirection: 'row',
                                alignSelf: 'center',
                                alignItems: 'center',
                                //backgroundColor: '#ffffff',
                                justifyContent: 'space-between',
                                //padding: 18,
                                marginTop: 5,
                                width: windowWidth * 0.87,
                            }}>
                            <View style={{ width: windowWidth * 0.25 }}>
                                <Text
                                    numberOfLines={3}
                                    style={{
                                        fontSize: switchMerchant ? 20 : 26,
                                        fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    {!showDetails
                                        ? `Work Order List`
                                        : `Work Order Details`}
                                </Text>
                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                }}>

                                <View
                                    style={[
                                        {
                                            // flex: 1,
                                            // alignContent: 'flex-end',
                                            // marginBottom: 10,
                                            // flexDirection: 'row',
                                            // backgroundColor: 'red',
                                            // alignItems: 'flex-end',
                                            height: switchMerchant ? 35 : 40,
                                            //marginTop: 10,
                                        },
                                        !isTablet()
                                            ? {
                                                marginLeft: 0,
                                            }
                                            : {},
                                    ]}>
                                    <View
                                        style={{
                                            width: switchMerchant ? 200 : 250,
                                            height: switchMerchant ? 35 : 40,
                                            backgroundColor: 'white',
                                            borderRadius: 5,
                                            flexDirection: 'row',
                                            alignContent: 'center',
                                            alignItems: 'center',

                                            //marginRight: windowWidth * Styles.sideBarWidth,

                                            // position: 'absolute',
                                            //right: '17%',

                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 3,
                                            borderWidth: 1,
                                            borderColor: '#E5E5E5',
                                        }}>
                                        <Icon
                                            name="search"
                                            size={switchMerchant ? 13 : 18}
                                            color={Colors.primaryColor}
                                            style={{ marginLeft: 15 }}
                                        />
                                        <TextInput
                                            editable={!loading}
                                            underlineColorAndroid={Colors.whiteColor}
                                            style={{
                                                width: switchMerchant ? 180 : 220,
                                                fontSize: switchMerchant ? 10 : 15,
                                                fontFamily: 'NunitoSans-Regular',
                                                paddingLeft: 5,
                                                height: 45,
                                            }}
                                            clearButtonMode="while-editing"
                                            placeholder=" Search"
                                            placeholderTextColor={Platform.select({
                                                ios: '#a9a9a9',
                                            })}
                                            onChangeText={(text) => {
                                                setSearch(text);
                                                // setList1(false);
                                                // setSearchList(true);
                                            }}
                                            value={search}
                                        />
                                    </View>
                                </View>
                            </View>
                        </View>
                        {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on 20 OCT 2020, 1:00PM</Text> */}
                        {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on {moment().format('LLLL')}</Text> */}
                        <View
                            style={{
                                flexDirection: 'row',
                                //backgroundColor: '#ffffff',
                                justifyContent: 'space-between',
                                //padding: 18,
                                marginTop: 20,
                                width: '100%',
                                paddingLeft: switchMerchant
                                    ? 0
                                    : windowWidth <= 1823 && windowWidth >= 1820
                                        ? '1.5%'
                                        : '1%',
                                paddingRight: switchMerchant
                                    ? 0
                                    : windowWidth <= 1823 && windowWidth >= 1820
                                        ? '1.5%'
                                        : '1%',
                            }}>

                            <TouchableOpacity
                                style={[
                                    {
                                        justifyContent: 'center',
                                        flexDirection: 'row',
                                        backgroundColor: '#0F1A3C',
                                        borderRadius: 5,
                                        //width: 160,
                                        paddingHorizontal: 10,
                                        height: switchMerchant ? 30 : 40,
                                        alignItems: 'center',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,

                                        opacity: !showDetails ? 0 : 100,
                                    },
                                ]}
                                onPress={() => {
                                    setShowDetails(false);
                                    setCurrWOItemSort('');
                                    setCurrWOListSort('');
                                }}
                                disabled={!showDetails}>
                                <AntDesign
                                    name="arrowleft"
                                    size={switchMerchant ? 10 : 20}
                                    color={Colors.whiteColor}
                                    style={
                                        {
                                            // top: -1,
                                            //marginRight: -5,
                                        }
                                    }
                                />
                                <Text
                                    style={{
                                        color: Colors.whiteColor,
                                        marginLeft: 5,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                        // marginBottom: Platform.OS === 'ios' ? 0 : 2
                                    }}>
                                    Summary
                                </Text>
                            </TouchableOpacity>

                            <View style={{ flexDirection: 'row' }}>
                                {/* <View
                                    style={[
                                        {
                                            //marginRight: Platform.OS === 'ios' ? 0 : 10,
                                            // paddingLeft: 15,
                                            paddingHorizontal: 15,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            borderRadius: 10,
                                            paddingVertical: 10,
                                            justifyContent: 'center',
                                            backgroundColor: Colors.whiteColor,
                                            shadowOpacity: 0,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                        },
                                    ]}>
                                    <View
                                        style={{ alignSelf: 'center', marginRight: 5 }}
                                        onPress={() => {
                                            setState({
                                                pickerMode: 'date',
                                                showDateTimePicker: true,
                                            });
                                        }}>
                                <GCalendar
                                    width={switchMerchant ? 15 : 20}
                                    height={switchMerchant ? 15 : 20}
                                />
                            </View>

                            <TouchableOpacity
                                onPress={() => {
                                    setShowDateTimePicker(true);
                                    setShowDateTimePicker1(false);
                                }}
                                style={{
                                    marginHorizontal: 4,
                                }}>
                                <Text
                                    style={
                                        switchMerchant
                                            ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                            : { fontFamily: 'NunitoSans-Regular' }
                                    }>
                                    {moment(historyStartDate).format('DD MMM yyyy')}
                                </Text>
                            </TouchableOpacity>

                            <Text
                                style={
                                    switchMerchant
                                        ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                        : { fontFamily: 'NunitoSans-Regular' }
                                }>
                                -
                            </Text>

                            <TouchableOpacity
                                onPress={() => {
                                    setShowDateTimePicker(false);
                                    setShowDateTimePicker1(true);
                                }}
                                style={{
                                    marginHorizontal: 4,
                                }}>
                                <Text
                                    style={
                                        switchMerchant
                                            ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                            : { fontFamily: 'NunitoSans-Regular' }
                                    }>
                                    {moment(historyEndDate).format('DD MMM yyyy')}
                                </Text>
                            </TouchableOpacity>
                        </View> */}
                            </View>
                        </View>
                        <View
                            style={{
                                backgroundColor: Colors.whiteColor,
                                width: windowWidth * 0.87,
                                height:
                                    Platform.OS == 'android'
                                        ? windowHeight * 0.6
                                        : windowHeight * 0.65,
                                marginTop: 10,
                                marginHorizontal: 30,
                                marginBottom: 10,
                                alignSelf: 'center',
                                borderRadius: 5,
                                shadowOpacity: 0,
                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 3,
                            }}>

                            {!showDetails ? (
                                <View style={{ marginTop: 10, flexDirection: 'row' }}>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '5%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <Text
                                                    numberOfLines={1}
                                                    style={{
                                                        fontSize: switchMerchant ? 10 : 13,
                                                        fontFamily: 'NunitoSans-Bold',
                                                        textAlign: 'left',
                                                    }}>
                                                    {'No.\n'}
                                                </Text>
                                                <Text
                                                    style={{
                                                        fontSize: switchMerchant ? 8 : 10,
                                                        color: Colors.descriptionColor,
                                                    }} />
                                            </View>
                                            <View style={{ marginLeft: '3%' }}>
                                                <Entypo
                                                    name="triangle-up"
                                                    size={switchMerchant ? 7 : 14}
                                                    color="transparent" />

                                                <Entypo
                                                    name="triangle-down"
                                                    size={switchMerchant ? 7 : 14}
                                                    color="transparent" />
                                                <Text
                                                    style={{
                                                        fontSize: 10,
                                                        color: Colors.descriptionColor,
                                                    }} />
                                            </View>
                                        </View>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '11%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Variant\nName'}
                                                    </Text>
                                                    {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Variant\nGroup Name'}</Text> */}
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '13%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Variant\nOptions'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '8%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nItem Sold'}</Text> */}
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Item\nSold'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '9%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Sales\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View
                                                    style={{
                                                        marginLeft: '3%',
                                                        justifyContent: 'space-between',
                                                    }}>
                                                    <View>
                                                        <Entypo
                                                            name="triangle-up"
                                                            size={switchMerchant ? 7 : 14}
                                                            color={
                                                                currWOListSort ===
                                                                    REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />

                                                        <Entypo
                                                            name="triangle-down"
                                                            size={switchMerchant ? 7 : 14}
                                                            color={
                                                                currWOListSort ===
                                                                    REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />
                                                    </View>
                                                    {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '7%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Disc\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View
                                                    style={{
                                                        marginLeft: '3%',
                                                        left: Platform.OS === 'ios' ? 0 : '-10%',
                                                    }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '7%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Disc\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        %
                                                    </Text>
                                                </View>
                                                <View
                                                    style={{
                                                        marginLeft: '3%',
                                                        left: Platform.OS === 'ios' ? 0 : '-10%',
                                                    }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '8%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.TAX_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TAX_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TAX_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Tax\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.TAX_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.TAX_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '10%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                            textAlign: 'left',
                                                        }}>
                                                        {'Service\nCharge'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '10%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nSales Return'}</Text> */}
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                            textAlign: 'left',
                                                        }}>
                                                        {'Sales\nReturn'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOListSort ===
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '12%',
                                            //borderRightWidth: 1,
                                            //borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOListSort ===
                                                    REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_ASC
                                                ) {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_DESC,
                                                    );
                                                } else {
                                                    setCurrWOListSort(
                                                        REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Net Sales\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View
                                                    style={{
                                                        marginLeft: '3%',
                                                        justifyContent: 'space-between',
                                                    }}>
                                                    <View>
                                                        <Entypo
                                                            name="triangle-up"
                                                            size={switchMerchant ? 7 : 14}
                                                            color={
                                                                currWOListSort ===
                                                                    REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_ASC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />

                                                        <Entypo
                                                            name="triangle-down"
                                                            size={switchMerchant ? 7 : 14}
                                                            color={
                                                                currWOListSort ===
                                                                    REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_DESC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />
                                                    </View>
                                                    {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}

                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            ) : (
                                <View style={{ marginTop: 10, flexDirection: 'row' }}>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '6%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <Text
                                                    numberOfLines={2}
                                                    style={{
                                                        fontSize: switchMerchant ? 10 : 13,
                                                        fontFamily: 'NunitoSans-Bold',
                                                        textAlign: 'left',
                                                    }}>
                                                    {'No.\n'}
                                                </Text>
                                                <Text
                                                    style={{
                                                        fontSize: switchMerchant ? 8 : 10,
                                                        color: Colors.descriptionColor,
                                                    }} />
                                            </View>
                                            <View style={{ marginLeft: '3%' }}>
                                                <Entypo
                                                    name="triangle-up"
                                                    size={switchMerchant ? 7 : 14}
                                                    color="transparent" />

                                                <Entypo
                                                    name="triangle-down"
                                                    size={switchMerchant ? 7 : 14}
                                                    color="transparent" />
                                                <Text
                                                    style={{
                                                        fontSize: 10,
                                                        color: Colors.descriptionColor,
                                                    }} />
                                            </View>
                                        </View>
                                        {/* <View style={{ marginLeft: '3%' }}>
                                            <TouchableOpacity onPress={() => setcurrWOListSort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC)}>
                                                <Entypo name='triangle-up' size={14} color={currWOListSort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={() => setcurrWOListSort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC)}>
                                                <Entypo name='triangle-down' size={14} color={currWOListSort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>
                                        </View> */}
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '12%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Order ID\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '18%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Transaction\nDate'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '10%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Sales\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View
                                                    style={{
                                                        marginLeft: '3%',
                                                        justifyContent: 'space-between',
                                                    }}>
                                                    <View>
                                                        <Entypo
                                                            name="triangle-up"
                                                            size={switchMerchant ? 7 : 14}
                                                            color={
                                                                currWOItemSort ===
                                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />

                                                        <Entypo
                                                            name="triangle-down"
                                                            size={switchMerchant ? 7 : 14}
                                                            color={
                                                                currWOItemSort ===
                                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />
                                                    </View>
                                                    {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}

                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '8%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Disc\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '8%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Disc\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        %
                                                    </Text>
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '8%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Tax\n'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '9%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Service\nCharge'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>

                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '9%',
                                            borderRightWidth: 1,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Sales\nReturn'}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                        RM
                                                    </Text>
                                                </View>
                                                <View style={{ marginLeft: '3%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={
                                                            currWOItemSort ===
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                                                ? Colors.secondaryColor
                                                                : Colors.descriptionColor
                                                        } />
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }} />
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    {/* <View style={{ flexDirection: 'row', flex: 1, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                        <View style={{ flexDirection: 'column' }}>
                                            <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>GP</Text>
                                            <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                        </View>
                                        <View style={{ marginLeft: '3%' }}>
                                            <TouchableOpacity onPress={() => setCurrWOItemSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC)}>
                                                <Entypo name='triangle-up' size={14} color={currWOItemSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={() => setCurrWOItemSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC)}>
                                                <Entypo name='triangle-down' size={14} color={currWOItemSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </TouchableOpacity>
                                        </View>
                                    </View> */}
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            width: '12%',
                                            borderRightWidth: 0,
                                            borderRightColor: 'lightgrey',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            paddingLeft: 10,
                                        }}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (
                                                    currWOItemSort ===
                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                                                ) {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC,
                                                    );
                                                } else {
                                                    setCurrWOItemSort(
                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC,
                                                    );
                                                }
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                                <View style={{ flexDirection: 'column' }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {'Net Sales\n'}
                                                    </Text>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 8 : 10,
                                                                color: Colors.descriptionColor,
                                                            }}>
                                                            RM
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 8 : 10,
                                                                color: Colors.primaryColor,
                                                            }}>
                                                            {' '}
                                                            *incl tax
                                                        </Text>
                                                    </View>
                                                </View>
                                                <View
                                                    style={{
                                                        marginLeft: '3%',
                                                        justifyContent: 'space-between',
                                                    }}>
                                                    <View>
                                                        <Entypo
                                                            name="triangle-up"
                                                            size={switchMerchant ? 7 : 14}
                                                            color={
                                                                currWOItemSort ===
                                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />

                                                        <Entypo
                                                            name="triangle-down"
                                                            size={switchMerchant ? 7 : 14}
                                                            color={
                                                                currWOItemSort ===
                                                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            } />
                                                    </View>
                                                    {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}

                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            )}

                            {!showDetails ? (
                                <>
                                    {woList.length > 0 ? (
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            // ref={flatListRef}
                                            data={
                                                sortWOList(
                                                    woList,
                                                    currWOListSort,
                                                )}
                                            renderItem={renderWO}
                                            keyExtractor={(item, index) => String(index)}
                                            style={{ marginTop: 10 }}
                                        />
                                    ) : (
                                        <View
                                            style={{
                                                height: windowHeight * 0.4,
                                            }}>
                                            <View
                                                style={{
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    height: '100%',
                                                }}>
                                                <Text style={{ color: Colors.descriptionColor }}>
                                                    - No Data Available -
                                                </Text>
                                            </View>
                                        </View>
                                    )}
                                </>
                            ) : (
                                <>
                                    {selectedWorkOrder && selectedWorkOrder.woItems.length > 0 ? (
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            // ref={flatListRef}
                                            data={selectedWorkOrder.woItems}
                                            // extraData={transactionTypeSales}
                                            renderItem={renderWorkOrderItemList}
                                            keyExtractor={(item, index) => String(index)}
                                            style={{ marginTop: 10 }}
                                        />
                                    ) : (
                                        <View
                                            style={{
                                                height: windowHeight * 0.4,
                                            }}>
                                            <View
                                                style={{
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    height: '100%',
                                                }}>
                                                <Text style={{ color: Colors.descriptionColor }}>
                                                    - No Data Available -
                                                </Text>
                                            </View>
                                        </View>
                                    )}
                                </>
                            )}
                        </View>


                    </View>
                </ScrollView >
            </View >
        </UserIdleWrapper >
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row'
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    listItem: {
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        width: Dimensions.get('screen').width * Styles.sideBarWidth,
        // shadowColor: "#000",
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 16,
        width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
    },
    submitText: {
        height: Platform.OS == 'ios' ? Dimensions.get('screen').height * 0.06 : Dimensions.get('screen').height * 0.05,
        paddingVertical: 5,
        paddingHorizontal: 20,
        flexDirection: 'row',
        color: '#4cd964',
        textAlign: 'center',
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    submitText1: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        flexDirection: 'row',
        color: '#4cd964',
        textAlign: 'center',
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        alignSelf: "flex-end",
        marginRight: 20,
        // marginTop: 15,
        height: 40,
        width: 220,
    },
    submitText2: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        flexDirection: 'row',
        color: '#4cd964',
        textAlign: 'center',
        alignSelf: "flex-end",
        marginRight: 20,
        marginTop: 15
    },
    /* textInput: {
      width: 300,
      height: '10%',
      padding: 20,
      backgroundColor: Colors.fieldtBgColor,
      borderRadius: 5,
      borderRadius: 5,
      paddingTop: 20,
    }, */

    textInput: {
        width: 200,
        height: 40,
        // padding:5,
        backgroundColor: Colors.whiteColor,
        borderRadius: 0,
        marginRight: '35%',
        flexDirection: 'row',
        alignContent: 'center',
        alignItems: 'flex-end',

        shadowOffset: Platform.OS == 'ios' ? {
            width: 0,
            height: 0,
        } : {
            width: 0,
            height: 7,
        },
        shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
        shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
        elevation: 15,
    },
    searchIcon: {
        backgroundColor: Colors.whiteColor,
        height: 40,
        padding: 10,
        shadowOffset: Platform.OS == 'ios' ? {
            width: 0,
            height: 0,
        } : {
            width: 0,
            height: 7,
        },
        shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
        shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

        elevation: 15,
    },
    textInput1: {
        width: 160,
        padding: 5,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        borderRadius: 5,
        paddingTop: 5,
    },
    textInput2: {
        width: 100,
        padding: 5,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        borderRadius: 5,
        paddingTop: 5,
        textAlign: 'center'
    },
    confirmBox: {
        width: 450,
        height: 450,
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    textFieldInput: {
        height: 80,
        width: 900,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        borderRadius: 5,
    },
    circleIcon: {
        width: 30,
        height: 30,
        // resizeMode: 'contain',
        marginRight: 10,
        alignSelf: 'center'
    },
    headerLeftStyle: {
        width: Dimensions.get('screen').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Dimensions.get('screen').width * 0.2,
        width: Dimensions.get('screen').width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('screen').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView1: {
        //height: Dimensions.get('screen').width * 0.2,
        width: Dimensions.get('screen').width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('screen').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalViewImport: {
        height: Dimensions.get('screen').width * 0.6,
        width: Dimensions.get('screen').width * 0.6,
        backgroundColor: Colors.whiteColor,
        borderRadius: Dimensions.get('screen').width * 0.03,
        padding: 20,
        paddingTop: 25,
        //paddingBottom: 30,
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('screen').width * 0.02,
        top: Dimensions.get('screen').width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: 'center',
        top: '20%',
        position: 'absolute',
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'

    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        textAlign: 'center',
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 18,
        color: Colors.fieldtTxtColor
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
        width: "20%",
    },
    modalSaveButton: {
        width: Dimensions.get('screen').width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
});
export default WorkOrderReportScreen;
