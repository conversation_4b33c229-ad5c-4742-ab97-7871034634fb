import { Text } from "react-native-fast-text";
import React, { Component, useEffect, useState, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal as ModalComponent,
  Dimensions,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  useWindowDimensions,
  TouchableWithoutFeedback,
  InteractionManager,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Styles from '../constant/Styles';
import Icon1 from 'react-native-vector-icons/Feather';
import Back from 'react-native-vector-icons/EvilIcons';
import {
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import { CommonStore } from '../store/commonStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import AsyncImage from '../components/asyncImage';
import { CHARGES_TYPE, MODE_ADD_CART, ORDER_TYPE, EXPAND_TAB_TYPE, PRODUCT_PRICE_TYPE, UNIT_TYPE_SHORT, UNIT_TYPE, ORDER_TYPE_SUB, } from '../constant/common';
import Feather from 'react-native-vector-icons/Feather';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import { color } from 'jimp';
import { parseValidIntegerText, parseValidPriceText } from '../util/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';

var quantity2;
var total;
var options;
var refreshAction = null;
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const MenuItemDetailsScreen = React.memo((props) => {
  const { navigation, route } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  //var DismissKeyboard = require('dismissKeyboard'); 

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const { orderType, refresh, orderTypeSub } = route.params;
  const outletDataParam = route.params.outletData;
  const menuItemParam = route.params.menuItem;
  const cartItemParam = route.params.cartItem;

  options = cartItem ? cartItem.options : [];
  quantity2 = cartItem ? cartItem.quantity : quantity;
  // total = cartItem
  //   ? setState({ total: cartItem.price })
  //   : total;
  refreshAction = refresh;

  const [editQuantity, setEditQuantity] = useState(false);
  const [editAddOnQuantity, setEditAddOnQuantity] = useState(false);
  const [menuItem, setMenuItem] = useState(menuItem);
  // const [total, setTotal] = useState(menuItem.price);
  const [total, setTotal] = useState(0);
  const [quantity, setQuantity] = useState('1');
  const [remark, setRemark] = useState(null);
  const [outletData, setOutletData] = useState(outletData);
  const [visible, setVisible] = useState(false);
  const [optional, setOptional] = useState(0);
  const [optional1, setOptional1] = useState(1);
  const [quantity1, setQuantity1] = useState(1);
  const [qty, setQty] = useState(null);
  const [detail, setDetail] = useState([]);
  const [choice, setChoice] = useState(null);
  const [size, setSize] = useState('small');
  const [clicked, setClicked] = useState(1);
  const [clicked1, setClicked1] = useState(0);
  const [clicked2, setClicked2] = useState(0);
  const [expandChoice, setExpandChoice] = useState(false);
  const [cartItem, setCartItem] = useState([]);
  const [cartIcon, setCartIcon] = useState(false);

  const [totalPrice, setTotalPrice] = useState(0);
  const [addOnPrice, setAddOnPrice] = useState(0);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  //////////////////////////////////////////////////////////////////////////////////////

  const [temp, setTemp] = useState('');

  const [variablePrice, setVariablePrice] = useState('0.00');

  //////////////////////////////////////////////////////////////////////////////////////

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const allOutletsItemAddOnChoiceIdDict = CommonStore.useState((s) => s.allOutletsItemAddOnChoiceIdDict);
  const selectedOutlet = MerchantStore.useState((s) => s.allOutlets[0]);
  const outletsItemAddOnDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnDict,
  );
  const outletsItemAddOnChoiceDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnChoiceDict,
  );
  const selectedOutletItem = CommonStore.useState((s) => s.selectedOutletItem);
  const selectedOutletItemAddOn = CommonStore.useState(
    (s) => s.selectedOutletItemAddOn,
  );
  const selectedOutletItemAddOnChoice = CommonStore.useState(
    (s) => s.selectedOutletItemAddOnChoice,
  );

  const selectedOutletItemAddOnOi = CommonStore.useState(s => s.selectedOutletItemAddOnOi);

  const cartItems = CommonStore.useState((s) => s.cartItems);
  const cartItemChoices = CommonStore.useState((s) => s.cartItemChoices);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const onUpdatingCartItem = CommonStore.useState((s) => s.onUpdatingCartItem);
  const modeAddCart = CommonStore.useState((s) => s.modeAddCart);
  const onUpdatingCurrPendingOrder = CommonStore.useState(
    (s) => s.onUpdatingCurrPendingOrder,
  );

  const [newSelectedOutletItemAddOn, setNewSelectedOutletItemAddOn] = useState(
    {},
  );
  const [
    newSelectedOutletItemAddOnDetails,
    setNewSelectedOutletItemAddOnDetails,
  ] = useState({});

  const selectedOutletTable = CommonStore.useState(
    (s) => s.selectedOutletTable,
  );
  const userCart = CommonStore.useState((s) => s.userCart);

  const isOrdering = CommonStore.useState((s) => s.isOrdering);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  //////////////////////////////////////////////////////////////////////////////////////

  const overrideItemPriceSkuDict = CommonStore.useState(
    (s) => s.overrideItemPriceSkuDict,
  );

  const overrideCategoryPriceNameDict = CommonStore.useState(
    (s) => s.overrideCategoryPriceNameDict,
  );

  const selectedOutletItemCategoriesDict = OutletStore.useState(s => s.outletCategoriesDict);

  const [promotionList, setPromotionList] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////////////////////

  const cartItemsTableIdDict = CommonStore.useState(s => s.cartItemsTableIdDict);

  //////////////////////////////////////////////////////////////////////////////////////

  // 2024-12-13 - add on group's min and max choices support

  const selectedAddOnIdForChoiceQtyDict = CommonStore.useState(s => s.selectedAddOnIdForChoiceQtyDict);

  //////////////////////////////////////////////////////////////////////////////////////

  const updateUserCart = async (newCartItems) => {
    const body = {
      userId: userCart.userId,
      outletId: selectedOutlet.uniqueId,
      tableId: selectedOutletTable.uniqueId || '',
      tablePax: selectedOutletTable.seated || 0,
      cartItems: newCartItems,

      waiterId: firebaseUid,
    };

    ApiClient.POST(API.updateUserCart, body).then((result) => {
      if (result && result.status === 'success') {
        // console.log('ok');
      }
    });
  };

  const setState = () => { };

  const [sortedVariantAddOnList, setSortedVariantAddOnList] = useState([]);

  useEffect(() => {
    let sortedVariantAddOnListTemp = [];

    if (selectedOutletItem && selectedOutletItem.uniqueId && outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
      sortedVariantAddOnListTemp = outletsItemAddOnDict[selectedOutletItem.uniqueId].filter((items) => !(items.isHideAddOn === true || items.isHideVariant === true));

      sortedVariantAddOnListTemp = sortedVariantAddOnListTemp.sort((a, b) => {
        return (
          ((a.orderIndex !== undefined)
            ? a.orderIndex
            : sortedVariantAddOnListTemp.length) -
          ((b.orderIndex !== undefined)
            ? b.orderIndex
            : sortedVariantAddOnListTemp.length)
        );
      });
    }

    setSortedVariantAddOnList(sortedVariantAddOnListTemp);
  }, [selectedOutletItem, outletsItemAddOnDict]);

  useEffect(() => {
    var variablePriceTemp = '0.00';

    if (selectedOutletItem &&
      // selectedOutletItem.uniqueId &&
      selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) {
      if (parseFloat(variablePrice) <= 0) {
        variablePriceTemp = selectedOutletItem.price.toFixed(2);
      }
    }

    if (onUpdatingCartItem &&
      // selectedOutletItem.uniqueId &&
      onUpdatingCartItem.priceVariable !== undefined) {
      if (parseFloat(onUpdatingCartItem.priceVariable) >= 0) {
        variablePriceTemp = onUpdatingCartItem.priceVariable.toFixed(2);
      }
    }

    setVariablePrice(variablePriceTemp);
  }, [selectedOutletItem, onUpdatingCartItem]);

  useEffect(() => {
    setNewSelectedOutletItemAddOnDetails({});

    CommonStore.update(s => {
      s.selectedOutletItemAddOn = {};
      s.selectedOutletItemAddOnChoice = {};
    });
  }, [
    selectedOutletItem,
    onUpdatingCartItem,
  ]);

  useEffect(() => {
    if (onUpdatingCartItem) {
      setQuantity(onUpdatingCartItem.quantity.toFixed(0));

      return () => {
        CommonStore.update((s) => {
          s.onUpdatingCartItem = null;
        });
      };
    }
  }, [onUpdatingCartItem]);

  useEffect(() => {
    if (selectedOutletItem && selectedOutletItem.price !== undefined) {
      var extraPrice = 0;
      if (orderType === ORDER_TYPE.DELIVERY &&
        outletData &&
        outletData.deliveryPrice) {
        extraPrice = outletData.deliveryPrice;
      }
      else if (orderType === ORDER_TYPE.PICKUP &&
        orderTypeSub === ORDER_TYPE_SUB.NORMAL &&
        outletData &&
        outletData.pickUpPrice) {
        extraPrice = outletData.pickUpPrice;
      }


      if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = selectedOutletItem.deliveryCharges || 0;

        if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          extraPrice = selectedOutletItem.price * extraPrice / 100;
        }

        if (!selectedOutletItem.deliveryChargesActive) {
          extraPrice = 0;
        }
      }
      // else {
      //   extraPrice = 0;
      // }

      if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
        extraPrice = selectedOutletItem.pickUpCharges || 0;

        if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          extraPrice = selectedOutletItem.price * extraPrice / 100;
        }

        if (!selectedOutletItem.pickUpChargesActive) {
          extraPrice = 0;
        }
      }
      // else {
      //   extraPrice = 0;
      // }

      if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
        extraPrice = selectedOutletItem.otherDCharges || 0;

        if (extraPrice && selectedOutletItem.otherDChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          extraPrice = selectedOutletItem.price * extraPrice / 100;
        }

        if (!selectedOutletItem.otherDChargesActive) {
          extraPrice = 0;
        }
      }

      var overrideCategoryPrice = undefined;
      if (
        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] &&
        overrideCategoryPriceNameDict[
        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name
        ] !== undefined
      ) {
        overrideCategoryPrice =
          overrideCategoryPriceNameDict[
            selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name
          ].overridePrice;
      }

      // setTotalPrice(selectedOutletItem.price);
      if (overrideItemPriceSkuDict[selectedOutletItem.sku] !== undefined) {
        setTotalPrice(overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice);
      } else if (overrideCategoryPrice !== undefined) {
        setTotalPrice(overrideCategoryPrice);
      } else {
        setTotalPrice(extraPrice + selectedOutletItem.price);
      }
    }
  }, [
    selectedOutletItem,
    overrideItemPriceSkuDict,
    overrideCategoryPriceNameDict,
  ]);

  const [addOnMinMaxMessage, setAddOnMinMaxMessage] = useState('');

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {

      const addOnList = Object.entries(selectedOutletItemAddOn).map(
        ([key, value]) => ({ key, value }),
      );

      var addOnPriceTemp = 0;

      for (var i = 0; i < addOnList.length; i++) {
        if (addOnList[i].value) {
          const addOnChoiceList = Object.entries(addOnList[i].value).map(
            ([key, value]) => ({ key, value }),
          );

          for (var j = 0; j < addOnChoiceList.length; j++) {
            // // console.log(addOnChoiceList[j].value);
            // // console.log(outletsItemAddOnChoiceDict[addOnList[i].key]);
            // // console.log(outletsItemAddOnChoiceDict[addOnList[i].key][addOnChoiceList[j].key]);

            if (addOnChoiceList[j].value) {
              const actualAddOnChoiceList =
                outletsItemAddOnChoiceDict[addOnList[i].key];

              if (actualAddOnChoiceList && actualAddOnChoiceList.length > 0) {
                for (var k = 0; k < actualAddOnChoiceList.length; k++) {
                  if (
                    addOnChoiceList[j].key === actualAddOnChoiceList[k].uniqueId
                  ) {
                    // add this addon price

                    addOnPriceTemp += actualAddOnChoiceList[k].price;
                  }
                }
              }
            }
          }
        }
      }

      //////////////////////////////////////////

      // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
      // // const newSelectedOutletItemAddOnDetailsList = Object.entries(newSelectedOutletItemAddOnDetails).map(([key, value]) => ({ key: key, value: value }));

      // for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
      //   if (newSelectedOutletItemAddOnList[i].value) {
      //     const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

      //     if (addOnTemp) {
      //       addOnPriceTemp += addOnTemp.quantity * addOnTemp.price;
      //     }
      //   }
      // }

      var addOnMinMaxMessageTemp = '';
      let selectedAddOnIdForChoiceQtyDictTemp = {};

      // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
      const newSelectedOutletItemAddOnList = Object.entries(
        newSelectedOutletItemAddOnDetails,
      ).map(([key, value]) => ({ key, value }));

      for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
        if (newSelectedOutletItemAddOnList[i].value) {
          const addOnTemp =
            newSelectedOutletItemAddOnDetails[
            newSelectedOutletItemAddOnList[i].key
            ];

          if (addOnTemp) {
            addOnPriceTemp += addOnTemp.quantity * addOnTemp.price;

            if (selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId]) {
              selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId].countedQty += addOnTemp.quantity;
            }
            else {
              selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId] = {
                ...addOnTemp,

                countedQty: addOnTemp.quantity,
              };
            }

            ///////////////////////////////

            // checks addOn minSelect and maxSelect

            if (addOnTemp.quantity < addOnTemp.minSelect || addOnTemp.quantity > addOnTemp.maxSelect) {
              addOnMinMaxMessageTemp += `${addOnTemp.choiceName}'s quantity must within ${addOnTemp.minSelect} ~ ${addOnTemp.maxSelect}\n`;
            }

            ///////////////////////////////
          }
        }
      }

      //////////////////////////////////////////

      const selectedAddOnIdForChoiceQtyDictTempList = Object.entries(selectedAddOnIdForChoiceQtyDictTemp).map(([key, value]) => {
        return {
          key: key,
          ...value,
        };
      });

      for (let i = 0; i < selectedAddOnIdForChoiceQtyDictTempList.length; i++) {
        const addOnIdForChoice = selectedAddOnIdForChoiceQtyDictTempList[i];

        if (addOnIdForChoice.addOnMin && addOnIdForChoice.addOnMax) {
          if (addOnIdForChoice.countedQty < addOnIdForChoice.addOnMin || addOnIdForChoice.countedQty > addOnIdForChoice.addOnMax) {
            addOnMinMaxMessageTemp += `${addOnIdForChoice.addOnName}'s choices quantities must within ${addOnIdForChoice.min} ~ ${addOnIdForChoice.max}\n`;
          }
        }
      }

      //////////////////////////////////////////

      setAddOnMinMaxMessage(addOnMinMaxMessageTemp);

      //////////////////////////////////////////

      setAddOnPrice(addOnPriceTemp);

      CommonStore.update(s => {
        s.selectedAddOnIdForChoiceQtyDict = selectedAddOnIdForChoiceQtyDictTemp;
      });
    });
  }, [
    selectedOutletItemAddOn,
    newSelectedOutletItemAddOn,
    newSelectedOutletItemAddOnDetails,
  ]);

  // auto init the selected addon dict with 0, then later can check

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var newSelectedOutletItemAddOnDetailsAppend = {};

      if (
        selectedOutletItem &&
        outletsItemAddOnDict[selectedOutletItem.uniqueId]
      ) {
        var addOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId].filter(item => item.minSelect === undefined && item.maxSelect === undefined);

        if (addOnList.length > 0) {
          for (var i = 0; i < addOnList.length; i++) {
            var addOn = addOnList[i];

            if (
              addOn &&
              outletsItemAddOnChoiceDict[addOn.uniqueId]
            ) {
              var addOnChoiceList = outletsItemAddOnChoiceDict[addOn.uniqueId];

              for (var j = 0; j < addOnChoiceList.length; j++) {
                var addOnChoice = addOnChoiceList[j];

                if (!newSelectedOutletItemAddOnDetails[addOnChoice.uniqueId]) {
                  // means not exist

                  newSelectedOutletItemAddOnDetailsAppend[addOnChoice.uniqueId] = {
                    quantity: 0,
                    price: addOnChoice.price,

                    outletItemAddOnChoiceId: addOnChoice.uniqueId,
                    outletItemAddOnId: addOn.uniqueId,

                    choiceName: addOnChoice.name,
                    addOnName: addOn.name,

                    minSelect: addOnChoice.minSelect,
                    maxSelect: addOnChoice.maxSelect,

                    oi: addOn.orderIndex !== undefined ? addOn.orderIndex : 0,

                    pal: addOn.pal !== undefined ? addOn.pal : null,

                    ...addOn.min && {
                      addOnMin: addOn.min,
                    },
                    ...addOn.max && {
                      addOnMax: addOn.max,
                    },
                  }
                }
              }
            }
          }
        }
      }

      setNewSelectedOutletItemAddOnDetails({
        ...newSelectedOutletItemAddOnDetails,
        ...newSelectedOutletItemAddOnDetailsAppend,
      });

      //////////////////////////////////////////

      // 2022-11-24 - Help to choose variant by default

      var selectedOutletItemAddOnTemp = {};
      var selectedOutletItemAddOnChoiceTemp = {};

      if (menuItem && outletsItemAddOnDict[menuItem.uniqueId]) {
        var variantList = outletsItemAddOnDict[menuItem.uniqueId].filter(
          (item) =>
            item.minSelect !== undefined && item.maxSelect !== undefined,
        );

        for (let variantIndex = 0; variantIndex < variantList.length; variantIndex++) {
          var variant = variantList[variantIndex];

          var selectedOutletItemAddOnChoiceDictToAdd = {};

          if (variant && outletsItemAddOnChoiceDict[variant.uniqueId]) {
            var variantChoiceList = outletsItemAddOnChoiceDict[variant.uniqueId];

            var selectedChoiceNum = 0;

            for (var j = 0; j < variantChoiceList.length; j++) {
              if (selectedChoiceNum < variant.minSelect || selectedChoiceNum < variant.maxSelect) {
                var variantChoice = variantChoiceList[j];

                selectedOutletItemAddOnChoiceTemp[variantChoice.uniqueId] = true;
                selectedChoiceNum++;

                selectedOutletItemAddOnChoiceDictToAdd[variantChoice.uniqueId] = true;
              }
              else {
                break;
              }
            }
          }

          selectedOutletItemAddOnTemp[variant.uniqueId] = selectedOutletItemAddOnChoiceDictToAdd;
        }
      }

      CommonStore.update(s => {
        s.selectedOutletItemAddOnChoice = selectedOutletItemAddOnChoiceTemp;
        s.selectedOutletItemAddOn = selectedOutletItemAddOnTemp;
      });

      //////////////////////////////////////////
    });
  }, [
    selectedOutletItem,
    outletsItemAddOnDict,
    outletsItemAddOnChoiceDict,
  ]);

  const addToCartFunction = async () => {
    // save();
    // setState({ optional: 0 });
    // //// console.log(optional);
    // addToCart(),
    //   props.navigation.goBack();

    // console.log('pressed order');

    if (!global.isAddingToCart) {
      global.isAddingToCart = true;

      requestAnimationFrame(async () => {
        if (addOnVerified && addOnMinMaxMessage.length <= 0 && !isLoading) {
          // console.log('isOrdering ' + isOrdering);
          if (!isOrdering) {
            // console.log('able to order');

            CommonStore.update((s) => {
              s.isOrdering = true;
              s.isLoading = true;
            });

            const cartItemDate = Date.now();

            var newCartItems = [];

            var isGotLowStock = false;

            if (selectedOutletItem) {
              var tempCartItemChoices = {};

              // if (cartItemChoices[selectedOutletItem.uniqueId]) {
              //   // means got previous choices for this outlet item, continue to accumulate

              //   tempCartItemChoices = {
              //     ...cartItemChoices[selectedOutletItem.uniqueId],
              //   };
              // }

              if (outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
                let tempOutletsItemAddOnList =
                  [...outletsItemAddOnDict[selectedOutletItem.uniqueId]];

                tempOutletsItemAddOnList.sort((a, b) => {
                  return (
                    ((a.orderIndex !== undefined)
                      ? a.orderIndex
                      : tempOutletsItemAddOnList.length) -
                    ((b.orderIndex !== undefined)
                      ? b.orderIndex
                      : tempOutletsItemAddOnList.length)
                  );
                })

                for (
                  var i = 0;
                  i < tempOutletsItemAddOnList.length;
                  i++
                ) {
                  // check against the default item add on list

                  const tempAddOn = tempOutletsItemAddOnList[i];

                  if (
                    selectedOutletItemAddOn[tempAddOn.uniqueId] !==
                    undefined
                  ) {
                    // means this addon got choices selected before (could also means deselected, so need checks further as shown below)

                    const tempAddOnSelectedObjList = Object.entries(
                      selectedOutletItemAddOn[tempAddOn.uniqueId],
                    ).map(([key, value]) => ({ key, value }));

                    for (
                      var j = 0;
                      j < tempAddOnSelectedObjList.length;
                      j++
                    ) {
                      if (tempAddOnSelectedObjList[j].value === true) {
                        // means this addon's choice is selected

                        tempCartItemChoices[
                          tempAddOnSelectedObjList[j].key
                        ] = true;
                      } else {
                        tempCartItemChoices[
                          tempAddOnSelectedObjList[j].key
                        ] = false;
                      }
                    }
                  }
                }
              }

              ///////////////////////////////////////////////////

              // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));

              // var addOnGroupList = [];

              // for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
              //   if (newSelectedOutletItemAddOnList[i].value) {
              //     const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

              //     if (addOnTemp) {
              //       addOnGroupList.push(addOnTemp);
              //     }
              //   }
              // }

              const newSelectedOutletItemAddOnList = Object.entries(
                newSelectedOutletItemAddOnDetails,
              ).map(([key, value]) => ({ key, value })).sort((a, b) => a.value.choiceName.localeCompare(b.value.choiceName));

              var addOnGroupList = [];

              for (
                var i = 0;
                i < newSelectedOutletItemAddOnList.length;
                i++
              ) {
                if (newSelectedOutletItemAddOnList[i].value &&
                  newSelectedOutletItemAddOnList[i].value.quantity > 0) {
                  const addOnTemp =
                    newSelectedOutletItemAddOnDetails[
                    newSelectedOutletItemAddOnList[i].key
                    ];

                  if (addOnTemp) {
                    addOnGroupList.push(addOnTemp);
                  }
                }
              }

              // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
              // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOnDetails).map(([key, value]) => ({ key: key, value: value }));

              // for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
              //   if (newSelectedOutletItemAddOnList[i].value) {
              //     const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

              //     if (addOnTemp) {
              //       addOnPriceTemp += addOnTemp.quantity * addOnTemp.price;
              //     }
              //   }
              // }

              ///////////////////////////////////////////////////

              // check choiceStockCount                      

              const tempCartItemChoicesList = Object.entries(tempCartItemChoices).map(
                ([key, value]) => ({ key, value }),
              );

              for (var i = 0; i < tempCartItemChoicesList.length; i++) {
                if (tempCartItemChoicesList[i].value &&
                  allOutletsItemAddOnChoiceIdDict[tempCartItemChoicesList[i].key]) {

                  // if (allOutletsItemAddOnChoiceIdDict[tempCartItemChoicesList[i].key].choiceStockCount === undefined ||
                  //   allOutletsItemAddOnChoiceIdDict[tempCartItemChoicesList[i].key].choiceStockCount <= 0) {
                  //   isGotLowStock = true;
                  //   break;
                  // }
                }
              }

              /////////////////////////////////////

              const addToCartFunc = () => {
                if (onUpdatingCartItem) {
                  // update existing cart item

                  var cartItemsToUpdated = [];
                  if (
                    modeAddCart === MODE_ADD_CART.PAYMENT_SUMMARY_EDIT
                  ) {
                    cartItemsToUpdated =
                      onUpdatingCurrPendingOrder.cartItems;
                  } else {
                    cartItemsToUpdated = cartItems;
                  }

                  var updateCartItemIndex = 0;

                  for (var i = 0; i < cartItemsToUpdated.length; i++) {
                    if (
                      cartItemsToUpdated[i].itemId ===
                      onUpdatingCartItem.itemId &&
                      cartItemsToUpdated[i].cartItemDate ===
                      onUpdatingCartItem.cartItemDate
                    ) {
                      updateCartItemIndex = i;
                    }
                  }

                  newCartItems = [];

                  var quantityParsed = !isNaN(parseInt(quantity)) ? parseInt(quantity) : 0;

                  if (quantityParsed <= 0) {
                    newCartItems = [
                      ...cartItemsToUpdated.slice(0, updateCartItemIndex),
                      ...cartItemsToUpdated.slice(
                        updateCartItemIndex + 1,
                      ),
                    ];
                  } else {
                    newCartItems = [
                      ...cartItemsToUpdated.slice(0, updateCartItemIndex),
                      {
                        priceTemp: (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * quantity),

                        itemId: selectedOutletItem.uniqueId,
                        choices: tempCartItemChoices,
                        remarks: remark,
                        fireOrder: false,
                        quantity: quantityParsed,
                        cartItemDate, // hmm need update or use existing?

                        weight: (selectedOutletItem.weight ? selectedOutletItem.weight : 0.1) * quantityParsed,

                        addOnGroupList,

                        itemSku: selectedOutletItem.sku,
                        categoryId: selectedOutletItem.categoryId,

                        printerAreaList: selectedOutletItem.printerAreaList || [],
                        printingTypeList: selectedOutletItem.printingTypeList || null,

                        isDocket: selectedOutletItem.isDocket || false,
                        printDocketQuantity: selectedOutletItem.printDocketQuantity || 1,

                        ...(selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE && {
                          priceVariable: parseFloat(variablePrice),
                        }),

                        ...((selectedOutletItem.bc !== undefined && selectedOutletItem.bc !== '') && {
                          bc: selectedOutletItem.bc,
                        }),

                        priceType: selectedOutletItem.priceType ? selectedOutletItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                        unitType: selectedOutletItem.unitType ? selectedOutletItem.unitType : UNIT_TYPE.GRAM,

                        itemCostPrice: selectedOutletItem.itemCostPrice ? selectedOutletItem.itemCostPrice : 0,

                        ...selectedOutletItem.upsellingCampaignId && {
                          priceUpselling: selectedOutletItem.priceUpselling,
                          upsellingCampaignId: selectedOutletItem.upsellingCampaignId,

                          upc: selectedOutletItem.upc,
                        },

                        ...selectedOutletItem.lpId && {
                          lpId: selectedOutletItem.lpId,
                          lplId: selectedOutletItem.lplId,
                          lpP: selectedOutletItem.lpP,
                          lpPO: selectedOutletItem.lpPO,
                          lpPVA: selectedOutletItem.lpPVA,
                          lpPOVA: selectedOutletItem.lpPOVA,
                        },
                      },
                      ...cartItemsToUpdated.slice(
                        updateCartItemIndex + 1,
                      ),
                    ];
                  }

                  if (modeAddCart === MODE_ADD_CART.NORMAL) {

                    // if (newCartItems.length > 0) {
                    //   await AsyncStorage.setItem(
                    //     `${firebaseUid}.cartItems`,
                    //     JSON.stringify(newCartItems),
                    //   );
                    // } else {
                    //   await AsyncStorage.removeItem(
                    //     `${firebaseUid}.cartItems`,
                    //   );
                    // }

                    // speed up
                    if (newCartItems.length > 0) {
                      // AsyncStorage.setItem(
                      //   `${firebaseUid}.cartItems`,
                      //   JSON.stringify(newCartItems),
                      // );
                    } else {
                      // AsyncStorage.removeItem(
                      //   `${firebaseUid}.cartItems`,
                      // );
                    }

                    CommonStore.update((s) => {
                      // s.cartItemChoices = {
                      //   ...cartItemChoices,
                      //   [selectedOutletItem.uniqueId]: {
                      //     ...tempCartItemChoices,
                      //   },
                      // };

                      // s.cartItems = new Set(cartItems).add(selectedOutletItem.uniqueId);
                      s.cartItems = newCartItems;
                    });

                    if (selectedOutletTable && selectedOutletTable.uniqueId) {
                      CommonStore.update((s) => {
                        s.cartItemsTableIdDict = {
                          ...cartItemsTableIdDict,
                          [selectedOutletTable.uniqueId]: newCartItems,
                        };
                      });
                    }
                  }
                } else {
                  // add to cart


                  var quantityParsed = !isNaN(parseInt(quantity)) ? parseInt(quantity) : 0;

                  if (quantityParsed <= 0) {
                    Alert.alert('Error', 'Please add one or more items');

                    global.isAddingToCart = false;

                    return;
                  }

                  var cartItemsToUpdated = [];
                  if (modeAddCart === MODE_ADD_CART.PAYMENT_SUMMARY_ADD) {

                    cartItemsToUpdated =
                      onUpdatingCurrPendingOrder.cartItems;
                  } else {
                    cartItemsToUpdated = cartItems;
                  }

                  newCartItems = [
                    ...cartItemsToUpdated,
                    {
                      priceTemp: (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * quantity),

                      itemId: selectedOutletItem.uniqueId,
                      choices: tempCartItemChoices,
                      remarks: remark,
                      fireOrder: false,
                      quantity: quantityParsed,
                      cartItemDate,

                      weight: (selectedOutletItem.weight ? selectedOutletItem.weight : 0.1) * quantityParsed,

                      addOnGroupList,

                      itemSku: selectedOutletItem.sku,
                      categoryId: selectedOutletItem.categoryId,

                      printerAreaList: selectedOutletItem.printerAreaList || [],
                      printingTypeList: selectedOutletItem.printingTypeList || null,

                      isDocket: selectedOutletItem.isDocket || false,
                      printDocketQuantity: selectedOutletItem.printDocketQuantity || 1,

                      ...(selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE && {
                        priceVariable: parseFloat(variablePrice),
                      }),

                      ...((selectedOutletItem.bc !== undefined && selectedOutletItem.bc !== '') && {
                        bc: selectedOutletItem.bc,
                      }),

                      priceType: selectedOutletItem.priceType ? selectedOutletItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                      unitType: selectedOutletItem.unitType ? selectedOutletItem.unitType : UNIT_TYPE.GRAM,

                      itemCostPrice: selectedOutletItem.itemCostPrice ? selectedOutletItem.itemCostPrice : 0,

                      ...selectedOutletItem.upsellingCampaignId && {
                        priceUpselling: selectedOutletItem.priceUpselling,
                        upsellingCampaignId: selectedOutletItem.upsellingCampaignId,

                        upc: selectedOutletItem.upc,
                      },

                      ...selectedOutletItem.lpId && {
                        lpId: selectedOutletItem.lpId,
                        lplId: selectedOutletItem.lplId,
                        lpP: selectedOutletItem.lpP,
                        lpPO: selectedOutletItem.lpPO,
                        lpPVA: selectedOutletItem.lpPVA,
                        lpPOVA: selectedOutletItem.lpPOVA,
                      },
                    },
                  ];

                  if (modeAddCart === MODE_ADD_CART.NORMAL) {
                    // await AsyncStorage.setItem(
                    //   `${firebaseUid}.cartItems`,
                    //   JSON.stringify(newCartItems),
                    // );

                    // speed up
                    // AsyncStorage.setItem(
                    //   `${firebaseUid}.cartItems`,
                    //   JSON.stringify(newCartItems),
                    // );

                    CommonStore.update((s) => {
                      s.cartItemChoices = {
                        ...cartItemChoices,
                        [selectedOutletItem.uniqueId]: {
                          ...tempCartItemChoices,
                        },
                      };

                      // s.cartItems = new Set(cartItems).add(selectedOutletItem.uniqueId);
                      s.cartItems = newCartItems;
                    });

                    if (selectedOutletTable && selectedOutletTable.uniqueId) {
                      CommonStore.update((s) => {
                        s.cartItemsTableIdDict = {
                          ...cartItemsTableIdDict,
                          [selectedOutletTable.uniqueId]: newCartItems,
                        };
                      });
                    }
                  }
                }

                if (modeAddCart === MODE_ADD_CART.NORMAL) {
                  // disable user cart for now

                  // if (userCart.userId) {
                  //   await updateUserCart(newCartItems);
                  // }

                  navigation.goBack();
                } else if (
                  modeAddCart === MODE_ADD_CART.PAYMENT_SUMMARY_EDIT ||
                  modeAddCart === MODE_ADD_CART.PAYMENT_SUMMARY_ADD
                ) {
                  CommonStore.update((s) => {
                    s.onUpdatingCurrPendingOrder = {
                      ...onUpdatingCurrPendingOrder,
                      cartItems: newCartItems,
                    };
                  });

                  navigation.navigate('Table');
                }

                setTimeout(() => {
                  CommonStore.update((s) => {
                    s.isOrdering = false;
                    s.isLoading = false;
                  });
                }, 1000);
              };

              /////////////////////////////////////

              if (isGotLowStock) {
                Alert.alert('Info', 'One of the variant or addon stock is running low, are you sure you want to proceed?', [
                  {
                    text: 'Yes',
                    onPress: () => {
                      addToCartFunc();
                    },
                  },

                  {
                    text: 'No',
                    onPress: () => {
                      CommonStore.update((s) => {
                        s.isOrdering = false;
                        s.isLoading = false;
                      });
                    },
                  },
                ]);
              }

              ///////////////////////////////////////////////////  
              if (!isGotLowStock) {
                if (modeAddCart === MODE_ADD_CART.NORMAL) {
                  // disable user cart for now

                  // if (userCart.userId) {
                  //   await updateUserCart(newCartItems);
                  // }

                  addToCartFunc();
                  // // console.log('asd')

                  // navigation.goBack();
                } else if (
                  modeAddCart === MODE_ADD_CART.PAYMENT_SUMMARY_EDIT ||
                  modeAddCart === MODE_ADD_CART.PAYMENT_SUMMARY_ADD
                ) {
                  addToCartFunc();

                  CommonStore.update((s) => {
                    s.onUpdatingCurrPendingOrder = {
                      ...onUpdatingCurrPendingOrder,
                      cartItems: newCartItems,
                    };
                  });

                  navigation.navigate('Table');
                }

                setTimeout(() => {
                  CommonStore.update((s) => {
                    s.isOrdering = false;
                    s.isLoading = false;
                  });
                }, 1000);
              }
            }


          }
        } else {
          // only show popup if havent choose addon, if tap fast multiple times dont show popup

          if (!addOnVerified) {
            Alert.alert(
              'Info',
              'Please select your choice before proceeding',
            );
          }

          if (addOnMinMaxMessage.length > 0) {
            Alert.alert(
              'Info',
              addOnMinMaxMessage,
            );
          }
        }

        global.isAddingToCart = false;
      });
    }

    // if (onUpdatingCartItem === null && quantity > 0) {

    // }
    // else {
    //   Alert.alert('Error', 'Please add one or more item');
    // }
  };


  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={{
          width: windowWidth * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Menu Item Details
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount() {
  //   ApiClient.GET(API.getItemAddOnChoice + menuItem.id).then(
  //     (result) => {
  //       setState({ menuItemDetails: result });
  //     }
  //   );
  //   setInterval(() => {
  //     cartCount();
  //   }, 5000);
  //   cartCount();
  // }

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const [addOnVerified, setAddOnVerified] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      InteractionManager.runAfterInteractions(() => {
        if (selectedOutletItem && selectedOutletItem.uniqueId) {
          const addOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId]
            ? outletsItemAddOnDict[selectedOutletItem.uniqueId].filter(
              (item) => !(item.isHideAddOn === true || item.isHideVariant === true)
            )
            : [];

          if (addOnList && addOnList.length > 0) {
            // got addons

            var resultList = [];

            for (var i = 0; i < addOnList.length; i++) {
              if (
                addOnList[i].minSelect !== undefined &&
                addOnList[i].maxSelect !== undefined
              ) {
                var result = false;

                const addOnId = addOnList[i].uniqueId;
                const { minSelect } = addOnList[i];
                const { maxSelect } = addOnList[i];

                if (minSelect === 0) {
                  result = true;
                } else if (selectedOutletItemAddOn[addOnId]) {
                  const selectedOutletItemAddOnValueList = Object.entries(
                    selectedOutletItemAddOn[addOnId],
                  ).map(([key, value]) => value);

                  const selectedCount = selectedOutletItemAddOnValueList.filter(
                    (value) => value === true,
                  ).length;

                  if (selectedCount >= minSelect && selectedCount <= maxSelect) {
                    result = true;
                  }
                }

                resultList.push(result);
              } else {
                resultList.push(true);
              }
            }

            var addOnVerifiedTemp = resultList.filter((result) => result === false).length === 0;

            setAddOnVerified(addOnVerifiedTemp);

            // setAddOnVerified(
            //   resultList.filter((result) => result === false).length === 0,
            // );
          } else {
            setAddOnVerified(true);
          }
        }
      });
    }
  }, [
    isLoading,
    selectedOutletItem,
    outletsItemAddOnDict,
    outletsItemAddOnChoiceDict,
    selectedOutletItemAddOn,
  ]);

  const expandChoiceFunc = (param) => {
    if (expandChoice == false) {
      return setState({ expandChoice: true }), (param.expandChoice = true);
    } else {
      return setState({ expandChoice: false }), (param.expandChoice = false);
    }
  };

  const cartCount = () => {
    if (Cart.getCartItem().length > 0) {
      setState({ cartIcon: true });
    } else {
      setState({ cartIcon: false });
    }
  };

  const changeClick = () => {
    if (clicked == 1) {
      setState({ clicked: 0 });
    } else {
      setState({ clicked: 1, clicked1: 0, clicked2: 0, size: 'small' });
    }
  };

  const changeClick1 = () => {
    if (clicked1 == 1) {
      setState({ clicked1: 0 });
    } else {
      setState({ clicked1: 1, clicked: 0, clicked2: 0, size: 'medium' });
    }
  };
  const changeClick2 = () => {
    if (clicked2 == 1) {
      setState({ clicked2: 0 });
    } else {
      setState({ clicked2: 1, clicked: 0, clicked1: 0, size: 'big' });
    }
  };

  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() });
  };

  // function here
  const addToCart = () => {
    Cart.setTax(5);
    if (Cart.getOutletId() != outletData.id) {
      Cart.setOutletId(outletData.id);
      Cart.clearCart();
    }
    options.forEach((element, index) => {
      if (element.quantity == 0) options.splice(index, 1);
    });
    var data = {
      itemId: menuItem.id,
      quantity,
      remarks: remark,
      options,
      image: menuItem.image,
      name: menuItem.name,
      price: parseFloat(totalState),
      fireOrder: optional,
      menuItem,
      size,
    };
    Cart.setCartItem(data);
    Cart.setOutletId(outletData.id);
    setState({ refresh: true });
    refreshAction();
    Cart.getRefreshCartPage();
  };

  const showPrice = () => {
    if (price == false) {
      setState({ price: true });
    } else {
      setState({ price: false });
    }
  };

  const addToCart1 = () => {
    Cart.setTax(5);
    if (Cart.getOutletId() != outletData.id) {
      Cart.setOutletId(outletData.id);
      Cart.clearCart();
    }
    options.forEach((element, index) => {
      if (element.quantity == 0) options.splice(index, 1);
    });
    var data = {
      itemId: menuItem.id,
      quantity,
      remark,
      options,
      image: menuItem.image,
      name: menuItem.name,
      price: parseFloat(totalState),
      fireOrder: optional1,
      menuItem,
    };
    Cart.setCartItem(data);
    Cart.setOutletId(outletData.id);
    setState({ refresh: true });
    refreshAction();
    Cart.getRefreshCartPage();
  };

  const containsObject = (id) => {
    var i;
    for (i = 0; i < options.length; i++) {
      if (options[i].choice === id) {
        return true;
      }
    }
    return false;
  };

  const addLeastItem = (name, choiceId, price, cat) => {
    var i;
    var total = 0;
    var prevChoice = 0;
    for (i = 0; i < options.length; i++) {
      if (options[i].least == true && options[i].cat == cat) {
        prevChoice = options[i].choice;
        total = parseFloat(totalState) - options[i].price * quantity1;
        options.splice(i, 1);
      }
    }
    if (prevChoice != choiceId) {
      var choice = {
        choice: choiceId,
        quantity1: 1,
        itemName: name,
        least: true,
        cat,
        price,
      };
      // setState({
      //   total:
      //     (total != 0 ? total : parseFloat(total)) +
      //     parseFloat(price) * quantity1,
      // });
      setTotalState(
        (total != 0 ? total : parseFloat(totalState)) +
        parseFloat(price) * quantity1,
      );
      options.push(choice);
    } else {
      // setState({ total: total })
      setTotalState(total);
    }
    setState({ refresh: true });
  };

  const addOption = (name, quantity, choiceId, price) => {
    if (quantity > 0) {
      if (!containsObject(choiceId, options)) {
        var choice = {
          choice: choiceId,
          quantity: 1,
          itemName: `Add ${name}`,
          least: false,
          price,
        };
        // setState({
        //   total: parseFloat(totalState) + parseFloat(price),
        // });
        setState(parseFloat(totalState) + parseFloat(price));
        options.push(choice);
      } else {
        var i;
        for (i = 0; i < options.length; i++) {
          if (options[i].choice === choiceId) {
            options[i].quantity = options[i].quantity + 1;
            // setState({
            //   total: parseFloat(total) + parseFloat(price),
            // });
            setState(parseFloat(totalState) + parseFloat(price));
          }
        }
      }
    } else {
      var i;
      for (i = 0; i < options.length; i++) {
        if (options[i].choice === choiceId) {
          if (options[i].quantity > 0) {
            options[i].quantity = options[i].quantity + quantity;
            if (options[i] - 1 == 0) {
              options.splice(i, 1);
            }
            // setState({
            //   total: parseFloat(total) - parseFloat(price),
            // });
            setState(parseFloat(totalState) + parseFloat(price));
          } else {
            options.splice(i, 1);
          }
        }
      }
    }
  };

  const getQuantity = (itemId) => {
    var i;
    for (i = 0; i < cartItem.length; i++) {
      if (cartItem[i].itemId === itemId) {
        quantity = options[i].quantity;
      }
    }
    return quantity;
  };

  const getOptionQuantity = (choiceId) => {
    var quantity = 0;
    var i;
    for (i = 0; i < options.length; i++) {
      if (options[i].choice === choiceId) {
        quantity = options[i].quantity;
      }
    }
    return quantity;
  };

  const quantityFunc = () => {
    if (quantity == null) {
      setState({ quantity: 1 });
      return quantity;
    } else if (cartItem != null) {
      return quantity2;
    } else {
      return quantity;
    }
  };
  // onChangeQty(e, id) {
  //   const cartItem = cartItem;
  //   // console.log(cartItem);
  //   const item = cartItem.find((obj) => obj.itemId === id);
  //   item.quantity = e;
  //   setState({
  //     cartItem,
  //   });
  // }
  const testdata = [
    {
      name: 'First Name',
      pname: 'First Item',
      description: 'First Description',
      pic: [<Ionicons name="fast-food-outline" size={45} />],
      price: 'RM 10',
    },
    {
      name: 'Second Name',
      pname: 'Second Item',
      description: 'Second Description',
    },
    {
      name: 'Third Item',
      pname: 'Third Item',
      description: 'Third Description',
    },
    {
      name: 'Fourth Item',
      pname: 'Fourth Item',
      description: 'Fourth Description',
    },
    {
      name: 'Fifth Item',
      pname: 'Fifth Item',
      description: 'Fifth Description',
    },
  ]
  const renderItem = ({ item }) => {

    return (
      <View style={[styles.card]}>
        <View style={{ flexDirection: 'row' }}>
          <View style={{ width: '50%', paddingRight: 5 }}>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              fontSize: 17,
              color: Colors.blackColor,
            }} numberOfLines={1}>
              {item.name}
            </Text>
          </View>
          <View style={{ width: '50%' }}>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              fontSize: 17,
              color: Colors.blackColor,
              textAlign: 'right'
            }} numberOfLines={1}>
              {item.pname}
            </Text>
          </View>
        </View>

        <View style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          marginTop: 10,
        }}>
          <View style={{
            marginBottom: 15,
          }}>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              fontSize: 13,
              marginHorizontal: 5,
              marginTop: 6,
              color: Colors.darkBgColor,
            }} numberOfLines={2}>
              {item.description}
            </Text>
          </View>
        </View>
        <View style={{ flexDirection: 'row' }}>
          <ScrollView horizontal>
            <View style={{ alignItems: 'center', }}>
              <View style={{
                width: windowWidth * 0.07,
                height: windowWidth * 0.07,
                backgroundColor: Colors.secondaryColor,
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 5,
              }}>
                {item.pic}
              </View>
              <Text style={{ fontSize: 16, }}>{item.price}</Text>
            </View>
          </ScrollView>
          <TouchableOpacity style={{ alignItems: 'flex-end', justifyContent: 'flex-end' }} onPress={() => { }}>
            <View style={{ backgroundColor: Colors.primaryColor, paddingHorizontal: 15, paddingVertical: 10, borderRadius: 12, }}>
              <Text style={{ color: 'white' }}>Select</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View >
    );
  }

  const goToCart = () => {
    // console.log('Cart.getCartItem()');
    // console.log(Cart.getCartItem());

    if (Cart.getCartItem().length > 0) {
      props.navigation.navigate({
        name: 'Cart',
        params: {
          test,
          outletData,
          menuItem,
          clicked,
          clicked1,
          clicked2,
        },
        merge: true,
      });
    } else {
      Alert.alert(
        'Info',
        'No items in your cart at the moment',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );
    }
  };

  const lessqty = (id) => {
    if (cartItem != null) {
      const cartItem = cartItem;
      cartItem.quantity -= 1;
      save();
    } else {
      return setState({ quantity: quantity - 1 });
    }
  };

  const addqty = (id) => {
    if (cartItem != null) {
      const cartItem = cartItem;
      cartItem.quantity += 1;
      save();
    } else {
      return setState({ quantity: quantity + 1 });
    }
  };

  const totals = () => {
    if (cartItem != null) {
      return totalState;
    } else {
      return totalState;
    }
  };

  const save = () => {
    if (cartItem != null) {
      return goToCart();
    } else {
      return setState({ visible: true });
    }
  };

  const renderVariants = (dataItem, item) => {
    const item2 = dataItem.item;
  };

  const renderAddons = (dataItem, item) => {
    const item2 = dataItem.item;
  };

  // function end

  var overrideCategoryPrice = undefined;
  if (
    selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] &&
    overrideCategoryPriceNameDict[
    selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name
    ] !== undefined
  ) {
    overrideCategoryPrice =
      overrideCategoryPriceNameDict[
        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name
      ].overridePrice;
  }

  var extraPrice = 0;
  //var tempPrice1 = outletData.deliveryPrice;

  if (orderType === ORDER_TYPE.DELIVERY &&
    outletData &&
    outletData.deliveryPrice) {
    extraPrice = outletData.deliveryPrice;
  }
  else if (orderType === ORDER_TYPE.PICKUP &&
    orderTypeSub === ORDER_TYPE_SUB.NORMAL &&
    outletData &&
    outletData.pickUpPrice) {
    extraPrice = outletData.pickUpPrice;
  }

  if (orderType === ORDER_TYPE.DELIVERY) {
    extraPrice = selectedOutletItem.deliveryCharges || 0;

    if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
      extraPrice = selectedOutletItem.price * extraPrice / 100;
    }

    if (!selectedOutletItem.deliveryChargesActive) {
      extraPrice = 0;
    }
  }
  // else {
  //   extraPrice = 0;
  // }

  if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
    extraPrice = selectedOutletItem.pickUpCharges || 0;

    if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
      extraPrice = selectedOutletItem.price * extraPrice / 100;
    }

    if (!selectedOutletItem.pickUpChargesActive) {
      extraPrice = 0;
    }
  }
  // else {
  //   extraPrice = 0;
  // }

  if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
    extraPrice = selectedOutletItem.otherDCharges || 0;

    if (extraPrice && selectedOutletItem.otherDChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
      extraPrice = selectedOutletItem.price * extraPrice / 100;
    }

    if (!selectedOutletItem.otherDChargesActive) {
      extraPrice = 0;
    }
  }

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View style={[styles.containerOutter, {
        ...getTransformForScreenInsideNavigation(),
      }]}>
        {/* <View
          style={[
            styles.sidebar,
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}
        <KeyboardAwareScrollView behavior={'position'} style={styles.container} nestedScrollEnabled={false}>
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              marginLeft: Platform.OS == 'android' ? -10 : -15,
              marginTop: 1,
              marginBottom: 10,
            }}
            onPress={() => {
              requestAnimationFrame(() => {
                props.navigation.goBack();
              });
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                alignContent: 'center',
              }}>
              <View>
                {switchMerchant ? (
                  <Feather
                    name="chevron-left"
                    size={20}
                    color={Colors.primaryColor}
                    style={{ paddingLeft: '1%' }}
                  />
                ) : (
                  <Feather
                    name="chevron-left"
                    size={30}
                    color={Colors.primaryColor}
                    style={{}}
                  />
                )}
              </View>
              <Text
                style={{
                  color: Colors.primaryColor,
                  fontSize: switchMerchant ? 14 : 17,
                  textAlign: 'center',
                  fontFamily: 'NunitoSans-Bold',
                  marginBottom: Platform.OS === 'ios' ? 0 : 2,
                  //top: -2,
                  //marginLeft: -3,
                }}>
                Back
              </Text>
            </View>
          </TouchableOpacity>

          <View
            style={{
              flexDirection: 'row',
              alignContent: 'center',
              alignItems: 'center',
              width: '75%',
              display: 'flex',
              justifyContent: 'flex-start',
              // backgroundColor: 'blue',
              marginBottom: 20,
            }}>
            <View
              style={[
                {
                  backgroundColor: Colors.secondaryColor,
                  // width: 60,
                  // height: 60,
                  width: switchMerchant
                    ? windowWidth * 0.08
                    : windowWidth * 0.11,
                  height: switchMerchant
                    ? windowWidth * 0.08
                    : windowWidth * 0.11,
                  borderRadius: 10,
                },
                selectedOutletItem.image
                  ? {}
                  : {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
              ]}>
              {selectedOutletItem.image ? (
                <>
                  {switchMerchant ? (
                    <AsyncImage
                      source={{ uri: selectedOutletItem.image }}
                      item={selectedOutletItem}
                      style={{
                        // width: 60,
                        // height: 60,
                        width: windowWidth * 0.08,
                        height: windowWidth * 0.08,
                        borderRadius: 10,
                      }}
                    />
                  ) : (
                    <AsyncImage
                      source={{ uri: selectedOutletItem.image }}
                      item={selectedOutletItem}
                      style={{
                        // width: 60,
                        // height: 60,
                        width: windowWidth * 0.11,
                        height: windowWidth * 0.11,
                        borderRadius: 10,
                      }}
                    />
                  )}
                </>
              ) : (
                <>
                  {switchMerchant ? (
                    <Ionicons name="fast-food-outline" size={20} />
                  ) : (
                    <Ionicons name="fast-food-outline" size={50} />
                  )}
                </>
              )}
            </View>
            <View
              style={{
                marginLeft: 15,
                // flexDirection: 'row',
                // flexShrink: 1,
                width: '90%',
                // backgroundColor: 'red',
              }}>
              <Text
                // numberOfLines={1}
                style={{
                  fontSize: switchMerchant ? 10 : 16,
                  textTransform: 'uppercase',
                  fontFamily: 'NunitoSans-Bold',
                  // flexWrap: 'wrap',
                  // flex: 1,
                  // flexShrink: 1,
                  // width: '100%',
                }}>
                {selectedOutletItem.name}
              </Text>
              <Text
                // numberOfLines={1}
                style={{
                  fontSize: switchMerchant ? 10 : 16,
                  textTransform: 'uppercase',
                  fontFamily: 'NunitoSans-Regular',
                  color: Colors.descriptionColor
                  // flexWrap: 'wrap',
                  // flex: 1,ss
                  // flexShrink: 1,
                  // width: '100%',
                }}>
                {selectedOutletItem.description}
              </Text>
              <Text
                style={{
                  color: Colors.primaryColor,
                  fontFamily: 'NunitoSans-Bold',
                  paddingTop: 5,
                  fontSize: switchMerchant ? 10 : 16,
                }}>
                {
                  selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE
                    ?
                    `Variable Pricing`
                    :
                    `RM ${overrideItemPriceSkuDict[selectedOutletItem.sku] !== undefined ||
                      overrideCategoryPrice !== undefined
                      ?
                      overrideItemPriceSkuDict[selectedOutletItem.sku] !== undefined ?
                        parseFloat(
                          overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice
                        ).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                        :
                        parseFloat(
                          overrideCategoryPrice
                        ).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
                      : parseFloat(extraPrice + selectedOutletItem.price).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
                }{selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[selectedOutletItem.unitType]}` : ''}
              </Text>
              {/* <Text>RM{parseFloat(extraPrice + selectedOutletItem.price).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text> */}

              {/* <Text
              style={{
                color: Colors.primaryColor,
                fontFamily: 'NunitoSans-Bold',
                paddingTop: 5,
                fontSize: switchMerchant ? 10 : 16,
              }}>
              RM
              {parseFloat(selectedOutletItem.price)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text> */}
            </View>
          </View>

          {/* <View style={{ marginBottom: 15 }}>
          <Text style={{ fontWeight: "700", fontSize: 16 }}>
            Choose size
              </Text>
          <View style={{ marginTop: 15, flexDirection: "row" }}>
            <TouchableOpacity style={{
              width: "30%", height: 40, backgroundColor: clicked == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
              borderColor: Colors.descriptionColor,
            }}
              onPress={() => { changeClick() }}>
              <Text style={{ alignSelf: "center", color: clicked == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Small</Text>
            </TouchableOpacity>

            <View style={{ width: '5%' }}></View>

            <TouchableOpacity style={{
              width: "30%", height: 40, backgroundColor: clicked1 == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
              borderColor: Colors.descriptionColor,
            }}
              onPress={() => { changeClick1() }}>
              <Text style={{ alignSelf: "center", color: clicked1 == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Medium</Text>
            </TouchableOpacity>

            <View style={{ width: '5%' }}></View>

            <TouchableOpacity style={{
              width: "30%", height: 40, backgroundColor: clicked2 == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
              borderColor: Colors.descriptionColor,
            }}
              onPress={() => { changeClick2() }}>
              <Text style={{ alignSelf: "center", color: clicked2 == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Big</Text>
            </TouchableOpacity>
          </View>
        </View> */}

          {isLoading ? (
            <View>
              <ActivityIndicator color={Colors.primaryColor} size={'large'} />
            </View>
          ) : (
            <>
              {sortedVariantAddOnList
                ? sortedVariantAddOnList.map(
                  (item, index) => {
                    // // console.log('outletsItemAddOnDict');
                    // // console.log(outletsItemAddOnDict);
                    // // console.log('outletsItemAddOnChoiceDict');
                    // // console.log(outletsItemAddOnChoiceDict);

                    if (
                      item.minSelect !== undefined &&
                      item.maxSelect !== undefined
                    ) {
                      // means is variant

                      return (
                        <View style={{ marginBottom: 15 }}>
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              // fontWeight: "500",
                              // fontSize: 16,
                              marginBottom: 0,
                              color: Colors.descriptionColor,
                              // color: Colors.mainTxtColor,
                              // fontWeight: "500",
                              fontSize: switchMerchant ? 10 : 17,
                              fontFamily: 'NunitoSans-SemiBold',
                            }}>
                            {item.name ? item.name : 'N/A'} {item.maxSelect > 1 ? `(Please select up to ${item.maxSelect} choices)` : ''}
                          </Text>

                          {/* <ScrollView
                              style={{ marginTop: 10 }}
                              horizontal
                              nestedScrollEnabled={true}
                              showsHorizontalScrollIndicator={false}> */}
                          <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginTop: 10 }}>
                            {outletsItemAddOnChoiceDict[item.uniqueId] &&
                              outletsItemAddOnChoiceDict[item.uniqueId].filter(choiceFilter => {
                                return !choiceFilter.isHidden;
                              }).sort((a, b) => a.name.localeCompare(b.name)).map(
                                (item2, index) => {
                                  return <>
                                    <TouchableOpacity
                                      style={{
                                        // width: '30%',
                                        marginHorizontal: 8,
                                        paddingHorizontal: 12,
                                        marginVertical: 5,
                                        height: 40,
                                        backgroundColor:
                                          selectedOutletItemAddOnChoice[
                                            item2.uniqueId
                                          ]
                                            ? Colors.primaryColor
                                            : Colors.whiteColor,
                                        justifyContent: 'center',
                                        borderRadius: 20,
                                        borderWidth: 1,
                                        borderColor:
                                          selectedOutletItemAddOnChoice[
                                            item2.uniqueId
                                          ]
                                            ? 'transparent'
                                            : Colors.descriptionColor,
                                        // borderColor: Colors.descriptionColor,
                                      }}
                                      onPress={() => {
                                        requestAnimationFrame(() => {
                                          // var chooseValue = false;
                                          // if (selectedOutletItemAddOnChoice[item2.uniqueId] === undefined ||
                                          //   selectedOutletItemAddOnChoice[item2.uniqueId] === false) {
                                          //   chooseValue = true;
                                          // }
                                          // else {
                                          //   chooseValue = false;
                                          // }

                                          // CommonStore.update(s => {
                                          //   s.selectedOutletItemAddOnChoice = {
                                          //     ...selectedOutletItemAddOnChoice,
                                          //     [item2.uniqueId]: chooseValue,
                                          //   };

                                          //   if (selectedOutletItemAddOn[item.uniqueId] === undefined) {
                                          //     s.selectedOutletItemAddOn = {
                                          //       ...selectedOutletItemAddOn,
                                          //       // [item.uniqueId]: new Set(),
                                          //       [item.uniqueId]: {},
                                          //     };
                                          //   }

                                          //   // can consider use set
                                          //   // s.selectedOutletItemAddOn = {
                                          //   //   ...selectedOutletItemAddOn,
                                          //   //   [item.uniqueId]: {
                                          //   //     ...(selectedOutletItemAddOn[item.uniqueId]),
                                          //   //     [item2.uniqueId]: chooseValue,
                                          //   //   },
                                          //   // };

                                          //   s.selectedOutletItemAddOn = {
                                          //     ...selectedOutletItemAddOn,
                                          //     [item.uniqueId]: {
                                          //       ...(selectedOutletItemAddOn[item.uniqueId]),
                                          //       [item2.uniqueId]: chooseValue,
                                          //     },
                                          //   };
                                          // });

                                          /////////////////////////////////
                                          // minSelect/maxSelect calculations

                                          var selectedOutletItemAddOnChoiceRemoved =
                                            {};

                                          if (
                                            item.minSelect === 1 &&
                                            item.maxSelect === 1
                                          ) {
                                            // means can only choose 1 addon choice, the others need removed

                                            for (
                                              var i = 0;
                                              i <
                                              outletsItemAddOnChoiceDict[
                                                item.uniqueId
                                              ].length;
                                              i++
                                            ) {
                                              selectedOutletItemAddOnChoiceRemoved[
                                                outletsItemAddOnChoiceDict[
                                                  item.uniqueId
                                                ][i].uniqueId
                                              ] = false;
                                            }
                                          }

                                          /////////////////////////////////

                                          var chooseValue = false;
                                          if (
                                            selectedOutletItemAddOnChoice[
                                            item2.uniqueId
                                            ] === undefined ||
                                            selectedOutletItemAddOnChoice[
                                            item2.uniqueId
                                            ] === false
                                          ) {
                                            chooseValue = true;
                                          } else {
                                            chooseValue = false;
                                          }

                                          //////////////////////////////////////////////////////////////////

                                          // 2023-01-09 - Prevent user from select the choice num exceed the max choice num

                                          var maxChoices = item.maxSelect;
                                          var totalChoiceNum = 0;
                                          var choseChoiceIdList = [];

                                          if (chooseValue && selectedOutletItemAddOn[item.uniqueId]) {
                                            for (
                                              var i = 0;
                                              i <
                                              outletsItemAddOnChoiceDict[
                                                item.uniqueId
                                              ].length;
                                              i++
                                            ) {
                                              var currChoiceId = outletsItemAddOnChoiceDict[
                                                item.uniqueId
                                              ][i].uniqueId;

                                              if (selectedOutletItemAddOnChoice[currChoiceId]) {
                                                // means selected

                                                totalChoiceNum++;

                                                choseChoiceIdList.push(currChoiceId);
                                              }
                                            }
                                          }

                                          if (chooseValue && totalChoiceNum >= maxChoices && choseChoiceIdList.length > 0) {
                                            // try to remove one of the previous choice

                                            selectedOutletItemAddOnChoiceRemoved[
                                              choseChoiceIdList[0]
                                            ] = false;

                                            // chooseValue = false;
                                          }

                                          //////////////////////////////////////////////////////////////////

                                          CommonStore.update((s) => {
                                            s.selectedOutletItemAddOnChoice = {
                                              ...selectedOutletItemAddOnChoice,

                                              ...selectedOutletItemAddOnChoiceRemoved,

                                              [item2.uniqueId]: chooseValue,
                                            };

                                            if (
                                              selectedOutletItemAddOn[
                                              item.uniqueId
                                              ] === undefined
                                            ) {
                                              s.selectedOutletItemAddOn = {
                                                ...selectedOutletItemAddOn,
                                                // [item.uniqueId]: new Set(),
                                                [item.uniqueId]: {},
                                              };
                                            }

                                            // can consider use set
                                            // s.selectedOutletItemAddOn = {
                                            //   ...selectedOutletItemAddOn,
                                            //   [item.uniqueId]: {
                                            //     ...(selectedOutletItemAddOn[item.uniqueId]),
                                            //     [item2.uniqueId]: chooseValue,
                                            //   },
                                            // };

                                            s.selectedOutletItemAddOn = {
                                              ...selectedOutletItemAddOn,
                                              [item.uniqueId]: {
                                                ...selectedOutletItemAddOn[
                                                item.uniqueId
                                                ],

                                                ...selectedOutletItemAddOnChoiceRemoved,

                                                [item2.uniqueId]: chooseValue,
                                              },
                                            };

                                            s.selectedOutletItemAddOnOi = {
                                              ...selectedOutletItemAddOnOi,
                                              [item.uniqueId]: item.orderIndex !== undefined ? item.orderIndex : 0,
                                            };

                                            // // console.log({
                                            //   ...selectedOutletItemAddOnChoice,

                                            //   ...selectedOutletItemAddOnChoiceRemoved,

                                            //   [item2.uniqueId]: chooseValue,
                                            // });

                                            // console.log({
                                            //   ...selectedOutletItemAddOn,
                                            //   [item.uniqueId]: {
                                            //     ...selectedOutletItemAddOn[
                                            //     item.uniqueId
                                            //     ],

                                            //     ...selectedOutletItemAddOnChoiceRemoved,

                                            //     [item2.uniqueId]: chooseValue,
                                            //   },
                                            // });
                                          });
                                        });
                                      }}>
                                      <Text
                                        style={{
                                          alignSelf: 'center',
                                          color:
                                            selectedOutletItemAddOnChoice[
                                              item2.uniqueId
                                            ]
                                              ? Colors.whiteColor
                                              : Colors.descriptionColor,
                                          fontFamily: 'NunitoSans-Regular',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}>{`${item2.name} (+RM${item2.price
                                          .toFixed(2)
                                          .replace(
                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                            '$1,',
                                          )})`}</Text>
                                    </TouchableOpacity>

                                    {/* <View style={{ width: '5%' }}></View> */}
                                  </>;
                                },
                              )}
                            {/* </ScrollView> */}
                          </View>
                        </View>
                      );
                    } else {
                      // means is addon

                      return (
                        <View style={{ marginBottom: 15 }}>
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              // fontWeight: "500",
                              // fontSize: 16,
                              marginBottom: 0,
                              color: Colors.descriptionColor,
                              // color: Colors.mainTxtColor,
                              // fontWeight: "500",
                              fontSize: switchMerchant ? 10 : 17,
                              fontFamily: 'NunitoSans-SemiBold',
                            }}>
                            {item.name ? item.name : 'N/A'}
                          </Text>

                          <ScrollView style={{ marginTop: 10 }}>
                            {outletsItemAddOnChoiceDict[item.uniqueId] &&
                              outletsItemAddOnChoiceDict[item.uniqueId].filter(choiceFilter => {
                                return !choiceFilter.isHidden;
                              }).sort((a, b) => a.name.localeCompare(b.name)).map(
                                (item2, index) => {
                                  return (
                                    <View
                                      style={{
                                        width: '100%',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        marginTop: 10,
                                        marginBottom: 10,
                                        justifyContent: 'space-between',
                                      }}>
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          alignItems: 'center',
                                          width: '70.5%',
                                        }}>
                                        {/* <TouchableOpacity
                                  underlayColor="transparent"
                                  onPress={() => {
                                    // this.setState({ rememberMe: !this.state.rememberMe });

                                    setNewSelectedOutletItemAddOn({
                                      ...newSelectedOutletItemAddOn,
                                      [item2.uniqueId]: !newSelectedOutletItemAddOn[item2.uniqueId],
                                    });
                                  }}>
                                  <View
                                    style={[
                                      {
                                        borderWidth: StyleSheet.hairlineWidth,
                                        borderColor: Colors.descriptionColor,
                                        width: 26,
                                        height: 26,
                                        borderRadius: 8,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                      },
                                      {
                                        backgroundColor: newSelectedOutletItemAddOn[item2.uniqueId]
                                          ? Colors.primaryColor
                                          : null,
                                      },
                                    ]}>
                                    <AntDesign name="check" size={18} color="#ffffff" />
                                  </View>
                                </TouchableOpacity> */}

                                        <Text
                                          style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: switchMerchant ? 10 : 16,
                                            // marginLeft: 15,
                                          }}>
                                          {item2.name}
                                        </Text>
                                      </View>

                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          alignItems: 'center',
                                          // backgroundColor: 'red',
                                          justifyContent: 'space-between',
                                          // width: windowWidth * 0.4,
                                          width: '20%',
                                        }}>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            width: '40%',
                                          }}>
                                          <TouchableOpacity
                                            onPress={() => {
                                              // if (quantity - 1 > 0) {
                                              //   lessqty(menuItem.id);
                                              // }

                                              // setQuantity(quantity - 1 >= 0 ? quantity - 1 : 0);

                                              var quantityTemp = 0;

                                              if (
                                                newSelectedOutletItemAddOnDetails[
                                                item2.uniqueId
                                                ]
                                              ) {
                                                quantityTemp =
                                                  newSelectedOutletItemAddOnDetails[
                                                    item2.uniqueId
                                                  ].quantity;

                                                quantityTemp =
                                                  quantityTemp - 1 >= 0
                                                    ? quantityTemp - 1
                                                    : 0;

                                                setNewSelectedOutletItemAddOnDetails(
                                                  {
                                                    ...newSelectedOutletItemAddOnDetails,
                                                    [item2.uniqueId]: {
                                                      ...newSelectedOutletItemAddOnDetails[
                                                      item2.uniqueId
                                                      ],
                                                      quantity: quantityTemp,
                                                      price: item2.price,

                                                      outletItemAddOnChoiceId:
                                                        item2.uniqueId,
                                                      outletItemAddOnId:
                                                        item.uniqueId,

                                                      choiceName: item2.name,
                                                      addOnName: item.name,

                                                      minSelect:
                                                        item2.minSelect,
                                                      maxSelect:
                                                        item2.maxSelect,

                                                      oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                                      pal: item.pal !== undefined ? item.pal : null,

                                                      ...item.min && {
                                                        addOnMin: item.min,
                                                      },
                                                      ...item.max && {
                                                        addOnMax: item.max,
                                                      },
                                                    },
                                                  },
                                                );
                                              } else {
                                                setNewSelectedOutletItemAddOnDetails(
                                                  {
                                                    ...newSelectedOutletItemAddOnDetails,
                                                    [item2.uniqueId]: {
                                                      quantity: quantityTemp,
                                                      price: item2.price,

                                                      outletItemAddOnChoiceId:
                                                        item2.uniqueId,
                                                      outletItemAddOnId:
                                                        item.uniqueId,

                                                      choiceName: item2.name,
                                                      addOnName: item.name,

                                                      minSelect:
                                                        item2.minSelect,
                                                      maxSelect:
                                                        item2.maxSelect,

                                                      oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                                      pal: item.pal !== undefined ? item.pal : null,

                                                      ...item.min && {
                                                        addOnMin: item.min,
                                                      },
                                                      ...item.max && {
                                                        addOnMax: item.max,
                                                      },
                                                    },
                                                  },
                                                );
                                              }
                                            }}>
                                            <View
                                              style={[
                                                styles.addBtn,
                                                {
                                                  backgroundColor:
                                                    Colors.descriptionColor,

                                                  width: 20,
                                                  height: 22,
                                                  marginRight: switchMerchant ? 10 : windowWidth < 1000 ? 0 : -1
                                                },
                                              ]}>
                                              <Text
                                                style={{
                                                  fontSize: switchMerchant
                                                    ? 13
                                                    : 15,
                                                  fontWeight: '500',
                                                  color: Colors.whiteColor,
                                                }}>
                                                -
                                              </Text>
                                            </View>
                                          </TouchableOpacity>
                                          <View
                                            style={[
                                              styles.addBtn,
                                              {
                                                backgroundColor:
                                                  Colors.whiteColor,
                                                borderWidth:
                                                  StyleSheet.hairlineWidth,
                                                borderColor:
                                                  Colors.descriptionColor,
                                                borderWidth: 1.5,

                                                width: 25,
                                                height: 22,
                                              },
                                            ]}>
                                            <TouchableOpacity onPress={() => setEditAddOnQuantity(true)}>
                                              {editAddOnQuantity ?
                                                <TextInput
                                                  editable={false}
                                                  style={[
                                                    {
                                                      fontSize: switchMerchant
                                                        ? 10
                                                        : 13,
                                                      // fontWeight: "bold",
                                                      fontFamily: 'NunitoSans-Bold',
                                                      // color: Colors.primaryColor,
                                                      color: Colors.descriptionColor,
                                                      paddingTop: 5,
                                                      paddingBottom: 0,
                                                    },
                                                  ]}
                                                  value={newSelectedOutletItemAddOnDetails[
                                                    item2.uniqueId
                                                  ]
                                                    ? newSelectedOutletItemAddOnDetails[
                                                      item2.uniqueId
                                                    ].quantity
                                                    : 0}
                                                  onChangeText={(text) => {
                                                    setNewSelectedOutletItemAddOnDetails(
                                                      {
                                                        ...newSelectedOutletItemAddOnDetails,
                                                        [item2.uniqueId]: {
                                                          ...newSelectedOutletItemAddOnDetails[
                                                          item2.uniqueId
                                                          ],
                                                          quantity: text.length > 0 ? parseInt(text) : 0,
                                                          price: item2.price,

                                                          outletItemAddOnChoiceId:
                                                            item2.uniqueId,
                                                          outletItemAddOnId:
                                                            item.uniqueId,

                                                          choiceName: item2.name,
                                                          addOnName: item.name,

                                                          minSelect:
                                                            item2.minSelect,
                                                          maxSelect:
                                                            item2.maxSelect,

                                                          oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                                          pal: item.pal !== undefined ? item.pal : null,

                                                          ...item.min && {
                                                            addOnMin: item.min,
                                                          },
                                                          ...item.max && {
                                                            addOnMax: item.max,
                                                          },
                                                        },
                                                      },
                                                    );

                                                  }}
                                                  onEndEditing={() => {
                                                    setEditAddOnQuantity(false)
                                                  }}
                                                  autoFocus={editAddOnQuantity}
                                                  keyboardType={'decimal-pad'}
                                                  // placeholder="0"
                                                  underlineColorAndroid={Colors.fieldtBgColor}
                                                />
                                                :
                                                <Text
                                                  style={[
                                                    {
                                                      fontSize: switchMerchant
                                                        ? 10
                                                        : 13,
                                                      // fontWeight: "bold",
                                                      fontFamily: 'NunitoSans-Bold',
                                                      // color: Colors.primaryColor,
                                                      color: Colors.descriptionColor,
                                                    },
                                                  ]}>
                                                  {newSelectedOutletItemAddOnDetails[
                                                    item2.uniqueId
                                                  ]
                                                    ? newSelectedOutletItemAddOnDetails[
                                                      item2.uniqueId
                                                    ].quantity
                                                    : 0}
                                                </Text>
                                              }
                                            </TouchableOpacity>
                                          </View>

                                          <TouchableOpacity
                                            onPress={() => {
                                              // addqty(menuItem.id);
                                              // setQuantity(quantity + 1);

                                              var quantityTemp = 0;

                                              if (
                                                newSelectedOutletItemAddOnDetails[
                                                item2.uniqueId
                                                ]
                                              ) {
                                                quantityTemp =
                                                  newSelectedOutletItemAddOnDetails[
                                                    item2.uniqueId
                                                  ].quantity;

                                                quantityTemp += 1;

                                                setNewSelectedOutletItemAddOnDetails(
                                                  {
                                                    ...newSelectedOutletItemAddOnDetails,
                                                    [item2.uniqueId]: {
                                                      ...newSelectedOutletItemAddOnDetails[
                                                      item2.uniqueId
                                                      ],
                                                      quantity: quantityTemp,
                                                      price: item2.price,

                                                      outletItemAddOnChoiceId:
                                                        item2.uniqueId,
                                                      outletItemAddOnId:
                                                        item.uniqueId,

                                                      choiceName: item2.name,
                                                      addOnName: item.name,

                                                      minSelect:
                                                        item2.minSelect,
                                                      maxSelect:
                                                        item2.maxSelect,

                                                      oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                                      pal: item.pal !== undefined ? item.pal : null,

                                                      ...item.min && {
                                                        addOnMin: item.min,
                                                      },
                                                      ...item.max && {
                                                        addOnMax: item.max,
                                                      },
                                                    },
                                                  },
                                                );
                                              } else {
                                                setNewSelectedOutletItemAddOnDetails(
                                                  {
                                                    ...newSelectedOutletItemAddOnDetails,
                                                    [item2.uniqueId]: {
                                                      quantity: 1,
                                                      price: item2.price,

                                                      outletItemAddOnChoiceId:
                                                        item2.uniqueId,
                                                      outletItemAddOnId:
                                                        item.uniqueId,

                                                      choiceName: item2.name,
                                                      addOnName: item.name,

                                                      minSelect:
                                                        item2.minSelect,
                                                      maxSelect:
                                                        item2.maxSelect,

                                                      oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                                                      pal: item.pal !== undefined ? item.pal : null,

                                                      ...item.min && {
                                                        addOnMin: item.min,
                                                      },
                                                      ...item.max && {
                                                        addOnMax: item.max,
                                                      },
                                                    },
                                                  },
                                                );
                                              }
                                            }}>
                                            <View
                                              style={[
                                                styles.addBtn,
                                                {
                                                  backgroundColor:
                                                    Colors.primaryColor,
                                                  left: -1,

                                                  width: 20,
                                                  height: 22,
                                                },
                                              ]}>
                                              <Text
                                                style={{
                                                  fontSize: switchMerchant
                                                    ? 13
                                                    : 15,
                                                  fontWeight: '500',
                                                  color: Colors.whiteColor,
                                                }}>
                                                +
                                              </Text>
                                            </View>
                                          </TouchableOpacity>
                                        </View>

                                        <View
                                          style={[{
                                            width: '60%',
                                            flexDirection: 'row',
                                            // backgroundColor: 'blue',
                                            justifyContent: 'flex-end',
                                          }, switchMerchant ? {
                                            // paddingLeft: 5,
                                          } : {}]}>
                                          <Text
                                            style={{
                                              fontFamily: 'NunitoSans-Regular',
                                              fontSize: switchMerchant
                                                ? 10
                                                : 14,
                                              marginLeft: 10,
                                            }}>
                                            + RM
                                            {(
                                              (newSelectedOutletItemAddOnDetails[
                                                item2.uniqueId
                                              ]
                                                ? newSelectedOutletItemAddOnDetails[
                                                  item2.uniqueId
                                                ].quantity > 0
                                                  ? newSelectedOutletItemAddOnDetails[
                                                    item2.uniqueId
                                                  ].quantity
                                                  : 1
                                                : 1) * item2.price
                                            )
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text>
                                        </View>
                                      </View>
                                    </View>
                                  );
                                },
                              )}
                          </ScrollView>
                        </View>
                      );
                    }
                  },
                )
                : null}
            </>
          )}

          {/* {outletsItemAddOnDict[selectedOutletItem.uniqueId]
          ? outletsItemAddOnDict[selectedOutletItem.uniqueId].map((item, index) => {  
            // console.log('outletsItemAddOnDict');
            // console.log(outletsItemAddOnDict);
            // console.log('outletsItemAddOnChoiceDict');
            // console.log(outletsItemAddOnChoiceDict);

            return (
              (item.name !== "" ? <View style={{ marginBottom: 15 }}>
                <Text
                  style={{
                    color: Colors.descriptionColor,
                    fontWeight: "500",
                    fontSize: 16,
                    marginBottom: 15,
                  }}
                >
                  {item.name}
                </Text>
                {outletsItemAddOnChoiceDict[item.uniqueId] && outletsItemAddOnChoiceDict[item.uniqueId].map((item2, index) => {
                  return (
                    (item2.name !== "" ? <View
                      style={{
                        marginBottom: 20,
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        {item.leastSelect == 1 ? (
                          <View
                            style={{
                              flexDirection: 'row',
                              marginRight: 10,
                            }}>

                            <TouchableOpacity
                              style={[
                                styles.addBtnSmall,
                                {
                                  backgroundColor:
                                    getOptionQuantity(item2.uniqueId) > 0
                                      ? Colors.primaryColor
                                      : "#e8e9eb",
                                },
                              ]}
                              onPress={() => {
                                // console.log("getOptionQuantity(item2.uniqudId)", -getOptionQuantity(item2.uniqueId))
                                if (getOptionQuantity(item2.uniqueId) >= 1) {
                                  addOption(
                                    item2.name,
                                    -getOptionQuantity(item2.uniqueId),
                                    item2.uniqueId,
                                    item2.price,
                                  );
                                }
                                else {
                                  addOption(
                                    item2.name,
                                    1,
                                    item2.uniqueId,
                                    item2.price,
                                  );
                                }


                              }}
                            >
                              <Icon1
                                name="check"
                                size={25}
                                color={
                                  getOptionQuantity(item2.uniqueId) > 0
                                    ? Colors.whiteColor
                                    : Colors.descriptionColor
                                }
                              />
                            </TouchableOpacity>

                          </View>
                        ) : (
                            <View
                              style={{
                                flexDirection: 'row',
                                marginRight: 10,
                              }}>
                              <View>
                                <TouchableOpacity
                                  style={[
                                    styles.addBtnSmall,
                                    {
                                      backgroundColor:
                                        containsObject(item2.uniqueId) == true
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                    },
                                  ]}
                                  onPress={() => {
                                    addLeastItem(
                                      item2.name,
                                      item2.uniqueId,
                                      item2.price,
                                      1,
                                    );
                                  }}>
                                  <Icon1
                                    name="check"
                                    size={25}
                                    color={
                                      containsObject(item2.uniqueId) == true
                                        ? Colors.whiteColor
                                        : Colors.descriptionColor
                                    }
                                  />
                                </TouchableOpacity>
                              </View>
                            </View>
                          )}
                        <View style={{ width: '55%' }}>
                          <Text style={{ fontSize: 14, fontWeight: '700' }}>
                            {item2.name}
                          </Text>
                        </View>
                        {item.leastSelect == 1 ? (
                          <View style={{ width: '14%', marginLeft: 3 }}>
                            <View style={{ flexDirection: 'row' }}>
                              <TouchableOpacity
                                disabled={false}
                                onPress={() => {
                                  if (item2.quantity > 0) {
                                    item2.quantity - 1;
                                  }
                                  addOption(
                                    item2.name,
                                    -1,
                                    item2.uniqueId,
                                    item2.price,
                                  );
                                }}>
                                <View
                                  style={[
                                    styles.addBtnSmall,
                                    {
                                      backgroundColor:
                                        Colors.descriptionColor,
                                    },
                                  ]}>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      fontWeight: '500',
                                      color: Colors.whiteColor,
                                    }}>
                                    -
                                    </Text>
                                </View>
                              </TouchableOpacity>
                              <View
                                style={[
                                  styles.addBtnSmall,
                                  {
                                    backgroundColor: Colors.whiteColor,
                                    borderWidth: StyleSheet.hairlineWidth,
                                    borderColor: Colors.descriptionColor,
                                  },
                                ]}>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    fontWeight: 'bold',
                                    color: Colors.primaryColor,
                                  }}>
                                  {getOptionQuantity(item2.uniqueId)}
                                </Text>
                              </View>
                              <TouchableOpacity
                                disabled={false}
                                onPress={() => {
                                  item2.uniqueId + 1;
                                  addOption(
                                    item2.name,
                                    1,
                                    item2.uniqueId,
                                    item2.price,
                                  );
                                }}>
                                <View
                                  style={[
                                    styles.addBtnSmall,
                                    {
                                      backgroundColor: Colors.primaryColor,
                                    },
                                  ]}>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      fontWeight: '500',
                                      color: Colors.whiteColor,
                                    }}>
                                    +
                                    </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>
                        ) : (
                            <View
                              style={{ width: '14%', marginLeft: 3 }}></View>
                          )}
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        {item.leastSelect == 1 ? (
                          <View style={{ width: '50%' }}>
                            <Text style={{ fontSize: 14, fontWeight: '300' }}>
                              {' '}
                                + RM
                                {(parseFloat(item2.price).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,') *
                                getOptionQuantity(item2.uniqueId)).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                            </Text>
                          </View>
                        ) : (
                            <View style={{ width: '50%' }}>
                              <Text style={{ fontSize: 14, fontWeight: '300' }}>
                                {' '}
                                + RM
                                {parseFloat(
                                  item2.price,
                                ).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,') * 1}
                              </Text>
                            </View>
                          )}
                      </View>
                    </View> : null)
                  );
                })}
              </View> : null)
            );
          })
          : null} */}

          {
            (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE)
              ?
              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                // keyboardVerticalOffset={-windowHeight * 0.2}
                style={{
                  marginBottom: 15,
                  marginTop: 10,
                }}>
                <Text
                  style={{
                    //color: Colors.descriptionColor,
                    // color: Colors.mainTxtColor,
                    // fontWeight: "500",
                    fontSize: switchMerchant ? 10 : 17,
                    fontFamily: 'NunitoSans-SemiBold',
                    marginBottom: 15,
                  }}>
                  Variable Price:
                </Text>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  // textAlignVertical={'top'}
                  style={[styles.textInput, {
                    height: 50,
                    width: '12%',
                  }, switchMerchant ? {
                    fontSize: 10,
                    height: 35,
                    width: '15%',
                  } : {}]}
                  placeholder="0.00"
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  // onChangeText={(text) => {
                  //   // setState({ remark: text });
                  //   setRemark(text);
                  // }}
                  keyboardType='decimal-pad'
                  value={variablePrice}
                  //iOS
                  // clearTextOnFocus
                  selectTextOnFocus
                  //////////////////////////////////////////////
                  //Android
                  // onFocus={() => {
                  //   setTemp(variablePrice)
                  //   setVariablePrice('');
                  // }}
                  ///////////////////////////////////////////////
                  //When textinput is not sele
                  // onEndEditing={() => {
                  //   if (variablePrice == '') {
                  //     setVariablePrice(temp);
                  //   }
                  // }}
                  onChangeText={(text) => {
                    setVariablePrice(parseValidPriceText(text));
                  }}
                // multiline={true}
                />
              </KeyboardAvoidingView>
              :
              <></>
          }

          <KeyboardAvoidingView
            behavior={'height'}
            keyboardVerticalOffset={windowHeight * 0.2}
            style={{
              marginBottom: 15,
              marginTop: 10,
            }}>
            <Text
              style={{
                //color: Colors.descriptionColor,
                // color: Colors.mainTxtColor,
                // fontWeight: "500",
                fontSize: switchMerchant ? 10 : 17,
                fontFamily: 'NunitoSans-SemiBold',
                marginBottom: 15,
              }}>
              Special Remarks:
            </Text>
            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              textAlignVertical={'top'}
              style={[styles.textInput, switchMerchant ? {
                fontSize: 10,
                paddingTop: 10,
              } : {}]}
              placeholder="eg: no onions"
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              onChangeText={(text) => {
                // setState({ remark: text });
                setRemark(text);
              }}
              value={remark}
              multiline
            />
          </KeyboardAvoidingView>

          <Text
            style={{
              fontSize: switchMerchant ? 10 : 18,
              // fontWeight: "700",
              color: Colors.primaryColor,
              alignSelf: 'flex-end',
              fontFamily: 'NunitoSans-SemiBold',
            }}>
            {/* RM{(parseFloat(totals()) * quantityFunc()).toFixed(2)} */}
            RM
            {' '}
            {
              ((selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * (!isNaN(parseInt(quantity)) ? parseInt(quantity) : 0)))
                // ((totalPrice + addOnPrice) * quantity)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}{selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` for ${quantity}${UNIT_TYPE_SHORT[selectedOutletItem.unitType]}` : ''}
          </Text>

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <View style={{ flexDirection: 'row' }}>
              <TouchableOpacity
                disabled={(selectedOutletItem && selectedOutletItem.lpP !== undefined) ? true : false}
                onPress={() => {
                  // if (quantity - 1 > 0) {
                  //   lessqty(menuItem.id);
                  // }

                  requestAnimationFrame(() => {
                    var quantityParsed = !isNaN(parseInt(quantity)) ? parseInt(quantity) : 1;

                    setQuantity((quantityParsed - 1 >= 1 ? quantityParsed - 1 : 1).toFixed(0));
                  });
                }}>
                <View
                  style={[
                    styles.addBtn,
                    { backgroundColor: Colors.descriptionColor },
                    switchMerchant
                      ? {
                        width: windowWidth * 0.03,
                        height: windowWidth * 0.03,
                      }
                      : {},
                  ]}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 30,
                      fontWeight: '500',
                      color: Colors.whiteColor,
                    }}>
                    -
                  </Text>
                </View>
              </TouchableOpacity>
              <View
                style={[
                  styles.addBtn,
                  {
                    backgroundColor: Colors.whiteColor,
                    borderWidth: StyleSheet.hairlineWidth,
                    borderColor: Colors.descriptionColor,
                    borderWidth: 1.5,

                    width: 50,
                  },
                  switchMerchant
                    ? {
                      width: windowWidth * 0.03,
                      height: windowWidth * 0.03,
                    }
                    : {},
                ]}>
                <TextInput
                  // editable={editQuantity}
                  editable={(selectedOutletItem && selectedOutletItem.lpP !== undefined) ? false : true}
                  style={[
                    {
                      fontSize: switchMerchant ? 10 : 25,
                      fontWeight: 'bold',
                      color: Colors.primaryColor,
                      paddingTop: 5,
                      paddingBottom: 5,
                    },
                  ]}
                  // defaultValue={quantity.toFixed(0)}
                  // onChangeText={(text) => {
                  //   // setQuantity(isNaN(text) ? quantity : parseInt(text))
                  //   text = text.replace(/[^0-9]/g, '');
                  //   setQuantity(text.length > 0 ? parseInt(text) : 0);
                  //   setEditQuantity(false);
                  // }}
                  // onSubmitEditing={(text) => {
                  //   setQuantity(isNaN(text) ? quantity : parseInt(text))                          
                  // }}
                  // onEndEditing={() => {
                  //   setEditQuantity(false);
                  // }}
                  //iOS
                  placeholder={'0'}
                  placeholderTextColor={Platform.select({
                    ios: '#a9a9a9',
                  })}
                  // clearTextOnFocus
                  selectTextOnFocus
                  //////////////////////////////////////////////
                  //Android
                  // onFocus={() => {
                  //   setTemp(quantity);
                  //   setQuantity('');
                  // }}
                  ///////////////////////////////////////////////
                  //When textinput is not selected
                  // onEndEditing={() => {
                  //   if (quantity == '') {
                  //     setQuantity(temp);
                  //   }
                  // }}
                  onChangeText={(text) => {
                    // setState({ itemPrice: text });
                    setQuantity(parseValidIntegerText(text));

                    // setEditQuantity(false);
                  }}
                  value={quantity}
                  // autoFocus={editQuantity}
                  keyboardType={'decimal-pad'}
                  // keyboardType={'default'}
                  // placeholder="0"
                  underlineColorAndroid={Colors.fieldtBgColor}
                />
                {/* <TouchableOpacity onPress={() => setEditQuantity(true)}>
                  {editQuantity ?
                    <TextInput
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 25,
                          fontWeight: 'bold',
                          color: Colors.primaryColor,
                          paddingTop: 5,
                          paddingBottom: 5
                        },
                      ]}
                      defaultValue={quantity.toFixed(0)}
                      onChangeText={(text) => {
                        setQuantity(isNaN(text) ? 0 : parseInt(text));
                        setEditQuantity(false);
                      }}
                      autoFocus={editQuantity}
                      keyboardType={'decimal-pad'}
                      // placeholder="0"
                      underlineColorAndroid={Colors.fieldtBgColor}
                    />
                    :
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 25,
                          fontWeight: 'bold',
                          color: Colors.primaryColor,
                        },
                      ]}>
                      {quantity}
                    </Text>
                  }
                </TouchableOpacity> */}

              </View>
              <TouchableOpacity
                disabled={(selectedOutletItem && selectedOutletItem.lpP !== undefined) ? true : false}
                onPress={() => {
                  // addqty(menuItem.id);

                  requestAnimationFrame(() => {
                    var quantityParsed = !isNaN(parseInt(quantity)) ? parseInt(quantity) : 0;

                    setQuantity((quantityParsed + 1).toFixed(0));
                  });
                }}>
                <View
                  style={[
                    styles.addBtn,
                    {
                      backgroundColor: Colors.primaryColor,
                      left: -1,
                    },
                    switchMerchant
                      ? {
                        width: windowWidth * 0.03,
                        height: windowWidth * 0.03,
                      }
                      : {},
                  ]}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 30,
                      fontWeight: '500',
                      color: Colors.whiteColor,
                    }}>
                    +
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            <View
              style={{
                flex: 1,
                paddingLeft: 30,
              }}>
              <TouchableOpacity
                onPress={addToCartFunction}>
                <View
                  style={[
                    Styles.button,
                    {
                      paddingVertical: 12,
                      width: windowWidth * 0.2,
                      right: 0,
                      alignSelf: 'flex-end',
                    },
                    switchMerchant
                      ? {
                        height: 35,
                      }
                      : {},
                  ]}>
                  <Text
                    style={{
                      color: '#ffffff',
                      fontSize: switchMerchant ? 10 : 18,
                      // fontWeight: "bold",
                      fontFamily: 'NunitoSans-SemiBold',
                      bottom: switchMerchant ? 2 : 0,
                    }}>
                    {isLoading ? 'LOADING...' : onUpdatingCartItem ? 'UPDATE CART' : 'ADD TO CART'}
                  </Text>
                </View>
              </TouchableOpacity>
              {/* <ModalView
              style={{ flex: 1 }}
              visible={visible}
              transparent={true}
              animationType="slide"
            >
              <View
                style={{
                  backgroundColor: "rgba(0,0,0,0.5)",
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <View style={styles.confirmBox}>
                  <TouchableOpacity
                    onPress={() => {
                      setState({ visible: false });
                    }}
                  >
                    <View
                      style={{
                        alignSelf: "flex-start",
                        padding: 16,
                      }}
                    >
                      <Close name="close" size={24} />
                    </View>
                  </TouchableOpacity>
                  <View>
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "bold",
                        fontSize: 14,
                        marginBottom: 5,
                      }}
                    >
                      Confirm order?
                    </Text>
                  </View>
                  <View
                    style={{
                      alignItems: "center",
                      width: "100%",
                      alignContent: "center",
                      marginBottom: "6%",
                      height: "20%",
                      marginTop: "5%",
                    }}
                  >
                    <TouchableOpacity
                      onPress={() => {
                        setState({ optional: 0 });
                        // console.log(optional);
                        addToCart(),
                          props.navigation.navigate("OutletMenu");
                        setState({ visible: false });
                      }}
                      style={{
                        backgroundColor: Colors.whiteColor,
                        borderColor: Colors.primaryColor,
                        borderWidth: 1,
                        width: "70%",
                        justifyContent: "center",
                        alignItems: "center",
                        alignContent: "center",
                        borderRadius: 5,
                        height: "80%",
                        marginBottom: "5%",
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 15,
                          color: Colors.primaryColor,
                        }}
                      >
                        Confirm Order
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        // setState({ optional1: 1 });
                        // // console.log(optional1);
                        // setState({ visible: false });
                        // addToCart1(),
                        //   props.navigation.navigate("OutletMenu");
                        setState({ visible: false });
                      }}
                      style={{
                        backgroundColor: Colors.primaryColor,
                        width: "70%",
                        justifyContent: "center",
                        alignItems: "center",
                        alignContent: "center",
                        borderRadius: 5,
                        height: "80%",
                        marginBottom: "2%",
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 15,
                          color: Colors.whiteColor,
                        }}
                      >
                        CANCEL
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </ModalView> */}

              {
                promotionList
                  ?
                  <ModalView
                    style={{}}
                    visible={promotionList}
                    supportedOrientations={['portrait', 'landscape']}
                    transparent
                    animationType={'fade'}>
                    <View
                      style={{
                        flex: 1,
                        backgroundColor: Colors.modalBgColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <View style={styles.modalContainer}>
                        <View style={[styles.modalView]}>
                          <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => {
                              setPromotionList(false);
                            }}>
                            {switchMerchant ? (
                              <AntDesign
                                name="closecircle"
                                size={15}
                                color={Colors.fieldtTxtColor}
                              />
                            ) : (
                              <AntDesign
                                name="closecircle"
                                size={25}
                                color={Colors.fieldtTxtColor}
                              />
                            )}
                          </TouchableOpacity>
                          <View
                            style={{
                              flexDirection: 'column',
                              paddingTop: switchMerchant
                                ? windowHeight * 0.1
                                : 40,
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <Text
                              style={[
                                switchMerchant
                                  ? {
                                    fontSize: 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    marginBottom: 30,
                                  }
                                  : {
                                    fontSize: 20,
                                    fontFamily: 'NunitoSans-Bold',
                                    marginBottom: 25,
                                  },
                              ]}>
                              Promotion
                            </Text>
                            <View style={{ height: windowHeight * 0.47, width: windowWidth * 0.62 }}>
                              <FlatList
                                showsVerticalScrollIndicator={false}
                                style={{}}
                                data={testdata}
                                renderItem={renderItem}
                                keyExtractor={(item, index) => String(index)}
                                contentContainerStyle={{
                                  paddingHorizontal: 16,
                                  paddingVertical: 4,
                                }}
                              />
                            </View>
                            <View style={{ zIndex: -1000, paddingTop: 20 }}>
                              <TouchableOpacity
                                style={{
                                  marginTop: 20,
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#0F1A3C',
                                  borderRadius: 5,
                                  //width: 100,
                                  paddingHorizontal: 10,
                                  height: 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1000,
                                }}
                                onPress={() => {
                                  setPromotionList(false)
                                }}>
                                <Text
                                  style={{
                                    color: Colors.whiteColor,
                                    //marginLeft: 5,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  Add to cart
                                </Text>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </ModalView>
                  :
                  <></>
              }
            </View>
          </View>

          <View style={{ minHeight: 100 }} />
        </KeyboardAwareScrollView>
        {/* {cartIcon ?
        <Draggable
          shouldReverse={reverse}
          renderSize={100}
          renderColor={Colors.secondaryColor}
          isCircle
          x={280}
          y={500}
          onShortPressRelease={() => {
            goToCart();
          }}
        >
          <View style={{ width: 60, height: 60, justifyContent: "center" }}>
            <View style={{ alignSelf: "center" }}>
              <Icon name="cart-outline" size={45} />
            </View>
            <View style={styles.cartCount}>
              <Text style={{ color: Colors.whiteColor, fontSize: 10 }}>
                {Cart.getCartItem().length}
              </Text>
            </View>
          </View>
        </Draggable>
        : null} */}
      </View>
    </UserIdleWrapper>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    padding: 16,
  },
  containerOutter: {
    flex: 1,
    backgroundColor: '#ffffff',
    position: 'relative',
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  textInput: {
    height: 100,
    paddingHorizontal: 10,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    //borderRadius: 5,
  },
  addBtn: {
    backgroundColor: Colors.primaryColor,
    width: 45,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addBtnSmall: {
    backgroundColor: Colors.primaryColor,
    width: 25,
    height: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBox: {
    width: '70%',
    height: '40%',
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: '100%',
    width: '100%',
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  addBtn1: {
    backgroundColor: Colors.primaryColor,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatCartBtn: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  cartCount: {
    position: 'absolute',
    top: -12,
    right: -10,
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.5,
    width: Dimensions.get('window').width * 0.75,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.04,
    top: Dimensions.get('window').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  card: {
    backgroundColor: 'white',
    marginTop: 20,
    borderWidth: 1,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
});
export default MenuItemDetailsScreen;
